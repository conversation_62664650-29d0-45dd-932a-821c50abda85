<?php

namespace app\common;

use think\facade\Db;

class Alipay
{
    //支付宝小程序支付
    public static function refund($aid, $platform, $ordernum, $totalprice, $refundmoney, $refund_desc = '退款')
    {
        if (!$refund_desc) $refund_desc = '退款';
        $appinfo = \app\common\System::appinfo($aid, $platform);
        if ($platform == 'h5' || $platform == 'app') {
            $appinfo['appid'] = $appinfo['ali_appid'];
            $appinfo['appsecret'] = $appinfo['ali_privatekey'];
            $appinfo['publickey'] = $appinfo['ali_publickey'];
        }
        if (getcustom('plug_more_alipay') && $platform == 'h5') {
            $alipay_log = Db::name('alipay_log')->where('aid', $aid)->where('ordernum', $ordernum)->find();
            if ($alipay_log && $alipay_log['mch_id']) {
                if ($alipay_log['mch_id'] == $appinfo['ali_appid2']) {
                    $appinfo['appid'] = $appinfo['ali_appid2'];
                    $appinfo['appsecret'] = $appinfo['ali_privatekey2'];
                    $appinfo['publickey'] = $appinfo['ali_publickey2'];
                }
                if ($alipay_log['mch_id'] == $appinfo['ali_appid3']) {
                    $appinfo['appid'] = $appinfo['ali_appid3'];
                    $appinfo['appsecret'] = $appinfo['ali_privatekey3'];
                    $appinfo['publickey'] = $appinfo['ali_publickey3'];
                }
            }
        }
        require_once(ROOT_PATH . '/extend/aop/AopClient.php');
        require_once(ROOT_PATH . '/extend/aop/AopCertification.php');
        require_once(ROOT_PATH . '/extend/aop/request/AlipayTradeRefundRequest.php');

        $aop = new \AopClient();
        $aop->gatewayUrl = 'https://openapi.alipay.com/gateway.do';
        $aop->appId = $appinfo['appid'];
        $aop->rsaPrivateKey = $appinfo['appsecret'];
        $aop->alipayrsaPublicKey = $appinfo['publickey'];
        $aop->apiVersion = '1.0';
        $aop->signType = 'RSA2';
        $aop->postCharset = 'utf-8';
        $aop->format = 'json';

        $request = new \AlipayTradeRefundRequest();
        $bizcontent = [];
        $bizcontent['out_trade_no'] = $ordernum;
        $bizcontent['out_request_no'] = $ordernum . '_' . rand(1000, 9999);
        $bizcontent['refund_amount'] = $refundmoney * 100 / 100;
        $bizcontent['refund_reason'] = $refund_desc;
        $request->setBizContent(jsonEncode($bizcontent));
        $result = $aop->execute($request);
        $responseNode = str_replace(".", "_", $request->getApiMethodName()) . "_response";
        $resultCode = $result->$responseNode->code;
        if (!empty($resultCode) && $resultCode == 10000) {
            return ['status' => 1, 'msg' => '退款成功'];
        } else {
            return ['status' => 0, 'msg' => $result->$responseNode->sub_msg];
        }
    }

    //支付宝H5支付

    function build_alipay($aid, $mid, $title, $ordernum, $price, $tablename, $notify_url = '')
    {
        if (!$notify_url) $notify_url = PRE_URL . '/notify.php';
        $appinfo = \app\common\System::appinfo($aid, 'alipay');
        $member = Db::name('member')->where('id', $mid)->find();

        require_once(ROOT_PATH . '/extend/aop/AopClient.php');
        require_once(ROOT_PATH . '/extend/aop/AopCertification.php');
        require_once(ROOT_PATH . '/extend/aop/request/AlipayTradeCreateRequest.php');

        $aop = new \AopClient();
        $aop->gatewayUrl = 'https://openapi.alipay.com/gateway.do';
        $aop->appId = $appinfo['appid'];
        $aop->rsaPrivateKey = $appinfo['appsecret'];
        $aop->alipayrsaPublicKey = $appinfo['publickey'];
        $aop->apiVersion = '1.0';
        $aop->signType = 'RSA2';
        $aop->postCharset = 'utf-8';
        $aop->format = 'json';


        $request = new \AlipayTradeCreateRequest ();
        $bizcontent = [];
        $bizcontent['buyer_id'] = $member['alipayopenid'];
        $bizcontent['subject'] = $title;
        $bizcontent['out_trade_no'] = '' . $ordernum;
        $bizcontent['total_amount'] = $price;
        $bizcontent['passback_params'] = urlencode($aid . ':' . $tablename . ':alipay');

        $request->setBizContent(jsonEncode($bizcontent));

        $request->setNotifyUrl($notify_url);
        $result = $aop->execute($request);

        $responseNode = str_replace(".", "_", $request->getApiMethodName()) . "_response";
        $resultCode = $result->$responseNode->code;
        if (!empty($resultCode) && $resultCode == 10000) {
            return ['status' => 1, 'data' => $result->$responseNode];
        } else {
            return ['status' => 0, 'msg' => $result->$responseNode->sub_msg];
        }
    }

    //支付宝APP支付

    function build_h5($aid, $mid, $title, $ordernum, $price, $tablename, $notify_url = '', $return_url = '', $more = 1)
    {
        if (!$notify_url) $notify_url = PRE_URL . '/notify.php';
        if (!$return_url) $return_url = m_url('pages/my/usercenter', $aid);
        $appinfo = \app\common\System::appinfo($aid, 'h5');
        require_once(ROOT_PATH . '/extend/aop/AopClient.php');
        require_once(ROOT_PATH . '/extend/aop/AopCertification.php');
        require_once(ROOT_PATH . '/extend/aop/request/AlipayTradeWapPayRequest.php');

        $aop = new \AopClient();
        $aop->gatewayUrl = 'https://openapi.alipay.com/gateway.do';
        if ($more == 1) {
            $aop->appId = $appinfo['ali_appid'];
            $aop->rsaPrivateKey = $appinfo['ali_privatekey'];
            $aop->alipayrsaPublicKey = $appinfo['ali_publickey'];
        }
        if ($more == 2) {
            $aop->appId = $appinfo['ali_appid2'];
            $aop->rsaPrivateKey = $appinfo['ali_privatekey2'];
            $aop->alipayrsaPublicKey = $appinfo['ali_publickey2'];
        }
        if ($more == 3) {
            $aop->appId = $appinfo['ali_appid3'];
            $aop->rsaPrivateKey = $appinfo['ali_privatekey3'];
            $aop->alipayrsaPublicKey = $appinfo['ali_publickey3'];
        }
        $aop->apiVersion = '1.0';
        $aop->signType = 'RSA2';
        $aop->postCharset = 'utf-8';
        $aop->format = 'json';

        $request = new \AlipayTradeWapPayRequest ();
        $bizcontent = [];
        $bizcontent['body'] = $title;
        $bizcontent['subject'] = $title;
        $bizcontent['out_trade_no'] = '' . $ordernum;
        $bizcontent['total_amount'] = $price;
        $bizcontent['product_code'] = 'QUICK_WAP_WAY';
        $bizcontent['quit_url'] = $return_url;
        $bizcontent['passback_params'] = urlencode($aid . ':' . $tablename . ':h5:' . $more);
        //echo $notify_url;die;
        $request->setBizContent(jsonEncode($bizcontent));
        $request->setNotifyUrl($notify_url);
        $request->setReturnUrl($return_url);
        $result = $aop->pageExecute($request);
        return ['status' => 1, 'data' => $result];
    }

    //支付宝退款

    function build_app($aid, $mid, $title, $ordernum, $price, $tablename, $notify_url = '')
    {
        if (!$notify_url) $notify_url = PRE_URL . '/notify.php';
        $appinfo = \app\common\System::appinfo($aid, 'app');
        require_once(ROOT_PATH . '/extend/aop/AopClient.php');
        require_once(ROOT_PATH . '/extend/aop/AopCertification.php');
        require_once(ROOT_PATH . '/extend/aop/request/AlipayTradeAppPayRequest.php');

        $aop = new \AopClient();
        $aop->gatewayUrl = 'https://openapi.alipay.com/gateway.do';
        $aop->appId = $appinfo['ali_appid'];
        $aop->rsaPrivateKey = $appinfo['ali_privatekey'];
        $aop->alipayrsaPublicKey = $appinfo['ali_publickey'];
        $aop->apiVersion = '1.0';
        $aop->signType = 'RSA2';
        $aop->postCharset = 'utf-8';
        $aop->format = 'json';

        $request = new \AlipayTradeAppPayRequest();
        $bizcontent = [];
        $bizcontent['body'] = $title;
        $bizcontent['subject'] = $title;
        $bizcontent['out_trade_no'] = '' . $ordernum;
        $bizcontent['total_amount'] = $price;
        $bizcontent['product_code'] = 'QUICK_MSECURITY_PAY';
        $bizcontent['passback_params'] = urlencode($aid . ':' . $tablename . ':app');
        $request->setBizContent(jsonEncode($bizcontent));
        $request->setNotifyUrl($notify_url);
        $result = $aop->sdkExecute($request);
        return ['status' => 1, 'data' => $result];
    }
}