<?php
/*
 * @Author: cclilshy <EMAIL>
 * @Date: 2023-02-27 17:57:39
 * @LastEditors: cclilshy <EMAIL>
 * @Description: My house
 * Copyright (c) 2023 by user email: <EMAIL>, All Rights Reserved.
 */

namespace app\common;

use think\facade\Db;

class Business
{
    //加余额
    public static function addmoney($aid, $bid, $money, $remark)
    {
        if ($money == 0) return;
        $business = Db::name('business')->where('aid', $aid)->where('id', $bid)->find();
        
        if (!$business) return ['status' => 0, 'msg' => '商家不存在'];
        Db::name('business')->where('aid', $aid)->where('id', $bid)->inc('money', $money)->update();

        $data = [];
        $data['aid'] = $aid;
        $data['bid'] = $bid;
        $data['money'] = $money;
        $data['after'] = $business['money'] + $money;
        $data['createtime'] = time();
        $data['remark'] = $remark;
        Db::name('business_moneylog')->insert($data);
        return ['status' => 1, 'msg' => ''];
    }
}