<?php

namespace app\common;

use think\facade\Db;

class Coupon
{
    //发送优惠券
    public static function send($aid, $mid, $cpid)
    {
        $coupon = db('coupon')->where('aid', $aid)->where('id', $cpid)->find();
        if (!$coupon) return ['status' => 0, 'msg' => t('优惠券') . '不存在'];
        if ($coupon['stock'] <= 0) return ['status' => 0, 'msg' => '库存不足'];
        $data = [];
        $data['aid'] = $aid;
        $data['bid'] = $coupon['bid'];
        $data['mid'] = $mid;
        $data['couponid'] = $coupon['id'];
        $data['couponname'] = $coupon['name'];
        $data['money'] = $coupon['money'];
        $data['minprice'] = $coupon['minprice'];
        if ($coupon['yxqtype'] == 1) {
            //固定有效期
            $yxqtime = explode(' ~ ', $coupon['yxqtime']);
            $data['starttime'] = strtotime($yxqtime[0]);
            $data['endtime'] = strtotime($yxqtime[1]);
        } elseif ($coupon['yxqtype'] == 2) {
            //领取后x天有效
            $data['starttime'] = time();
            $data['endtime'] = $data['starttime'] + 86400 * $coupon['yxqdate'];
        } elseif ($coupon['yxqtype'] == 3) {
            //次日起计算有效期
            $data['starttime'] = strtotime(date('Y-m-d')) + 86400;
            $data['endtime'] = $data['starttime'] + 86400 * $coupon['yxqdate'] - 1;
        }
        $data['type'] = $coupon['type'];
        $data['limit_count'] = $coupon['limit_count'];
        $data['limit_perday'] = $coupon['limit_perday'];
        $data['createtime'] = time();
        $data['code'] = random(16);
        $data['hexiaoqr'] = createqrcode(m_url('admin/hexiao/hexiao?type=coupon&co=' . $data['code'], $aid));
        db('coupon_record')->insert($data);
        db('coupon')->where('aid', $aid)->where('id', $coupon['id'])->update(['stock' => Db::raw('stock-1'), 'getnum' => Db::raw('getnum+1')]);
        Wechat::updatemembercard($aid, $mid);
        return ['status' => 1, 'msg' => '发送成功'];
    }

    //支付后送券
    public static function getpaygive($aid, $mid, $type, $money, $orderids = null)
    {
        if (!$money) $money = 0;
        $time = time();
        $whereOr = [];
        if ($type == 'shop' && $orderids) {
            if (!is_array($orderids)) {
                $orderids = [$orderids];
            }
            $proids = db('shop_order_goods')->where('orderid', 'in', $orderids)->column('proid');
            foreach ($proids as $proid) {
                $whereOr[] = "find_in_set('{$proid}',buyproids)";
            }
        }
        $whereOr = implode(' or ', $whereOr);
        if ($whereOr) {
            $whereOr = "buyprogive=1 and ({$whereOr})";
        } else {
            $whereOr = '1=0';
        }
        $couponlist = db('coupon')->where("aid={$aid} and unix_timestamp(starttime)<={$time} and unix_timestamp(endtime)>={$time} and stock>0")->where("(paygive=1 and paygive_minprice<={$money} and paygive_maxprice>={$money} and find_in_set('{$type}',paygive_scene)) or ($whereOr)")->order('sort desc,id desc')->select()->toArray();
        //var_dump(Db::getlastsql());
        foreach ($couponlist as $k => $coupon) {
            $havegetnum = db('coupon_record')->where('aid', $aid)->where('mid', $mid)->where('couponid', $coupon['id'])->count();
            if ($havegetnum >= $coupon['perlimit']) unset($couponlist[$k]);
        }
        if (!$couponlist) return false;
        return $couponlist;
    }
}