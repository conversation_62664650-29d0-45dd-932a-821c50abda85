<?php

namespace app\common;

use think\facade\Db;

class Menu
{
    //获取菜单数据
    public static function getdata($aid = 0, $uid = 0)
    {
        $user = [];
        if ($aid == 0) {
            $platform = ['mp', 'wx', 'alipay', 'baidu', 'toutiao', 'qq', 'h5', 'app'];
        } else {
            $platform = explode(',', Db::name('admin')->where('id', $aid)->value('platform'));
        }
        if ($uid > 0) {
            $user = Db::name('admin_user')->where('id', $uid)->find();
            if ($user['bid'] > 0) {
                $isadmin = false;
                if ($user['auth_type'] == 1) {
                    $user = Db::name('admin_user')->where('aid', $aid)->where('isadmin', '>', 0)->find();
                }
            } else {
                $isadmin = true;
            }
        } else {
            if ($uid == -1) {
                $isadmin = false;
                $user = Db::name('admin_user')->where('aid', $aid)->where('isadmin', '>', 0)->find();
            } else {
                $isadmin = true;
            }
        }
        $menudata = [];
        $system_child = [];
        $system_child[] = ['name' => '系统配置', 'path' => 'Backstage/sysset', 'authdata' => 'Backstage/sysset'];
        $system_child[] = ['name' => '门店管理', 'path' => 'Mendian/index', 'authdata' => 'Mendian/*'];
        $system_child[] = ['name' => '管理员列表', 'path' => 'User/index', 'authdata' => 'User/*'];
        $system_child[] = ['name' => '配送方式', 'path' => 'Freight/index', 'authdata' => 'Freight/*'];

        $system_child[] = ['name' => '小票打印机', 'path' => 'Wifiprint/index', 'authdata' => 'Wifiprint/*'];

        if ($isadmin) {
            $system_child[] = ['name' => '短信设置', 'path' => 'Sms/set', 'authdata' => 'Sms/*'];
        }
        if (!$isadmin && getcustom('plug_businessqr')) {
            $system_child[] = ['name' => '推广码', 'path' => 'PlugBusinessqrPoster/index', 'authdata' => 'PlugBusinessqrPoster/*'];
            $system_child[] = ['name' => '快速支付', 'path' => 'PlugBusinessqrPay/index', 'authdata' => 'PlugBusinessqrPay/*'];
        }
        $system_child[] = ['name' => '操作日志', 'path' => 'Backstage/plog', 'authdata' => 'Backstage/plog'];
        $menudata['system'] = ['name' => '系统', 'fullname' => '系统设置', 'icon' => 'my-icon my-icon-sysset', 'child' => $system_child];

        $jiemian_child = [];
        $jiemian_child[] = ['name' => '页面设计', 'path' => 'DesignerPage/index', 'authdata' => 'DesignerPage/*'];

        if ($isadmin) {
            $jiemian_child[] = ['name' => '底部导航', 'path' => 'DesignerMenu/index', 'authdata' => 'DesignerMenu/*'];
            $jiemian_child[] = ['name' => '内页导航', 'path' => 'DesignerMenu/menu2', 'authdata' => 'DesignerMenu/*'];
        } else {
            $jiemian_child[] = ['name' => '底部导航', 'path' => 'DesignerMenu/menu2', 'authdata' => 'DesignerMenu/*'];
        }
        $jiemian_child[] = ['name' => '分享设置', 'path' => 'DesignerShare/index', 'authdata' => 'DesignerShare/*'];
        $jiemian_child[] = ['name' => '链接地址', 'path' => 'DesignerPage/chooseurl', 'params' => '/type/geturl', 'authdata' => 'DesignerPage/*'];
        $menudata['jiemian'] = ['name' => '设计', 'fullname' => '界面设计', 'icon' => 'my-icon my-icon-sheji', 'child' => $jiemian_child];

        if (getcustom('plug_businessqr') && !$isadmin) {
            unset($menudata['jiemian']);
        }

        if ($isadmin) {
            $pingtai_child = [];
            if (in_array('mp', $platform)) {
                $pingtai_child_mp = [];
                $pingtai_child_mp[] = ['name' => '公众号绑定', 'path' => 'Binding/index', 'authdata' => 'Binding/*'];
                $pingtai_child_mp[] = ['name' => '菜单设置', 'path' => 'Mpmenu/index', 'authdata' => 'Mpmenu/*'];
                $pingtai_child_mp[] = ['name' => '支付设置', 'path' => 'Mppay/set', 'authdata' => 'Mppay/*'];
                $pingtai_child_mp[] = ['name' => '模板消息设置', 'path' => 'Mptmpl/tmplset', 'authdata' => 'Mptmpl/*'];
                $pingtai_child_mp[] = ['name' => '已添加的模板', 'path' => 'Mptmpl/mytmpl', 'authdata' => 'Mptmpl/*'];
                $pingtai_child_mp[] = ['name' => '被关注回复', 'path' => 'Mpkeyword/subscribe', 'authdata' => 'Mpkeyword/*'];
                $pingtai_child_mp[] = ['name' => '关键字回复', 'path' => 'Mpkeyword/index', 'authdata' => 'Mpkeyword/*'];
                $pingtai_child_mp[] = ['name' => '粉丝列表', 'path' => 'Mpfans/fanslist', 'authdata' => 'Mpfans/*'];
                //$pingtai_child_mp[] = ['name'=>'素材管理','path'=>'Mpfans/sourcelist','authdata'=>'Mpfans/*'];
                $pingtai_child_mp[] = ['name' => '模板消息群发', 'path' => 'Mpfans/tmplsend', 'authdata' => 'Mpfans/*'];
                //$pingtai_child_mp[] = ['name'=>'活跃粉丝群发','path'=>'Mpfans/kfmsgsend','authdata'=>'Mpfans/*'];
                $pingtai_child[] = ['name' => '微信公众号', 'child' => $pingtai_child_mp];
                $pingtai_child_mpcard = [];
                $pingtai_child_mpcard[] = ['name' => '领取记录', 'path' => 'Membercard/record', 'authdata' => 'Membercard/record'];
                $pingtai_child_mpcard[] = ['name' => '会员卡/创建', 'path' => 'Membercard/index', 'authdata' => 'Membercard/*'];
                $pingtai_child[] = ['name' => '微信会员卡', 'child' => $pingtai_child_mpcard];
            }
            if (in_array('wx', $platform)) {
                $pingtai_child_wx = [];
                $pingtai_child_wx[] = ['name' => '小程序绑定', 'path' => 'Binding/index', 'authdata' => 'Binding/*'];
                $pingtai_child_wx[] = ['name' => '支付设置', 'path' => 'Wxpay/set', 'authdata' => 'Wxpay/*'];
                $pingtai_child_wx[] = ['name' => '订阅消息设置', 'path' => 'Wxtmpl/tmplset', 'authdata' => 'Wxtmpl/*'];
                $pingtai_child_wx[] = ['name' => '服务类目', 'path' => 'Wxleimu/index', 'authdata' => 'Wxleimu/*'];
                //$pingtai_child_wx[] = ['name'=>'关键字回复','path'=>'Wxkeyword/index','authdata'=>'Wxkeyword/*'];
                $pingtai_child[] = ['name' => '微信小程序', 'child' => $pingtai_child_wx];
            }
            if (in_array('alipay', $platform)) {
                $pingtai_child[] = ['name' => '支付宝小程序', 'path' => 'Binding/alipay', 'authdata' => 'Binding/*'];
            }
            if (in_array('baidu', $platform)) {
                $pingtai_child[] = ['name' => '百度小程序', 'path' => 'Binding/baidu', 'authdata' => 'Binding/*'];
            }
            if (in_array('toutiao', $platform)) {
                $pingtai_child[] = ['name' => '头条小程序', 'path' => 'Binding/toutiao', 'authdata' => 'Binding/*'];
            }
            if (in_array('qq', $platform)) {
                $pingtai_child[] = ['name' => 'QQ小程序', 'path' => 'Binding/qq', 'authdata' => 'Binding/*'];
            }
            if (in_array('h5', $platform)) {
                $pingtai_child[] = ['name' => '手机H5', 'path' => 'Binding/h5', 'authdata' => 'Binding/*'];
            }
            if (in_array('app', $platform)) {
                $pingtai_child[] = ['name' => '手机APP', 'path' => 'Binding/app', 'authdata' => 'Binding/*'];
            }
            $menudata['pingtai'] = ['name' => '平台', 'fullname' => '平台设置', 'icon' => 'my-icon my-icon-pingtai', 'child' => $pingtai_child];
        }
        if ($isadmin) {
            $member_child = [];
            $member_child[] = ['name' => t('会员') . '列表', 'path' => 'Member/index', 'authdata' => 'Member/index,Member/excel,Member/excel,Member/importexcel,Member/getplatform,Member/edit,Member/save,Member/del,Member/getcarddetail,Member/charts'];
            $member_child[] = ['name' => '充值', 'path' => 'Member/recharge', 'authdata' => 'Member/recharge', 'hide' => true];
            $member_child[] = ['name' => '加积分', 'path' => 'Member/addscore', 'authdata' => 'Member/addscore', 'hide' => true];
            $member_child[] = ['name' => '加佣金', 'path' => 'Member/addcommission', 'authdata' => 'Member/addcommission', 'hide' => true];
            $member_child[] = ['name' => '等级及分销', 'path' => 'MemberLevel/index', 'authdata' => 'MemberLevel/*'];
            $member_child[] = ['name' => '升级申请记录', 'path' => 'MemberLevel/applyorder', 'authdata' => 'MemberLevel/*'];
            if (getcustom('plug_sanyang')) {
                $member_child[] = ['name' => '等级分组', 'path' => 'MemberLevelCategory/index', 'authdata' => 'MemberLevelCategory/*'];
            }
            if (getcustom('plug_luckycollage')) {
                $member_child[] = ['name' => '增加开团次数', 'path' => 'Member/addktnum', 'authdata' => 'Member/addktnum', 'hide' => true];
            }
            $member_child[] = ['name' => t('会员') . '关系图', 'path' => 'Member/charts', 'authdata' => 'Member/charts'];
            $member_child[] = ['name' => '分享海报', 'path' => 'MemberPoster/index', 'authdata' => 'MemberPoster/*'];
            if (getcustom('plug_xiongmao')) {
                $member_child[] = ['name' => '导入收货地址', 'path' => 'MemberAddressdr/index', 'authdata' => 'MemberAddressdr/*'];
            }
            if (getcustom('partner_jiaquan')) {
                $member_child[] = ['name' => '股东加权分红', 'path' => 'partner_jiaquan', 'authdata' => 'partner_jiaquan', 'hide' => true];
            }
            if (getcustom('partner_gongxian')) {
                $member_child[] = ['name' => '股东贡献量分红', 'path' => 'partner_gongxian', 'authdata' => 'partner_gongxian', 'hide' => true];
            }
            if (getcustom('teamfenhong_pingji')) {
                $member_child[] = ['name' => '团队分红平级奖', 'path' => 'teamfenhong_pingji', 'authdata' => 'teamfenhong_pingji', 'hide' => true];
            }

            $menudata['member'] = ['name' => t('会员'), 'fullname' => t('会员') . '管理', 'icon' => 'my-icon my-icon-member', 'child' => $member_child];
        }
        $shop_child = [];
        $shop_child[] = ['name' => '商品管理', 'path' => 'ShopProduct/index', 'authdata' => 'ShopProduct/*,ShopCode/*'];
        $shop_child[] = ['name' => '订单管理', 'path' => 'ShopOrder/index', 'authdata' => 'ShopOrder/*'];
        $shop_child[] = ['name' => '退款申请', 'path' => 'ShopRefundOrder/index', 'authdata' => 'ShopRefundOrder/*'];
        $shop_child[] = ['name' => '评价管理', 'path' => 'ShopComment/index', 'authdata' => 'ShopComment/*'];
        if ($isadmin) {
            $shop_child[] = ['name' => '商品分类', 'path' => 'ShopCategory/index', 'authdata' => 'ShopCategory/*'];
            $shop_child[] = ['name' => '商品分组', 'path' => 'ShopGroup/index', 'authdata' => 'ShopGroup/*'];
        } else {
            $shop_child[] = ['name' => '商品分类', 'path' => 'ShopCategory2/index', 'authdata' => 'ShopCategory2/*'];
        }
        $shop_child[] = ['name' => '商品服务', 'path' => 'ShopFuwu/index', 'authdata' => 'ShopFuwu/*'];
        if ($isadmin) {
            $shop_child[] = ['name' => '商品海报', 'path' => 'ShopPoster/index', 'authdata' => 'ShopPoster/*'];
            $shop_child[] = ['name' => '录入订单', 'path' => 'ShopOrderlr/index', 'authdata' => 'ShopOrderlr/*,ShopProduct/chooseproduct,ShopProduct/index,ShopProduct/getproduct,Member/index'];
        }
        $shop_child[] = ['name' => '商品采集', 'path' => 'ShopTaobao/index', 'authdata' => 'ShopTaobao/*'];
        $shop_child[] = ['name' => '销售统计', 'path' => 'ShopOrder/tongji', 'authdata' => 'ShopOrder/*'];
        
        $cashier_child = [];
        $cashier_child[] = ['name' => '收银设置', 'path' => 'Cashier/index', 'authdata' => 'Cashier/*'];
        $cashier_child[] = ['name' => '收银订单', 'path' => 'CashierOrder/index', 'authdata' => 'CashierOrder/*'];
        $cashier_child[] = ['name' => '订单统计', 'path' => 'CashierOrder/tongji', 'authdata' => 'CashierOrder/*'];
        $shop_child[] = ['name'=>'收银台','child'=>$cashier_child];
        
        
        if (getcustom('plug_xiongmao')) {
            $shop_child[] = ['name' => '导入库存', 'path' => 'ShopKucundr/index', 'authdata' => 'ShopKucundr/*'];
        }
        if ($isadmin) {
            $shop_child[] = ['name' => '系统设置', 'path' => 'ShopSet/index', 'authdata' => 'ShopSet/*'];
        }
        $menudata['shop'] = ['name' => '商城', 'fullname' => '商城系统', 'icon' => 'my-icon my-icon-shop', 'child' => $shop_child];
        if ($isadmin) {
            $finance_child = [];
            $finance_child[] = ['name' => '消费明细', 'path' => 'Payorder/index', 'authdata' => 'Payorder/*'];
            $finance_child[] = ['name' => t('余额') . '明细', 'path' => 'Money/moneylog', 'authdata' => 'Money/moneylog,Money/moneylogexcel,Money/moneylogsetst,Money/moneylogdel'];
            $finance_child[] = ['name' => '充值记录', 'path' => 'Money/rechargelog', 'authdata' => 'Money/rechargelog,Money/rechargelogexcel,Money/rechargelogdel'];
            $finance_child[] = ['name' => t('余额') . '提现', 'path' => 'Money/withdrawlog', 'authdata' => 'Money/*'];
            if (getcustom('plug_yuebao')) {
                $finance_child[] = ['name' => t('余额宝') . '明细', 'path' => 'Yuebao/moneylog', 'authdata' => 'Yuebao/moneylog,Yuebao/moneylogexcel,Yuebao/moneylogdel'];
                $finance_child[] = ['name' => t('余额宝') . '提现', 'path' => 'Yuebao/withdrawlog', 'authdata' => 'Yuebao/withdrawlog,Yuebao/withdrawlogexcel,Yuebao/withdrawlogsetst,Yuebao/withdrawlogdel'];
            }
            $finance_child[] = ['name' => t('佣金') . '记录', 'path' => 'Commission/record', 'authdata' => 'Commission/record'];
            $finance_child[] = ['name' => t('佣金') . '明细', 'path' => 'Commission/commissionlog', 'authdata' => 'Commission/commissionlog,Commission/commissionlogexcel,Commission/commissionlogdel'];
            $finance_child[] = ['name' => t('佣金') . '提现', 'path' => 'Commission/withdrawlog', 'authdata' => 'Commission/*'];
            $finance_child[] = ['name' => t('积分') . '明细', 'path' => 'Score/scorelog', 'authdata' => 'Score/*'];
            $finance_child[] = ['name' => '买单记录', 'path' => 'Maidan/index', 'authdata' => 'Maidan/*'];
            $finance_child[] = ['name' => '分红记录', 'path' => 'Commission/fenhonglog', 'authdata' => 'Commission/*'];
            if (getcustom('plug_ttdz')) {
                $finance_child[] = ['name' => '团队分红', 'path' => 'Commission/teamfenhong', 'authdata' => 'Commission/*'];
            }
            if (getcustom('everyday_hongbao')) {
                $finance_child[] = ['name' => '红包明细', 'path' => 'HongbaoEveryday/log', 'authdata' => 'HongbaoEveryday/log,Commission/logExcel,Commission/logDel'];
                $finance_child[] = ['name' => '红包提现', 'path' => 'HongbaoEveryday/withdrawlog', 'authdata' => 'HongbaoEveryday/withdrawLog,HongbaoEveryday/withdrawLogExcel,Money/withdrawLogSetst,HongbaoEveryday/withdrawLogDel'];
            }
        } else {
            $finance_child = [];
            $finance_child[] = ['name' => '余额明细', 'path' => 'BusinessMoney/moneylog', 'authdata' => 'BusinessMoney/*'];
            $finance_child[] = ['name' => '余额提现', 'path' => 'BusinessMoney/withdraw', 'authdata' => 'BusinessMoney/*'];
            $finance_child[] = ['name' => '提现记录', 'path' => 'BusinessMoney/withdrawlog', 'authdata' => 'BusinessMoney/*'];
            $finance_child[] = ['name' => '买单记录', 'path' => 'BusinessMaidan/index', 'authdata' => 'BusinessMaidan/*'];
        }
        $finance_child[] = ['name' => '核销记录', 'path' => 'Hexiao/index', 'authdata' => 'Hexiao/*'];
        $finance_child[] = ['name' => '发票管理', 'path' => 'Invoice/index', 'authdata' => 'Invoice/*'];
        $menudata['finance'] = ['name' => '财务', 'fullname' => '财务管理', 'icon' => 'my-icon my-icon-finance', 'child' => $finance_child];

        $yingxiao_child = [];
        $yingxiao_child[] = ['name' => t('优惠券'), 'path' => 'Coupon/index', 'authdata' => 'Coupon/*,ShopCategory/index,ShopCategory/choosecategory'];
        if ($isadmin) {
            $yingxiao_child[] = ['name' => '注册赠送', 'path' => 'Member/registerGive', 'authdata' => 'Member/registerGive'];
            $yingxiao_child[] = ['name' => '充值赠送', 'path' => 'Money/giveset', 'authdata' => 'Money/giveset'];
            $yingxiao_child[] = ['name' => '购物满减', 'path' => 'Manjian/set', 'authdata' => 'Manjian/set'];
        }
        $yingxiao_child[] = ['name' => '商品促销', 'path' => 'Cuxiao/index', 'authdata' => 'Cuxiao/*'];
        if ($isadmin) {
            $yingxiao_child[] = ['name' => '购物返现', 'path' => 'Cashback/index', 'authdata' => 'Cashback/*'];
        }

        $yingxiao_collage = [];
        $yingxiao_collage[] = ['name' => '商品管理', 'path' => 'CollageProduct/index', 'authdata' => 'CollageProduct/*,CollageCode/*'];
        $yingxiao_collage[] = ['name' => '订单管理', 'path' => 'CollageOrder/index', 'authdata' => 'CollageOrder/*'];
        $yingxiao_collage[] = ['name' => '拼团管理', 'path' => 'CollageTeam/index', 'authdata' => 'CollageTeam/*'];
        $yingxiao_collage[] = ['name' => '评价管理', 'path' => 'CollageComment/index', 'authdata' => 'CollageComment/*'];
        if ($isadmin) {
            $yingxiao_collage[] = ['name' => '商品分类', 'path' => 'CollageCategory/index', 'authdata' => 'CollageCategory/*'];
            $yingxiao_collage[] = ['name' => '分享海报', 'path' => 'CollagePoster/index', 'authdata' => 'CollagePoster/*'];
            $yingxiao_collage[] = ['name' => '系统设置', 'path' => 'CollageSet/index', 'authdata' => 'CollageSet/*'];
        }
        $yingxiao_child[] = ['name' => '多人拼团', 'child' => $yingxiao_collage];

        $yingxiao_kanjia = [];
        $yingxiao_kanjia[] = ['name' => '商品管理', 'path' => 'KanjiaProduct/index', 'authdata' => 'KanjiaProduct/*,KanjiaCode/*'];
        $yingxiao_kanjia[] = ['name' => '订单管理', 'path' => 'KanjiaOrder/index', 'authdata' => 'KanjiaOrder/*'];
        if ($isadmin) {
            $yingxiao_kanjia[] = ['name' => '分享海报', 'path' => 'KanjiaPoster/index', 'authdata' => 'KanjiaPoster/*'];
            $yingxiao_kanjia[] = ['name' => '系统设置', 'path' => 'KanjiaSet/index', 'authdata' => 'KanjiaSet/*'];
        }
        $yingxiao_child[] = ['name' => '砍价活动', 'child' => $yingxiao_kanjia];

        $yingxiao_seckill = [];
        //$yingxiao_seckill[] = ['name'=>'商品设置','path'=>'SeckillProset/index','authdata'=>'SeckillProset/*,ShopProduct/chooseproduct,ShopProduct/getproduct'];
        //$yingxiao_seckill[] = ['name'=>'秒杀列表','path'=>'SeckillList/index','authdata'=>'SeckillList/*'];
        $yingxiao_seckill[] = ['name' => '商品列表', 'path' => 'SeckillProduct/index', 'authdata' => 'SeckillProduct/*,SeckillCode/*'];
        $yingxiao_seckill[] = ['name' => '订单列表', 'path' => 'SeckillOrder/index', 'authdata' => 'SeckillOrder/*'];
        $yingxiao_seckill[] = ['name' => '用户评价', 'path' => 'SeckillComment/index', 'authdata' => 'SeckillComment/*'];
        if ($isadmin) {
            $yingxiao_seckill[] = ['name' => '秒杀设置', 'path' => 'SeckillSet/index', 'authdata' => 'SeckillSet/*'];
        }
        $yingxiao_child[] = ['name' => '整点秒杀', 'child' => $yingxiao_seckill];

        $yingxiao_tuangou = [];
        $yingxiao_tuangou[] = ['name' => '商品管理', 'path' => 'TuangouProduct/index', 'authdata' => 'TuangouProduct/*,TuangouCode/*'];
        $yingxiao_tuangou[] = ['name' => '订单管理', 'path' => 'TuangouOrder/index', 'authdata' => 'TuangouOrder/*'];
        $yingxiao_tuangou[] = ['name' => '评价管理', 'path' => 'TuangouComment/index', 'authdata' => 'TuangouComment/*'];
        $yingxiao_tuangou[] = ['name' => '商品分类', 'path' => 'TuangouCategory/index', 'authdata' => 'TuangouCategory/*'];
        if ($isadmin) {
            $yingxiao_tuangou[] = ['name' => '分享海报', 'path' => 'TuangouPoster/index', 'authdata' => 'TuangouPoster/*'];
            $yingxiao_tuangou[] = ['name' => '系统设置', 'path' => 'TuangouSet/index', 'authdata' => 'TuangouSet/*'];
        }
        $yingxiao_child[] = ['name' => '团购活动', 'child' => $yingxiao_tuangou];

        if ($isadmin) {
            $yingxiao_scoreshop = [];
            $yingxiao_scoreshop[] = ['name' => '商品管理', 'path' => 'ScoreshopProduct/index', 'authdata' => 'ScoreshopProduct/*,ScoreshopCode/*'];
            $yingxiao_scoreshop[] = ['name' => '兑换记录', 'path' => 'ScoreshopOrder/index', 'authdata' => 'ScoreshopOrder/*'];
            $yingxiao_scoreshop[] = ['name' => '商品分类', 'path' => 'ScoreshopCategory/index', 'authdata' => 'ScoreshopCategory/*'];
            $yingxiao_scoreshop[] = ['name' => '分享海报', 'path' => 'ScoreshopPoster/index', 'authdata' => 'ScoreshopPoster/*'];
            $yingxiao_scoreshop[] = ['name' => '系统设置', 'path' => 'ScoreshopSet/index', 'authdata' => 'ScoreshopSet/*'];
            $yingxiao_child[] = ['name' => t('积分') . '兑换', 'child' => $yingxiao_scoreshop];

            //$yingxiao_hongbao = [];
            //$yingxiao_hongbao[] = ['name'=>'活动列表','path'=>'Hongbao/index','authdata'=>'Hongbao/*'];
            //$yingxiao_hongbao[] = ['name'=>'领取记录','path'=>'Hongbao/record','authdata'=>'Hongbao/*'];
            //$yingxiao_child[] = ['name'=>'微信红包','child'=>$yingxiao_hongbao];

        }
        if ($isadmin) {
            $yingxiao_choujiang = [];
            $yingxiao_choujiang[] = ['name' => '活动列表', 'path' => 'Choujiang/index', 'authdata' => 'Choujiang/*'];
            $yingxiao_choujiang[] = ['name' => '抽奖记录', 'path' => 'Choujiang/record', 'authdata' => 'Choujiang/*'];
            $yingxiao_child[] = ['name' => '抽奖活动', 'child' => $yingxiao_choujiang];
        }

        $lucky_collage = [];
        $lucky_collage[] = ['name' => '商品管理', 'path' => 'LuckyCollageProduct/index', 'authdata' => 'LuckyCollageProduct/*,LuckyCollageCode/*'];
        $lucky_collage[] = ['name' => '订单管理', 'path' => 'LuckyCollageOrder/index', 'authdata' => 'LuckyCollageOrder/*'];
        $lucky_collage[] = ['name' => '拼团管理', 'path' => 'LuckyCollageTeam/index', 'authdata' => 'LuckyCollageTeam/*'];
        $lucky_collage[] = ['name' => '评价管理', 'path' => 'LuckyCollageComment/index', 'authdata' => 'LuckyCollageComment/*'];
        if ($isadmin) {
            $lucky_collage[] = ['name' => '商品分类', 'path' => 'LuckyCollageCategory/index', 'authdata' => 'LuckyCollageCategory/*'];
            $lucky_collage[] = ['name' => '分享海报', 'path' => 'LuckyCollagePoster/index', 'authdata' => 'LuckyCollagePoster/*'];
            $lucky_collage[] = ['name' => '机器人管理', 'path' => 'LuckyCollageJiqiren/index', 'authdata' => 'LuckyCollageJiqiren/*'];
            $lucky_collage[] = ['name' => '系统设置', 'path' => 'LuckyCollageSet/index', 'authdata' => 'LuckyCollageSet/*'];
        }
        $yingxiao_child[] = ['name' => '幸运拼团', 'child' => $lucky_collage];

        $short_video = [];
        $short_video[] = ['name' => '分类列表', 'path' => 'ShortvideoCategory/index', 'authdata' => 'ShortvideoCategory/*'];
        $short_video[] = ['name' => '视频列表', 'path' => 'Shortvideo/index', 'authdata' => 'Shortvideo/*'];
        $short_video[] = ['name' => '评论列表', 'path' => 'ShortvideoComment/index', 'authdata' => 'ShortvideoComment/*'];
        $short_video[] = ['name' => '回评列表', 'path' => 'ShortvideoCommentReply/index', 'authdata' => 'ShortvideoCommentReply/*'];
        if ($isadmin) {
            $short_video[] = ['name' => '海报设置', 'path' => 'ShortvideoPoster/index', 'authdata' => 'ShortVideoPoster/*'];
            $short_video[] = ['name' => '系统设置', 'path' => 'ShortvideoSysset/index', 'authdata' => 'ShortVideoSysset/*'];
        }
        $yingxiao_child[] = ['name' => '短视频', 'child' => $short_video];
        if ($isadmin && getcustom('everyday_hongbao')) {
            $hongbao_child[] = ['name' => '活动设置', 'path' => 'HongbaoEveryday/index', 'authdata' => 'HongbaoEveryday/*'];
            $hongbao_child[] = ['name' => '额度记录', 'path' => 'HongbaoEveryday/eduRecord', 'authdata' => 'HongbaoEveryday/*'];
            $hongbao_child[] = ['name' => '红包记录', 'path' => 'HongbaoEveryday/record', 'authdata' => 'HongbaoEveryday/*'];
            $yingxiao_child[] = ['name' => '每日红包', 'child' => $hongbao_child];
        }
        if (getcustom('yx_kouling')) {
            $yingxiao_kouling = [];
            $yingxiao_kouling[] = ['name' => '口令列表', 'path' => 'Kouling/index', 'authdata' => 'Kouling/*'];
            $yingxiao_kouling[] = ['name' => '口令设置', 'path' => 'Kouling/set', 'authdata' => 'Kouling/set'];
            $yingxiao_child[] = ['name' => '口令', 'child' => $yingxiao_kouling, 'authdata' => 'Kouling/*'];
        }
        if (getcustom('yx_riddle')) {
            $yingxiao_child[] = ['name' => '谜语', 'path' => 'Riddle/index', 'authdata' => 'Riddle/*'];
        }
        if (!$isadmin && getcustom('yx_jidian')) {
            $yingxiao_jidian = [];
            $yingxiao_jidian[] = ['name' => '集点记录', 'path' => 'Jidian/record', 'authdata' => 'Jidian/record,Jidian/recordexcel,Jidian/recorddel'];
            $yingxiao_jidian[] = ['name' => '集点设置', 'path' => 'Jidian/set', 'authdata' => 'Jidian/set'];
            $yingxiao_child[] = ['name' => '集点', 'child' => $yingxiao_jidian];
        }

        $menudata['yingxiao'] = ['name' => '营销', 'fullname' => '营销活动', 'icon' => 'my-icon my-icon-yingxiao', 'child' => $yingxiao_child];
        $component_child = [];
        if ($isadmin) {
            $component_business = [];
            $component_business[] = ['name' => '商户列表', 'path' => 'Business/index', 'authdata' => 'Business/*,BusinessFreight/*'];
            $component_business[] = ['name' => '商户分类', 'path' => 'Business/category', 'authdata' => 'Business/*'];
            $component_business[] = ['name' => '商户商品', 'path' => 'ShopProduct/index&showtype=2', 'authdata' => 'ShopProduct/*'];

            if (getcustom('plug_businessqr')) {
                $component_business[] = ['name' => '默认商品', 'path' => 'ShopProduct/index&showtype=21', 'authdata' => 'ShopProduct/*'];
            }
            if (getcustom('rest_product_demo')) {
                $component_business[] = ['name' => '默认菜品', 'path' => 'RestaurantProduct/index&showtype=21', 'authdata' => 'RestaurantProduct/*'];
            }

            $component_business[] = ['name' => '商户订单', 'path' => 'ShopOrder/index&showtype=2', 'authdata' => 'ShopOrder/*'];
            $component_business[] = ['name' => '拼团商品', 'path' => 'CollageProduct/index&showtype=2', 'authdata' => 'CollageProduct/*'];
            $component_business[] = ['name' => '砍价商品', 'path' => 'KanjiaProduct/index&showtype=2', 'authdata' => 'KanjiaProduct/*'];
            $component_business[] = ['name' => '秒杀商品', 'path' => 'SeckillProduct/index&showtype=2', 'authdata' => 'SeckillProduct/*'];
            $component_business[] = ['name' => '团购商品', 'path' => 'TuangouProduct/index&showtype=2', 'authdata' => 'TuangouProduct/*'];
            $component_business[] = ['name' => '服务商品', 'path' => 'YuyueList/index&showtype=2', 'authdata' => 'YuyueList/*'];
            $component_business[] = ['name' => '文章列表', 'path' => 'Article/index&showtype=2', 'authdata' => 'Article/*'];
            $component_business[] = ['name' => '短视频列表', 'path' => 'Shortvideo/index&showtype=2', 'authdata' => 'Shortvideo/*'];
            
            if (getcustom('user_bquanxian')) {
                $component_business[] = ['name' => '自定义表单', 'path' => 'Form/index&showtype=2', 'authdata' => 'Form/index,Form/record,Form/recordexcel,Form/chooseform'];
                $component_business[] = ['name' => '表单编辑', 'path' => 'Member/addcommission', 'authdata' => 'Form/edit,Form/save,Form/del,Form/recordsetst,Form/recorddel', 'hide' => true];
            } else {
                $component_business[] = ['name' => '自定义表单', 'path' => 'Form/index&showtype=2', 'authdata' => 'Form/*'];
            }
            $component_business[] = ['name' => t('余额') . '明细', 'path' => 'BusinessMoney/moneylog', 'authdata' => 'BusinessMoney/moneylog,BusinessMoney/moneylogexcel,BusinessMoney/moneylogsetst,BusinessMoney/moneylogdel'];
            $component_business[] = ['name' => '提现记录', 'path' => 'BusinessMoney/withdrawlog', 'authdata' => 'BusinessMoney/*'];
            $component_business[] = ['name' => '通知公告', 'path' => 'BusinessNotice/index', 'authdata' => 'BusinessNotice/*'];
            $component_business[] = ['name' => '默认导航', 'path' => 'DesignerMenu/business', 'authdata' => 'DesignerMenu/*'];
            if (getcustom('plug_businessqr')) {
                $component_business[] = ['name' => '快速支付', 'path' => 'PlugBusinessqrPay/index', 'authdata' => 'PlugBusinessqrPay/*'];
                $component_business[] = ['name' => '首页导航', 'path' => 'PlugBusinessqrMenu/index', 'authdata' => 'PlugBusinessqrMenu/*'];
            }
            $component_business[] = ['name' => '系统设置', 'path' => 'Business/sysset', 'authdata' => 'Business/sysset'];


            $component_child[] = ['name' => '多商户', 'child' => $component_business];

            if ($uid == 0) {
                $component_business2[] = ['name' => '商品分类', 'path' => 'ShopCategory2/index', 'authdata' => 'ShopCategory2/*', 'hide' => true];
                $component_business2[] = ['name' => '余额明细', 'path' => 'BusinessMoney/moneylog', 'authdata' => 'BusinessMoney/*', 'hide' => true];
                $component_business2[] = ['name' => '余额提现', 'path' => 'BusinessMoney/withdraw', 'authdata' => 'BusinessMoney/*', 'hide' => true];
                $component_business2[] = ['name' => '提现记录', 'path' => 'BusinessMoney/withdrawlog', 'authdata' => 'BusinessMoney/*', 'hide' => true];
                $component_business2[] = ['name' => '买单记录', 'path' => 'BusinessMaidan/index', 'authdata' => 'BusinessMaidan/*', 'hide' => true];

                $component_child[] = ['name' => '商户后台', 'child' => $component_business2, 'hide' => true];

            }
        }
        $component_article = [];
        $component_article[] = ['name' => '文章列表', 'path' => 'Article/index', 'authdata' => 'Article/*'];
        $component_article[] = ['name' => '文章分类', 'path' => 'ArticleCategory/index', 'authdata' => 'ArticleCategory/*'];
        $component_article[] = ['name' => '评论列表', 'path' => 'ArticlePinglun/index', 'authdata' => 'ArticlePinglun/*'];
        $component_article[] = ['name' => '回评列表', 'path' => 'ArticlePlreply/index', 'authdata' => 'ArticlePlreply/*'];
        $component_article[] = ['name' => '系统设置', 'path' => 'ArticleSet/set', 'authdata' => 'ArticleSet/*'];
        $component_child[] = ['name' => '文章管理', 'child' => $component_article];
        if ($isadmin) {
            $component_luntan = [];
            $component_luntan[] = ['name' => '帖子列表', 'path' => 'Luntan/index', 'authdata' => 'Luntan/*'];
            $component_luntan[] = ['name' => '分类管理', 'path' => 'LuntanCategory/index', 'authdata' => 'LuntanCategory/*'];
            $component_luntan[] = ['name' => '评论列表', 'path' => 'LuntanPinglun/index', 'authdata' => 'LuntanPinglun/*'];
            $component_luntan[] = ['name' => '回评列表', 'path' => 'LuntanPlreply/index', 'authdata' => 'LuntanPlreply/*'];
            $component_luntan[] = ['name' => '系统设置', 'path' => 'Luntan/sysset', 'authdata' => 'Luntan/sysset'];
            $component_child[] = ['name' => '用户论坛', 'child' => $component_luntan];
        }
        if ($isadmin) {
            $component_sign = [];
            $component_sign[] = ['name' => '签到记录', 'path' => 'Sign/record', 'authdata' => 'Sign/record,Sign/recordexcel,Sign/recorddel'];
            $component_sign[] = ['name' => '签到设置', 'path' => 'Sign/set', 'authdata' => 'Sign/set'];
            $component_child[] = ['name' => t('积分') . '签到', 'child' => $component_sign];
        }
        //预约服务
        //$yuyue = db('yuyue_set')->field('diyname')->where('aid',aid)->find();
        $component_yuyue = [];
        $component_yuyue[] = ['name' => '服务类型', 'path' => 'Yuyue/index', 'authdata' => 'Yuyue/*'];
        $component_yuyue[] = ['name' => '服务商品', 'path' => 'YuyueList/index', 'authdata' => 'YuyueList/*'];
        $component_yuyue[] = ['name' => '服务订单', 'path' => 'YuyueOrder/index', 'authdata' => 'YuyueOrder/*'];
        $component_yuyue[] = ['name' => '商品服务', 'path' => 'YuyueFuwu/index', 'authdata' => 'YuyueFuwu/*'];
        $component_yuyue[] = ['name' => '商品评价', 'path' => 'YuyueComment/index', 'authdata' => 'YuyueComment/*'];
        if ($isadmin) {
            $component_yuyue[] = ['name' => '海报设置', 'path' => 'YuyuePoster/index', 'authdata' => 'YuyuePoster/*'];
        }
        $component_yuyue[] = ['name' => '人员类型', 'path' => 'YuyueWorkerCategory/index', 'authdata' => 'YuyueWorkerCategory/*'];
        $component_yuyue[] = ['name' => '人员列表', 'path' => 'YuyueWorker/index', 'authdata' => 'YuyueWorker/*'];
        $component_yuyue[] = ['name' => '人员评价', 'path' => 'YuyueWorkerComment/index', 'authdata' => 'YuyueWorkerComment/*'];
        $component_yuyue[] = ['name' => '提成明细', 'path' => 'YuyueMoney/moneylog', 'authdata' => 'YuyueMoney/*'];
        $component_yuyue[] = ['name' => '提现记录', 'path' => 'YuyueMoney/withdrawlog', 'authdata' => 'YuyueMoney/*'];
        $component_yuyue[] = ['name' => '系统设置', 'path' => 'YuyueSet/set', 'authdata' => 'YuyueSet/*'];
        $component_child[] = ['name' => '预约服务', 'child' => $component_yuyue];


        $component_kecheng = [];
        $component_kecheng[] = ['name' => '课程类型', 'path' => 'KechengCategory/index', 'authdata' => 'KechengCategory/*'];
        $component_kecheng[] = ['name' => '课程列表', 'path' => 'KechengList/index', 'authdata' => 'KechengList/*'];
        $component_kecheng[] = ['name' => '课程章节', 'path' => 'KechengChapter/index', 'authdata' => 'KechengChapter/*'];
        $component_kecheng[] = ['name' => '题库管理', 'path' => 'KechengTiku/index', 'authdata' => 'KechengTiku/*'];
        $component_kecheng[] = ['name' => '课程订单', 'path' => 'KechengOrder/index', 'authdata' => 'KechengOrder/*'];
        $component_kecheng[] = ['name' => '学习记录', 'path' => 'KechengStudylog/index', 'authdata' => 'KechengStudylog/*'];
        $component_child[] = ['name' => '知识付费', 'child' => $component_kecheng];


        if ($isadmin) {
            $component_peisong = [];
            $component_peisong[] = ['name' => '配送员列表', 'path' => 'PeisongUser/index', 'authdata' => 'PeisongUser/*'];
            $component_peisong[] = ['name' => '配送单列表', 'path' => 'PeisongOrder/index', 'authdata' => 'PeisongOrder/*'];
            $component_peisong[] = ['name' => '评价列表', 'path' => 'PeisongComment/index', 'authdata' => 'PeisongComment/*'];
            $component_peisong[] = ['name' => '提成明细', 'path' => 'PeisongMoney/moneylog', 'authdata' => 'PeisongMoney/*'];
            $component_peisong[] = ['name' => '提现记录', 'path' => 'PeisongMoney/withdrawlog', 'authdata' => 'Peisong/*'];
            $component_peisong[] = ['name' => '系统设置', 'path' => 'Peisong/set', 'authdata' => 'Peisong/*'];
            $component_peisong[] = ['name' => '码科跑腿对接', 'path' => 'Peisong/makeset', 'authdata' => 'Peisong/*'];
            if (getcustom('express_wx')) {
                $component_peisong[] = ['name' => '即时配送对接', 'path' => 'Peisong/wxset', 'authdata' => 'Peisong/*'];
                $component_peisong[] = ['name' => '即时配送订单', 'path' => 'PeisongOrder/wxOrder', 'authdata' => 'PeisongOrder/*'];
            }

            $component_child[] = ['name' => '同城配送', 'child' => $component_peisong];
            if (in_array('express', getcustom())) {
                $component_express = [];
                $component_express[] = ['name' => '寄件列表', 'path' => 'Express/index', 'authdata' => 'Express/*'];
                $component_express[] = ['name' => '基本设置', 'path' => 'ExpressSet/index', 'authdata' => 'ExpressSet/*'];
                $component_child[] = ['name' => '查寄快递', 'child' => $component_express];
            }
            //if(getcustom('toupiao')){
            $component_express = [];
            $component_express[] = ['name' => '活动列表', 'path' => 'Toupiao/index', 'authdata' => 'Toupiao/*'];
            $component_express[] = ['name' => '选手列表', 'path' => 'Toupiao/joinlist', 'authdata' => 'Toupiao/*'];
            $component_express[] = ['name' => '投票记录', 'path' => 'Toupiao/helplist', 'authdata' => 'Toupiao/*'];
            //$component_express[] = ['name'=>'投票设置','path'=>'Toupiao/set','authdata'=>'Toupiao/*'];
            $component_child[] = ['name' => '投票活动', 'child' => $component_express];
            //}
        }

        if ($isadmin && in_array('workorder', getcustom())) {
            $component_workorder = [];
            $component_workorder[] = ['name' => '工单类型', 'path' => 'WorkorderCategory/index', 'authdata' => 'WorkorderCategory/*'];
            $component_workorder[] = ['name' => '工单流程', 'path' => 'WorkorderLiucheng/index', 'authdata' => 'WorkorderLiucheng/*'];
            $component_child[] = ['name' => '工单管理', 'child' => $component_workorder];
        }

        $component_child[] = ['name' => '自定义表单', 'path' => 'Form/index', 'authdata' => 'Form/*'];

        if ($isadmin) {
            $component_child[] = ['name' => '礼品卡兑换', 'path' => 'Lipin/index', 'authdata' => 'Lipin/*'];
            if (in_array('wx', $platform)) {
                $component_child[] = ['name' => '物流助手', 'path' => 'Miandan/index', 'authdata' => 'Miandan/*'];
                $component_child[] = ['name' => '小程序直播', 'path' => 'Live/index', 'authdata' => 'Live/*'];
                $component_child[] = ['name' => '视频号接入', 'child' => [
                    ['name' => '申请接入', 'path' => 'Wxvideo/apply', 'authdata' => 'Wxvideo/*'],
                    ['name' => '商家信息', 'path' => 'Wxvideo/setinfo', 'authdata' => 'Wxvideo/*'],
                    ['name' => '商品管理', 'path' => 'ShopProduct/index&fromwxvideo=1', 'authdata' => 'ShopProduct/*,ShopCode/*'],
                    ['name' => '订单管理', 'path' => 'ShopOrder/index&fromwxvideo=1', 'authdata' => 'ShopOrder/*'],
                    ['name' => '退款申请', 'path' => 'ShopRefundOrder/index&fromwxvideo=1', 'authdata' => 'ShopRefundOrder/*'],
                    ['name' => '我的类目', 'path' => 'Wxvideo/category', 'authdata' => 'Wxvideo/*'],
                    ['name' => '我的品牌', 'path' => 'Wxvideo/brand', 'authdata' => 'Wxvideo/*'],
                ]];
            }
            //if(in_array('toutiao',$platform)){
            //	$component_child[] = ['name'=>'抖音接入','child'=>[
            //		['name'=>'接入配置','path'=>'DouyinSet/index','authdata'=>'Douyin/*'],
            //['name'=>'商品管理','path'=>'ShopProduct/index&fromdouyin=1','authdata'=>'ShopProduct/*,ShopCode/*'],
            //		['name'=>'商品管理','path'=>'DouyinProduct/index','authdata'=>'DouyinProduct/*'],
            //	]];
            //}
            if (getcustom('mh_link')) {
                $component_child[] = ['name' => '魔盒转链', 'path' => 'MoheLink/index', 'authdata' => 'MoheLink/*'];
            }
        }
        $menudata['component'] = ['name' => '扩展', 'fullname' => '扩展功能', 'icon' => 'my-icon my-icon-kuozhan', 'child' => $component_child];
        if (in_array('restaurant', getcustom())) {
            $menudata['restaurant'] = \app\custom\Restaurant::getmenu($isadmin);
        }
        if ($user && $user['auth_type'] == 0) {
            if ($user['groupid']) {
                $user['auth_data'] = Db::name('admin_user_group')->where('id', $user['groupid'])->value('auth_data');
            }
            $auth_data = json_decode($user['auth_data'], true);
            foreach ($menudata as $k => $v) {
                if ($v['child']) {
                    foreach ($v['child'] as $k1 => $v1) {
                        if (!$v1['authdata'] && $v1['child']) {
                            $path = array();
                            foreach ($v1['child'] as $k2 => $v2) {
                                if (!in_array($v2['path'] . ',' . $v2['authdata'], $auth_data)) {
                                    unset($menudata[$k]['child'][$k1]['child'][$k2]);
                                }
                            }
                            if (count($menudata[$k]['child'][$k1]['child']) == 0) {
                                unset($menudata[$k]['child'][$k1]);
                            }
                        } else {
                            if (!in_array($v1['path'] . ',' . $v1['authdata'], $auth_data)) {
                                unset($menudata[$k]['child'][$k1]);
                            }
                        }
                    }
                    if (count($menudata[$k]['child']) == 0) {
                        unset($menudata[$k]);
                    }
                } else {
                    if (!in_array($v['path'] . ',' . $v['authdata'], $auth_data)) {
                        unset($menudata[$k]);
                    }
                }
            }
        }

        return $menudata;
    }

    //白名单 不校验权限
    public static function blacklist()
    {
        $data = [];
        $data[] = 'Backstage/index';
        $data[] = 'Backstage/welcome';
        $data[] = 'Backstage/setpwd';
        $data[] = 'Backstage/about';
        $data[] = 'Help/*';
        $data[] = 'Upload/*';
        $data[] = 'DesignerPage/chooseurl';
        $data[] = 'Peisong/getpeisonguser';
        $data[] = 'Peisong/peisong';
        $data[] = 'Miandan/addorder';
        $data[] = 'Wxset/*';
        $data[] = 'Notice/*';
        $data[] = 'notice/*';
        return $data;
    }

}