<?php


// +----------------------------------------------------------------------
// | 微支付通知
// +----------------------------------------------------------------------
namespace app\common;

use think\facade\Db;
use think\facade\Log;

class Notify
{
    public $member;
    public $givescore = 0;

    public function index()
    {
        if ($_GET['stripepsy'] == 'true') {
            $this->stripepsy();
            die;
        }
        if ($_POST['taskId']) {
            $this->kuaidi100();
            die;
        }
        if ($_POST['passback_params'] && $_POST['trade_status']) {
            $this->alipay();
            die;
        }
        if ($_POST['returnData'] && $_POST['dealId']) {
            $this->baidupay();
            die;
        }
        //Log::write($_SERVER['QUERY_STRING']);
        if ($_SERVER['QUERY_STRING'] && (strpos($_SERVER['QUERY_STRING'], '%3Ayunpay') > 0 || strpos($_SERVER['QUERY_STRING'], 'usicd%3DWXMP') > 0)) { //云收银
            $this->yunpay();
            die;
        }
        $xml = file_get_contents('php://input');
        //Log::write($xml);
        if ($xml && strpos($xml, '%3Aqmpay') > 0) {
            $this->qmpay();
            die;
        }
        if ($xml && (strpos($xml, ':sxpaymp:') > 0 || strpos($xml, ':sxpaywx:') > 0)) {
            $this->sxpay();
            die;
        }
        if ($xml && (strpos($xml, '"applicationId":') > 0 && strpos($xml, '"taskType":') > 0)) {
            $this->sxaudit();
            die;
        }
        if ($xml && (strpos($xml, ':fbpaymp:') > 0 || strpos($xml, ':fbpaywx:') > 0 || strpos($xml, ':fbpayali:') > 0)) {
            $this->fbpay();
            die;
        }

        $ttpost = json_decode($xml, true);
        if ($ttpost && $ttpost['msg_signature'] && $ttpost['type'] == 'payment') {
            $this->ttpay($ttpost);
            die;
        }
        if ($ttpost && $ttpost['data'] && $ttpost['data']['mch_order_no']) {
            $this->ksherpay($ttpost);
            die;
        }
        if (!$xml) die('fail');
        libxml_disable_entity_loader(true);
        $msg = (array)simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA);
        if (empty($msg)) {
            exit('fail');
        }
        if ($msg['result_code'] != 'SUCCESS' || $msg['return_code'] != 'SUCCESS') {
            exit('fail');
        }
        //Log::write($msg);
        $attach = explode(':', $msg['attach']);
        $aid = intval($attach[0]);
        define('aid', $aid);
        $tablename = $attach[1];
        $platform = $attach[2];
        $appinfo = \app\common\System::appinfo($aid, $platform);
        if (!empty($appinfo)) {
            ksort($msg);
            $string1 = '';
            foreach ($msg as $k => $v) {
                if ($v != '' && $k != 'sign') {
                    $string1 .= "{$k}={$v}&";
                }
            }
            if ($appinfo['wxpay_type'] == 1) {
                $dbwxpayset = Db::name('sysset')->where('name', 'wxpayset')->value('value');
                $dbwxpayset = json_decode($dbwxpayset, true);
                $mchkey = $dbwxpayset['mchkey'];
            } else {
                $mchkey = $appinfo['wxpay_mchkey'];
            }
            if ($attach[3]) {
                $bid = $attach[3];
                $bset = Db::name('business_sysset')->where('aid', $aid)->find();
                $mchkey = $bset['wxfw_mchkey'];
            } else {
                $bid = 0;
            }
            $sign = strtoupper(md5($string1 . "key={$mchkey}"));
            if ($sign == $msg['sign']) {
                if ($bid) {
                    Db::name('payorder')->where(['aid' => $aid, 'type' => $tablename, 'ordernum' => $msg['out_trade_no']])->update(['isbusinesspay' => 1]);
                }
                $rs = $this->setorder($tablename, $msg['out_trade_no'], $msg['transaction_id'], $msg['total_fee'], '微信支付', 2);
                if ($rs['status'] == 1) {
                    if ($bid) {
                        $business = Db::name('business')->where('id', $bid)->find();
                        if ($business['feepercent'] > 0) {
                            $paymoney = $msg['total_fee'] * 0.01;
                            $chouchengmoney = floatval($business['feepercent']) * 0.01 * $paymoney;
                            if ($chouchengmoney > 0.01 && $paymoney * 0.3 >= $chouchengmoney) {
                                $chouchengmoney = intval($chouchengmoney * 100) / 100;
                            } else {
                                $chouchengmoney = 0;
                            }
                        }
                        $sub_mchid = $business['wxpay_submchid'];
                    } else {
                        //服务商分账
                        $chouchengmoney = 0;
                        if ($appinfo['wxpay_type'] == 1) {
                            $paymoney = $msg['total_fee'] * 0.01;
                            $admindata = Db::name('admin')->where('id', aid)->find();
                            if ($admindata['chouchengset'] == 0) { //默认抽成
                                if ($dbwxpayset && $dbwxpayset['chouchengset'] != 0) {
                                    if ($dbwxpayset['chouchengset'] == 1) {
                                        $chouchengmoney = floatval($dbwxpayset['chouchengrate']) * 0.01 * $paymoney;
                                        if ($dbwxpayset['chouchengmin'] && $chouchengmoney < floatval($dbwxpayset['chouchengmin'])) {
                                            $chouchengmoney = floatval($dbwxpayset['chouchengmin']);
                                        }
                                    } else {
                                        $chouchengmoney = floatval($dbwxpayset['chouchengmoney']);
                                    }
                                }
                            } elseif ($admindata['chouchengset'] == 1) { //按比例抽成
                                $chouchengmoney = floatval($admindata['chouchengrate']) * 0.01 * $paymoney;
                                if ($chouchengmoney < floatval($admindata['chouchengmin'])) {
                                    $chouchengmoney = floatval($admindata['chouchengmin']);
                                }
                            } elseif ($admindata['chouchengset'] == 2) { //按固定金额抽成
                                $chouchengmoney = floatval($admindata['chouchengmoney']);
                            }
                            //die;
                            if ($chouchengmoney > 0 && $paymoney * 0.3 >= $chouchengmoney) {
                                $chouchengmoney = intval($chouchengmoney * 100) / 100;
                            } else {
                                $chouchengmoney = 0;
                            }
                        }
                        $sub_mchid = ($appinfo['wxpay_type'] == 1 ? $appinfo['wxpay_sub_mchid'] : '');
                    }

                    //记录
                    $data = array();
                    $data['aid'] = aid;
                    $data['mid'] = mid;
                    $data['openid'] = $msg['openid'];
                    $data['tablename'] = $tablename;
                    $data['givescore'] = $this->givescore;
                    $data['ordernum'] = $msg['out_trade_no'];
                    $data['mch_id'] = $msg['mch_id'];
                    $data['transaction_id'] = $msg['transaction_id'];
                    $data['total_fee'] = $msg['total_fee'] * 0.01;
                    $data['createtime'] = time();
                    $data['fenzhangmoney'] = $chouchengmoney;
                    $data['sub_mchid'] = $sub_mchid;
                    $data['platform'] = $platform;
                    $data['bid'] = $bid;
                    Db::name('wxpay_log')->insert($data);
                    \app\common\Member::uplv(aid, mid);

                }

                exit('success');
            }
        }
    }

    //百度支付

    private function kuaidi100()
    {
        $msg = $_POST;
        $param = json_decode($msg['param'], true);
        //Log::write($param);
        if ($param['data']['orderId']) {
            $data = [];
            $data['courierName'] = $param['data']['courierName'];
            $data['courierMobile'] = $param['data']['courierMobile'];
            $data['kuaidinum'] = ['kuaidinum'];
            $data['status'] = $param['data']['status'];
            Db::name('express_order')->where(['orderId' => $param['data']['orderId']])->update($data);
            return ['status' => 1, 'msg' => ''];
        }
    }

    //支付宝支付

    private function alipay()
    {
        $msg = $_POST;
        $attach = explode(':', urldecode($msg['passback_params']));
        $aid = intval($attach[0]);
        define('aid', $aid);
        $tablename = $attach[1];
        $platform = $attach[2];
        $appinfo = \app\common\System::appinfo($aid, $platform);
        if ($platform == 'alipay') {
            $appinfo['ali_publickey'] = $appinfo['publickey'];
        }
        if ($attach[3] && $attach[3] == 2) {
            $appinfo['ali_publickey'] = $appinfo['ali_publickey2'];
        }
        if ($attach[3] && $attach[3] == 3) {
            $appinfo['ali_publickey'] = $appinfo['ali_publickey3'];
        }

        //Log::write($msg);
        //Log::write($appinfo);
        require_once(ROOT_PATH . '/extend/aop/AopClient.php');
        $aop = new \AopClient();
        $aop->alipayrsaPublicKey = $appinfo['ali_publickey'];
        $result = $aop->rsaCheckV1($msg, $appinfo['ali_publickey'], $msg['sign_type']);
        if ($result) {
            if ($msg['trade_status'] == 'TRADE_FINISHED' || $msg['trade_status'] == 'TRADE_SUCCESS') {
                $rs = $this->setorder($tablename, $msg['out_trade_no'], $msg['trade_no'], $msg['total_amount'] * 100, '支付宝支付', 3);
                if ($rs['status'] == 1) {
                    //记录
                    $data = array();
                    $data['aid'] = aid;
                    $data['mid'] = mid;
                    $data['openid'] = '';
                    $data['tablename'] = $tablename;
                    $data['givescore'] = $this->givescore;
                    $data['ordernum'] = $msg['out_trade_no'];
                    $data['mch_id'] = $msg['app_id'];
                    $data['transaction_id'] = $msg['trade_no'];
                    $data['total_fee'] = $msg['total_amount'];
                    $data['createtime'] = time();
                    Db::name('alipay_log')->insert($data);
                    \app\common\Member::uplv(aid, mid);
                }
                exit('success');
            }
        } else {
            exit('fail');
        }
    }

    //头条支付

    private function setorder($tablename, $out_trade_no, $transaction_id, $total_fee, $paytype, $paytypeid)
    {

        $payorder = Db::name('payorder')->where(['aid' => aid, 'type' => $tablename, 'ordernum' => $out_trade_no])->find();
        if ($payorder['status'] != 0) return ['status' => 0, 'msg' => '订单已支付'];
        if ($payorder['score'] > 0) {
            $rs = \app\common\Member::addscore(aid, $payorder['mid'], -$payorder['score'], '支付订单，订单号：' . $out_trade_no);
            if ($rs['status'] == 0) {
                $order = $payorder;
                $order['totalprice'] = $order['money'];
                $order['paytypeid'] = $paytypeid;
                $rs = \app\common\Order::refund($order, $order['money'], '积分扣除失败退款');
                Log::write($rs);
                return ['status' => 0, 'msg' => '已退款'];
            }
        }
        $rs = \app\model\Payorder::payorder($payorder['id'], $paytype, $paytypeid, $transaction_id);
        if ($rs['status'] == 0) return $rs;
        define('mid', $payorder['mid']);

        $set = Db::name('admin_set')->where('aid', aid)->find();
        //消费送积分
        if ($tablename != 'recharge' && $set['scorein_money'] > 0 && $set['scorein_score'] > 0) {
            $givescore = floor($total_fee * 0.01 / $set['scorein_money']) * $set['scorein_score'];
            \app\common\Member::addscore(aid, mid, $givescore, '消费送' . t('积分'));
        }
        //充值送积分
        if ($tablename == 'recharge' && $set['scorecz_money'] > 0 && $set['scorecz_score'] > 0) {
            $givescore = floor($total_fee * 0.01 / $set['scorecz_money']) * $set['scorecz_score'];
            \app\common\Member::addscore(aid, mid, $givescore, '充值送' . t('积分'));
        }
        $this->givescore = $givescore;
        return ['status' => 1, 'msg' => ''];
    }


    public function stripepsy()
    {
        $payId = input('id');
        $amount = input('amount');
        $orderInfo = Db::name('stripepay_log')
        ->where('stripepay_id', $payId)
        ->where('status', 0)
        ->where('amount', floatval($amount))
        ->find();

        if ($orderInfo) {
            Db::name('stripepay_log')
            ->where('stripepay_id', $payId)
            ->where('status', 0)
            ->where('amount', floatval($amount))
                ->update(['status' => 1]);
            $ddOrderInfo = Db::name('payorder')
            ->where('id', $orderInfo['pay_id'])
                ->where('status', 0)
                ->where('money', floatval($amount))
                ->find();
            //callback function
            // var_dump($ddOrderInfo);
            defined('aid') || define('aid', 2);
            $this->member = Db::name('member')->where('id', $ddOrderInfo['mid'])->find();
            // var_dump($this->member);
            $rs = $this->setorder($ddOrderInfo['type'], $ddOrderInfo['ordernum'], $payId, $amount, 'Stripe Payment', 13);
            if ($rs['status'] == 1) {
                //记录
                // $data = array();
                // $data['aid'] = aid;
                // $data['mid'] = mid;
                // $data['openid'] = '';
                // $data['tablename'] = 'recharge';
                // $data['givescore'] = $this->givescore;
                // $data['ordernum'] = $payId;
                // $data['mch_id'] = '';
                // $data['transaction_id'] = '';
                // $data['total_fee'] = $amount;
                // $data['createtime'] = time();
                // Db::name('toutiaopay_log')->insert($data);
                \app\common\Member::uplv(aid, mid);
                echo json_encode(['code' => 0]);
            } else {
                echo json_encode(['code' => -1]);
            }
        } else {
            echo json_encode(['code' => -1]);
        }
    }
    //云收银

    private function baidupay()
    {
        $msg = $_POST;
        $returnData = json_decode($msg['returnData'], true);
        $attach = explode(':', $returnData['params']);
        $aid = intval($attach[0]);
        define('aid', $aid);
        $tablename = $attach[1];
        $baiduapp = \app\common\System::appinfo($aid, 'baidu');
        $result = \app\common\RSASign::checkSign($msg, $baiduapp['pay_publickey']);
        if ($result) {
            if ($msg['status'] == 2) {
                $rs = $this->setorder($tablename, $msg['tpOrderId'], $msg['orderId'], $msg['payMoney'], '百度支付', 11);
                if ($rs['status'] == 1) {
                    //记录
                    $data = array();
                    $data['aid'] = aid;
                    $data['mid'] = mid;
                    $data['openid'] = '';
                    $data['tablename'] = $tablename;
                    $data['givescore'] = $this->givescore;
                    $data['ordernum'] = $msg['tpOrderId'];
                    $data['mch_id'] = $baiduapp['pay_appid'];
                    $data['transaction_id'] = $msg['orderId'];
                    $data['total_fee'] = $msg['payMoney'] * 0.01;
                    $data['createtime'] = time();
                    $data['userId'] = $msg['userId'];
                    Db::name('baidupay_log')->insert($data);
                    \app\common\Member::uplv(aid, mid);
                }

                $ret = [];
                $ret['errno'] = 0;
                $ret['msg'] = 'success';
                $ret['data'] = ['isConsumed' => 2];
                exit(json_encode($ret));
            }
        } else {
            exit('fail');
        }
    }

    private function yunpay()
    {

        $querystring = urldecode($_SERVER['QUERY_STRING']);
        parse_str($querystring, $querydata);
        //Log::write($querydata);
        if (!$querydata['attach']) {
            $payorder = Db::name('payorder')->where('ordernum', $querydata['orderNum'])->find();
            if ($querydata['busicd'] == 'WXMP') {
                $aid = intval($payorder['aid']);
                $tablename = $payorder['type'];
                $platform = 'wx';
            }
        } else {
            $attach = explode(':', $querydata['attach']);
            $aid = intval($attach[0]);
            $tablename = $attach[1];
            $platform = $attach[2];
        }
        define('aid', $aid);
        $appinfo = \app\common\System::appinfo($aid, $platform);

        ksort($querydata);
        $string1 = '';
        foreach ($querydata as $k => $v) {
            if ($v != '' && $k != 'sign') {
                $string1 .= "{$k}={$v}&";
            }
        }
        $string1 = trim($string1, '&');
        $string1 .= $appinfo['yun_mchkey'];
        $sign = hash("sha256", $string1);
        //Log::write($sign);
        //Log::write($querydata['sign']);
        if ($sign == $querydata['sign']) {
            if ($querydata['respcd'] == '00') {
                Db::name('payorder')->where('aid', aid)->where('ordernum', $querydata['orderNum'])->update(['platform' => $platform]);
                $rs = $this->setorder($tablename, $querydata['orderNum'], $querydata['channelOrderNum'], intval($querydata['txamt']), '在线支付', 22);
                if ($rs['status'] == 1) {
                    //记录
                    $data = array();
                    $data['aid'] = aid;
                    $data['mid'] = mid;
                    $data['openid'] = '';
                    $data['tablename'] = $tablename;
                    $data['givescore'] = $this->givescore;
                    $data['ordernum'] = $querydata['orderNum'];
                    $data['mch_id'] = $appinfo['pay_appid'];
                    $data['transaction_id'] = $querydata['channelOrderNum'];
                    $data['total_fee'] = intval($querydata['txamt']) * 0.01;
                    $data['createtime'] = time();
                    Db::name('wxpay_log')->insert($data);
                    \app\common\Member::uplv(aid, mid);
                }
                exit('success');
            }
        } else {
            exit('fail');
        }
    }

    private function qmpay()
    {
        //Log::write('qmpay---------------------------');
        $querystring = urldecode(file_get_contents('php://input'));
        parse_str($querystring, $querydata);
        //Log::write($querydata);

        $merOrderId = str_replace('11UM', '', $querydata['merOrderId']);

        if (!$querydata['attachedData']) {
            $payorder = Db::name('payorder')->where('ordernum', $merOrderId)->find();
            $aid = intval($payorder['aid']);
            $tablename = $payorder['type'];
            $platform = 'h5';
        } else {
            $attach = explode(':', $querydata['attachedData']);
            $aid = intval($attach[0]);
            $tablename = $attach[1];
            $platform = $attach[2];
        }
        define('aid', $aid);
        $appinfo = \app\common\System::appinfo($aid, $platform);

        ksort($querydata);
        $string1 = '';
        foreach ($querydata as $k => $v) {
            if ($v != '' && $k != 'sign') {
                $string1 .= "{$k}={$v}&";
            }
        }
        $config = include(ROOT_PATH . 'config.php');
        $config = $config['qmpay'];

        $string1 = trim($string1, '&');
        //$string1 .= '47ace12ae3b348fe93ab46cee97c6fde';//$appinfo['yun_mchkey'];
        $string1 .= $config['md5key'];
        $sign = strtoupper(hash("sha256", $string1));
        //$sign = strtoupper(md5($string1));
        //Log::write($string1);
        //Log::write('-----1');
        //Log::write($sign);
        //Log::write('-----2');
        //Log::write($querydata['sign']);
        if ($sign == $querydata['sign']) {
            if ($querydata['status'] == 'TRADE_SUCCESS') {
                Db::name('payorder')->where('aid', aid)->where('ordernum', $merOrderId)->update(['platform' => $platform]);
                $rs = $this->setorder($tablename, $merOrderId, $querydata['targetOrderId'], intval($querydata['totalAmount']), '支付宝支付', 23);
                if ($rs['status'] == 1) {
                    //记录
                    $data = array();
                    $data['aid'] = aid;
                    $data['mid'] = mid;
                    $data['openid'] = '';
                    $data['tablename'] = $tablename;
                    $data['givescore'] = $this->givescore;
                    $data['ordernum'] = $merOrderId;
                    $data['mch_id'] = $appinfo['pay_appid'];
                    $data['transaction_id'] = $querydata['targetOrderId'];
                    $data['total_fee'] = intval($querydata['totalAmount']) * 0.01;
                    $data['createtime'] = time();
                    Db::name('wxpay_log')->insert($data);
                    \app\common\Member::uplv(aid, mid);
                }
                exit('success');
            }
        } else {
            exit('fail');
        }
    }

    private function sxpay()
    {
        $postdata = json_decode(file_get_contents('php://input'), true);
        $attach = explode(':', $postdata['extend']);
        $aid = intval($attach[0]);
        $tablename = $attach[1];
        $platform = $attach[2];
        if ($platform == 'sxpaymp') $platform = 'mp';
        if ($platform == 'sxpaywx') $platform = 'wx';
        $appinfo = \app\common\System::appinfo($aid, $platform);
        $md5sign = $attach[3];
        define('aid', $aid);
        if ($md5sign == md5($tablename . $postdata['ordNo'] . $appinfo['sxpay_mchkey'])) {
            if ($postdata['bizCode'] == '0000') {
                Db::name('payorder')->where('aid', aid)->where('ordernum', $postdata['ordNo'])->update(['platform' => $platform]);
                $rs = $this->setorder($tablename, $postdata['ordNo'], $postdata['transactionId'], intval($postdata['amt'] * 100), '微信支付', 2);
                if ($rs['status'] == 1) {
                    //记录
                    $data = array();
                    $data['aid'] = aid;
                    $data['mid'] = mid;
                    $data['openid'] = $postdata['uuid'];
                    $data['tablename'] = $tablename;
                    $data['givescore'] = $this->givescore;
                    $data['ordernum'] = $postdata['ordNo'];
                    $data['mch_id'] = $postdata['mno'];
                    $data['transaction_id'] = $postdata['transactionId'];
                    $data['total_fee'] = $postdata['amt'];
                    $data['createtime'] = time();
                    Db::name('wxpay_log')->insert($data);
                    \app\common\Member::uplv(aid, mid);
                }
                die('{"code":"success","msg":"成功"}');
            }
        }
    }

    private function sxaudit()
    {
        //\think\facade\Log::write(file_get_contents('php://input'));
        $postdata = json_decode(file_get_contents('php://input'), true);
        \think\facade\Log::write($postdata);
        $updata = [];
        if ($postdata['taskType'] == '01') { //修改单
            $updata['taskStatus_edit'] = $postdata['taskStatus'];
            $updata['suggestion_edit'] = $postdata['suggestion'];
        } else { //入驻单
            $updata['taskStatus'] = $postdata['taskStatus'];
            $updata['suggestion'] = $postdata['suggestion'];
            $repoInfo = $postdata['repoInfo'];
            if ($repoInfo) {
                $submchid = '';
                $zfbmchid = '';
                foreach ($repoInfo as $v) {
                    if ($v['childNoType'] == 'WX') {
                        $submchid = $v['childNo'];
                    }
                    if ($v['childNoType'] == 'ZFB') {
                        $zfbmchid = $v['childNo'];
                    }
                }
                if ($submchid) {
                    $updata['submchid'] = $submchid;
                }
                if ($zfbmchid) {
                    $updata['zfbmchid'] = $zfbmchid;
                }
            }
        }
        if ($postdata['isEspecial'] == '01' || $postdata['isEspecial'] === '00') { //复核单
            $updata['isEspecial'] = $postdata['isEspecial'];
            $updata['suggestion2'] = $postdata['suggestion'];
            $updata['specialMerFlagEndTime'] = $postdata['specialMerFlagEndTime'];
        }
        Db::name('sxpay_income')->where('business_code', $postdata['mno'])->update($updata);
        die('{"code": "success","msg": "成功"}');
    }


    //快递100

    private function fbpay()
    {
        $postdata = json_decode(file_get_contents('php://input'), true);
        $attach = explode(':', $postdata['attach']);
        $aid = intval($attach[0]);
        $tablename = $attach[1];
        $platform = $attach[2];
        if ($platform == 'fbpaymp') $platform = 'mp';
        if ($platform == 'fbpaywx') $platform = 'wx';
        if ($platform == 'fbpayali') $platform = 'alipay';
        $appinfo = \app\common\System::appinfo($aid, $platform);
        $md5sign = $attach[3];
        define('aid', $aid);

        ksort($postdata);
        $string1 = '';
        foreach ($postdata as $k => $v) {
            if ($v != '' && $k != 'sign') {
                $string1 .= "{$k}={$v}&";
            }
        }
        $string1 = trim($string1, '&');
        $string1 .= $appinfo['fbpay_appsecret'];
        $sign = strtoupper(md5($string1));
        if ($sign == $postdata['sign']) {
            if ($postdata['result_code'] == '200') {
                Db::name('payorder')->where('aid', aid)->where('ordernum', $postdata['merchant_order_sn'])->update(['platform' => $platform]);
                $rs = $this->setorder($tablename, $postdata['merchant_order_sn'], $postdata['order_sn'], intval($postdata['fee'] * 100), ($platform == 'alipay' ? '支付宝支付' : '微信支付'), 2);
                if ($rs['status'] == 1) {
                    //记录
                    $data = array();
                    $data['aid'] = aid;
                    $data['mid'] = mid;
                    $data['openid'] = $postdata['user_id'];
                    $data['tablename'] = $tablename;
                    $data['givescore'] = $this->givescore;
                    $data['ordernum'] = $postdata['merchant_order_sn'];
                    $data['mch_id'] = $appinfo['fbpay_appid'];
                    $data['transaction_id'] = $postdata['order_sn'];
                    $data['total_fee'] = $postdata['fee'];
                    $data['createtime'] = time();
                    if ($platform == 'alipay') {
                        Db::name('alipay_log')->insert($data);
                    } else {
                        Db::name('wxpay_log')->insert($data);
                    }
                    \app\common\Member::uplv(aid, mid);
                }
                die('success');
            }
        }
    }

    //随行付审核

    public function ttpay($post)
    {
        //Log::write($post);
        $msg = json_decode($post['msg'], true);
        $extra = json_decode($msg['cp_extra'], true);
        $attach = explode(':', $extra['param']);
        $aid = intval($attach[0]);
        define('aid', $aid);
        $tablename = $attach[1];
        $toutiaoapp = \app\common\System::appinfo($aid, 'toutiao');
        $post['token'] = $toutiaoapp['pay_token'];

        $signdata = [];
        $signdata[] = $toutiaoapp['pay_token'];
        $signdata[] = $post['timestamp'];
        $signdata[] = $post['nonce'];
        $signdata[] = $post['msg'];
        sort($signdata, 2);
        $signstr = implode('', $signdata);
        $sign = sha1($signstr);
        if ($sign == $post['msg_signature']) {
            $rs = $this->setorder($tablename, $msg['cp_orderno'], $post['channel_no'], $extra['total_amount'], '头条小程序支付', 12);
            if ($rs['status'] == 1) {
                //记录
                $data = array();
                $data['aid'] = aid;
                $data['mid'] = mid;
                $data['openid'] = '';
                $data['tablename'] = $tablename;
                $data['givescore'] = $this->givescore;
                $data['ordernum'] = $msg['out_trade_no'];
                $data['mch_id'] = '';
                $data['transaction_id'] = '';
                $data['total_fee'] = $extra['total_amount'];
                $data['createtime'] = time();
                Db::name('toutiaopay_log')->insert($data);
                \app\common\Member::uplv(aid, mid);
            }
            exit(json_encode(['err_no' => 0, 'err_tips' => 'success']));
        } else {
            exit('fail');
        }
    }
    
    public function ksherpay($data_array)
    {
        Log::write($data_array);
        if( array_key_exists("code", $data_array)
            && array_key_exists("sign", $data_array)
            && array_key_exists("data", $data_array)
            && array_key_exists("result", $data_array['data'])
            && $data_array['data']["result"] == "SUCCESS"){
            //3.1验证签名
            require_once(ROOT_PATH . '/app/common/Ksher.php');
            $privatekey=<<<EOD
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************EOD;
            $class = new \KsherPay('mch43655', $privatekey);
            $verify_sign = $class->verify_ksher_sign($data_array['data'], $data_array['sign']);
            Log::write("IN IF function sign :". $verify_sign );
            if( $verify_sign==1 ){
                //更新订单信息 change order status
                $out_trade_no = $data_array['data']['mch_order_no'];

                $rs = $this->setorder('shop', $out_trade_no, $data_array['data']['channel_order_no'], $data_array['data']['total_fee'] * 100, 'ksher-'.$data_array['data']['channel'], 14);
                if ($rs['status'] == 1) {
                    Log::write("change order status"); 
                    //开锁
                    
                }
                echo json_encode(array('result'=>'SUCCESS',"msg"=>'OK'));
            } else {
                Log::write("VERIFY_KSHER_SIGN_FAIL");
                echo json_encode(array('result'=>'Fail',"msg"=>'VERIFY_KSHER_SIGN_FAIL'));
            }
        }
    }

}