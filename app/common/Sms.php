<?php

namespace app\common;

use AlibabaCloud\Client\AlibabaCloud;
use AlibabaCloud\Client\Exception\ClientException;
use AlibabaCloud\Client\Exception\ServerException;
use TencentCloud\Common\Credential;
use TencentCloud\Common\Exception\TencentCloudSDKException;
use TencentCloud\Common\Profile\ClientProfile;
use TencentCloud\Common\Profile\HttpProfile;
use TencentCloud\Sms\V20190711\Models\SendSmsRequest;
use TencentCloud\Sms\V20190711\SmsClient;
use think\facade\Db;

// 导入 SMS 的 client

// 导入要请求接口对应的 Request 类

// 导入可选配置类

class Sms
{
    //发送短信 $templateCode 模板编号 $templateParam 参数 如:['code'=>589887]
    public static function send($aid = 0, $tel, $templateCode, $templateParam = [])
    {
        $smsset = db('admin_set_sms')->where('aid', $aid)->find();
        if ($smsset['status'] != 1) return ['status' => 0, 'msg' => '短信功能未开启'];

        if (!$smsset['accesskey'] || !$smsset['accesssecret']) {
            return ['status' => 0, 'msg' => '短信参数未配置'];
        }
        if ($smsset['smstype'] == 2 && !$smsset['sdkappid']) { //腾讯云短信
            return ['status' => 0, 'msg' => '短信应用AppID未配置'];
        }
        $signName = $smsset['sign_name'];

        if (!$signName) return ['status' => 0, 'msg' => '短信签名未配置'];
        if (strpos($templateCode, 'tmpl_') === 0) {
            if ($smsset[$templateCode . '_st'] != 1) return ['status' => 0, 'msg' => '该短信模板未开启'];
            $templateCode = $smsset[$templateCode];
        }
        if ($smsset['smstype'] == 1) { //阿里云短信
        } elseif ($smsset['smstype'] == 2) { //腾讯云短信
        }

        switch ($smsset['smstype']) {
            case 1:
                $rs = self::alisms($aid, $smsset['accesskey'], $smsset['accesssecret'], $signName, $templateCode, $tel, $templateParam);
                break;
            case 2:
                $rs = self::tencentsms($aid, $smsset['accesskey'], $smsset['accesssecret'], $smsset['sdkappid'], $signName, $templateCode, $tel, $templateParam);
                break;
            case 3:

                $rs = self::huaweisms($tel, ['code' => $templateParam['code']]);
                $data = [];
                $data['aid'] = $aid;
                $data['PhoneNumbers'] = is_array($tel) ? implode(',', $tel) : $tel;
                $data['SignName'] = $signName;
                $data['TemplateCode'] = $templateCode;
                $data['ip'] = request()->ip();
                $data['createtime'] = time();
                $data['createdate'] = date('Ymd');
                $data['biz_id'] = '';
                $data['TemplateParam'] = json_encode($templateParam);
                Db::name('smslog')->insert($data);
                break;
            default:
                $rs = array('status' => 0, 'msg' => '接口类型未配置');
                break;
        }
        return $rs;
    }

    public static function alisms($aid, $accessKey, $accessSecret, $signName, $templateID, $tel, $templateParam)
    {
        $paramdata = ['PhoneNumbers' => $tel, 'SignName' => $signName, 'TemplateCode' => $templateID, 'TemplateParam' => jsonEncode($templateParam)];
        AlibabaCloud::accessKeyClient($accessKey, $accessSecret)->regionId('cn-hangzhou')->asDefaultClient();
        try {
            $result = AlibabaCloud::rpc()->product('Dysmsapi')->scheme('https')->version('2017-05-25')->action('SendSms')->method('POST')->host('dysmsapi.aliyuncs.com')->options(['query' => $paramdata])->request();
            $rs = $result->toArray();
            if ($rs['Code'] == 'OK') {
                $data = [];
                $data['aid'] = $aid;
                $data['PhoneNumbers'] = $tel;
                $data['SignName'] = $signName;
                $data['TemplateCode'] = $templateID;
                $data['ip'] = request()->ip();
                $data['createtime'] = time();
                $data['createdate'] = date('Ymd');
                $data['biz_id'] = $rs['data']['BizId'];
                $data['TemplateParam'] = $paramdata['TemplateParam'];
                Db::name('smslog')->insert($data);
                return ['status' => 1, 'data' => $rs, 'msg' => '操作成功'];
            } else {
                return ['status' => 0, 'data' => $rs, 'msg' => $rs['Message']];
            }
        } catch (ClientException $e) {
            return ['status' => 0, 'msg' => $e->getErrorMessage()];
        } catch (ServerException $e) {
            return ['status' => 0, 'msg' => $e->getErrorMessage()];
        }
    }

    public static function tencentsms($aid, $accessKey, $accessSecret, $sdkAppid, $signName, $templateID, $tel, $templateParam)
    {
        try {
            if (is_array($tel)) {
                $tels = [];
                foreach ($tel as $v) {
                    $tels[] = '+86' . $v;
                }
            } else {
                $tels = ['+86' . $tel];
            }
            $cred = new Credential($accessKey, $accessSecret);
            $httpProfile = new HttpProfile();
            $httpProfile->setReqMethod("GET");  // POST 请求（默认为 POST 请求）
            $httpProfile->setReqTimeout(30);    // 请求超时时间，单位为秒（默认60秒）
            $httpProfile->setEndpoint("sms.tencentcloudapi.com");  // 指定接入地域域名（默认就近接入）
            // 实例化一个 client 选项，可选，无特殊需求时可以跳过
            $clientProfile = new ClientProfile();
            $clientProfile->setSignMethod("TC3-HMAC-SHA256");  // 指定签名算法（默认为 HmacSHA256）
            $clientProfile->setHttpProfile($httpProfile);
            $client = new SmsClient($cred, "ap-shanghai", $clientProfile);
            // 实例化一个 sms 发送短信请求对象，每个接口都会对应一个 request 对象。
            $req = new SendSmsRequest();
            /* 填充请求参数，这里 request 对象的成员变量即对应接口的入参
            * 您可以通过官网接口文档或跳转到 request 对象的定义处查看请求参数的定义
            * 基本类型的设置:
            * 帮助链接：
            * 短信控制台：https://console.cloud.tencent.com/smsv2
            * sms helper：https://cloud.tencent.com/document/product/382/3773 */
            /* 短信应用 ID: 在 [短信控制台] 添加应用后生成的实际 SDKAppID，例如1400006666 */
            $req->SmsSdkAppid = $sdkAppid;
            /* 短信签名内容: 使用 UTF-8 编码，必须填写已审核通过的签名，可登录 [短信控制台] 查看签名信息 */
            $req->Sign = $signName;
            /* 短信码号扩展号: 默认未开通，如需开通请联系 [sms helper] */
            //$req->ExtendCode = "0";
            /* 下发手机号码，采用 e.164 标准，+[国家或地区码][手机号]
            * 例如+8613711112222， 其中前面有一个+号 ，86为国家码，13711112222为手机号，最多不要超过200个手机号*/
            $req->PhoneNumberSet = $tels;
            /* 国际/港澳台短信 senderid: 国内短信填空，默认未开通，如需开通请联系 [sms helper] */
            $req->SenderId = "";
            /* 用户的 session 内容: 可以携带用户侧 ID 等上下文信息，server 会原样返回 */
            $req->SessionContext = "";
            /* 模板 ID: 必须填写已审核通过的模板 ID。可登录 [短信控制台] 查看模板 ID */
            $req->TemplateID = $templateID;
            /* 模板参数: 若无模板参数，则设置为空*/
            if ($templateParam) {
                $req->TemplateParamSet = array_values($templateParam);
            } else {
                $req->TemplateParamSet = [];
            }
            // 通过 client 对象调用 SendSms 方法发起请求。注意请求方法名与请求对象是对应的
            $resp = $client->SendSms($req);
            // 输出 JSON 格式的字符串回包
            if ($resp->SendStatusSet[0]->Code == 'Ok') {
                $data = [];
                $data['aid'] = $aid;
                $data['PhoneNumbers'] = is_array($tel) ? implode(',', $tel) : $tel;
                $data['SignName'] = $signName;
                $data['TemplateCode'] = $templateID;
                $data['ip'] = request()->ip();
                $data['createtime'] = time();
                $data['createdate'] = date('Ymd');
                $data['biz_id'] = '';
                if ($req->TemplateParamSet)
                    $data['TemplateParam'] = json_encode($req->TemplateParamSet);
                Db::name('smslog')->insert($data);
                return ['status' => 1, 'msg' => '发送成功'];
            } else {
                return ['status' => 0, 'msg' => $resp->SendStatusSet[0]->Message];
            }
        } catch (TencentCloudSDKException $e) {
            return ['status' => 0, 'msg' => '发送失败'];
        }
    }

    public static function huaweisms($recipients, $code)
    {
        // file_put_contents(__DIR__ . '/hwverify.log','$response');
        /**
         * @Author: cclilshy
         * @Date:   2023-01-16 10:44:43
         * @Last Modified by:   cclilshy
         * @Last Modified time: 2023-01-18 11:44:02
         */
        $url = 'https://smsapi.ap-southeast-1.myhuaweicloud.com:443/sms/batchSendSms/v1'; //APP接入地址+接口访问URI
        $APP_KEY = '4b1rm6C6pA8g1wDwH049DBayiW7n'; //Application Key
        $APP_SECRET = 'Y5RRXdCBJ29r7ROjawA8g0ruIyWE'; //Application Secret
        $sender = 'smsapp0000000242'; //中国大陆短信签名通道号或全球短信通道号
        $TEMPLATE_ID = 'b0d36076044c4f1bac0acd91850d06a9'; //模板ID
        if (substr($recipients, 0, 2) == '86') {
            $url = 'https://smsapi.ap-southeast-1.myhuaweicloud.com:443/sms/batchSendSms/v1'; //APP接入地址+接口访问URI
            $APP_KEY = 'PxcWkmptfR0vx85vImJ0noOx8pvi'; //Application Key
            $APP_SECRET = 'R9AoaXM2hl6jH0EoEsgf2KGOyMdo'; //Application Secret
            $sender = '8823011746626'; //中国大陆短信签名通道号或全球短信通道号
            $TEMPLATE_ID = 'e6b9fdfc0f2649d79a08f104e81464a3'; //模板ID
        }

        //条件必填,中国大陆短信关注,当templateId指定的模板类型为通用模板时生效且必填,必须是已审核通过的,与模板类型一致的签名名称
        //全球短信不用关注该参数
        $signature = 'SGMall'; //签名名称

        //必填,全局号码格式(包含国家码),示例:+8615123456789,多个号码之间用英文逗号分隔
        $receiver = '+' . $recipients; //短信接收人号码

        //选填,短信状态报告接收地址,推荐使用域名,为空或者不填表示不接收状态报告
        $statusCallback = '';

        /**
         * 选填,使用无变量模板时请赋空值 $TEMPLATE_PARAS = '';
         * 单变量模板示例:模板内容为"您的验证码是${NUM_6}"时,$TEMPLATE_PARAS可填写为'["369751"]'
         * 双变量模板示例:模板内容为"您有${NUM_2}件快递请到${TXT_20}领取"时,$TEMPLATE_PARAS可填写为'["3","人民公园正门"]'
         * 查看更多模板和变量规范:产品介绍>模板和变量规范
         * @var string $TEMPLATE_PARAS
         */
        $TEMPLATE_PARAS = '["' . $code['code'] . '"]';
        //模板变量，此处以单变量验证码短信为例，请客户自行生成6位验证码，并定义为字符串类型，以杜绝首位0丢失的问题（例如：002569变成了2569）。

        //请求Headers
        $headers = [
            'Content-Type: application/x-www-form-urlencoded',
            'Authorization: WSSE realm="SDP",profile="UsernameToken",type="Appkey"',
            'X-WSSE: ' . call_user_func(function () use ($APP_KEY, $APP_SECRET) {
                date_default_timezone_set('Asia/Shanghai');
                $now = date('Y-m-d\TH:i:s\Z'); //Created
                $nonce = uniqid(); //Nonce
                $base64 = base64_encode(hash('sha256', ($nonce . $now . $APP_SECRET))); //PasswordDigest
                return sprintf(
                    "UsernameToken Username=\"%s\",PasswordDigest=\"%s\",Nonce=\"%s\",Created=\"%s\"",
                    $APP_KEY,
                    $base64,
                    $nonce,
                    $now
                );
            })
        ];
        //请求Body
        $data = http_build_query([
            'from' => $sender,
            'to' => $receiver,
            'templateId' => $TEMPLATE_ID,
            'templateParas' => $TEMPLATE_PARAS,
            'statusCallback' => $statusCallback,
            'signature' => $signature //使用中国大陆短信通用模板时,必须填写签名名称
        ]);
        $context_options = [
            'http' => ['method' => 'POST', 'header' => $headers, 'content' => $data, 'ignore_errors' => true],
            'ssl' => ['verify_peer' => false, 'verify_peer_name' => false] //为防止因HTTPS证书认证失败造成API调用失败，需要先忽略证书信任问题
        ];

        $response = file_get_contents($url, false, stream_context_create($context_options));
        file_put_contents(__DIR__.'/hw.log',$response);
        return ['status' => 1, 'msg' => 'success'];
    }
}
