<?php

namespace app\common;

class Ttpay
{
    //头条小程序支付
    public static function build($aid, $mid, $title, $ordernum, $price, $tablename, $notify_url = '')
    {
        if (!$notify_url) $notify_url = PRE_URL . '/notify.php';

        $toutiaoapp = \app\common\System::appinfo($aid, 'toutiao');

        $url = 'https://developer.toutiao.com/api/apps/ecpay/v1/create_order';

        $data = [];
        $data['app_id'] = $toutiaoapp['appid'];
        $data['out_order_no'] = $ordernum;
        $data['total_amount'] = intval($price * 100);
        $data['subject'] = $title;
        $data['body'] = $title;
        $data['valid_time'] = 86400;
        $data['cp_extra'] = json_encode(['param' => $aid . ':' . $tablename, 'total_amount' => $data['total_amount']]);
        $data['notify_url'] = $notify_url;
        $data['sign'] = self::getSign($data, $toutiaoapp['pay_salt']);
        $rs = request_post($url, jsonEncode($data));
        $rs = json_decode($rs, true);
        if ($rs['err_no'] == 0) {
            return ['status' => 1, 'orderInfo' => $rs['data']];
        } else {
            return ['status' => 0, 'msg' => $rs['err_tips']];
        }
    }

    //退款

    protected static function getSign($params, $paysecret)
    {
        unset($params["sign"]);
        unset($params["app_id"]);
        unset($params["thirdparty_id"]);
        $paramArray = [];
        foreach ($params as $param) {
            $paramArray[] = trim($param);
        }
        $paramArray[] = trim($paysecret);
        sort($paramArray, 2);
        $signStr = trim(implode('&', $paramArray));
        return md5($signStr);
    }

    //头条小程序分账

    public static function refund($aid, $ordernum, $totalprice, $refund_money, $reason)
    {
        $toutiaoapp = \app\common\System::appinfo($aid, 'toutiao');
        $url = 'https://developer.toutiao.com/api/apps/ecpay/v1/create_refund';
        $data = [];
        $data['app_id'] = $toutiaoapp['appid'];
        $data['out_order_no'] = $ordernum;
        $data['out_refund_no'] = date('YmdHis');
        $data['refund_amount'] = intval($refund_money * 100);
        $data['reason'] = $reason;
        $data['sign'] = self::getSign($data, $toutiaoapp['pay_salt']);
        $rs = request_post($url, jsonEncode($data));
        $rs = json_decode($rs, true);
        if ($rs['err_no'] == 0) {
            return ['status' => 1, 'msg' => '退款发起成功', 'refund_no' => $rs['refund_no']];
        } else {
            return ['status' => 0, 'msg' => $rs['err_tips']];
        }
    }

    public static function settle($aid, $ordernum, $out_settle_no = '', $settle_desc = '分账')
    {
        //if(!$notify_url) $notify_url = PRE_URL.'/notify.php';

        $out_settle_no = $out_settle_no ? $out_settle_no : date('YmdHis') . rand(100000, 999999);

        $toutiaoapp = \app\common\System::appinfo($aid, 'toutiao');

        $url = 'https://developer.toutiao.com/api/apps/ecpay/v1/settle';

        $data = [];
        $data['app_id'] = $toutiaoapp['appid'];
        $data['out_order_no'] = $ordernum;
        $data['out_settle_no'] = $out_settle_no;
        $data['settle_desc'] = $settle_desc;
        //$data['notify_url'] = $notify_url;
        $data['sign'] = self::getSign($data, $toutiaoapp['pay_salt']);
        $rs = request_post($url, jsonEncode($data));
        $rs = json_decode($rs, true);
        if ($rs['err_no'] == 0) {
            return ['status' => 1, 'orderInfo' => $rs['data']];
        } else {
            return ['status' => 0, 'msg' => $rs['err_tips']];
        }
    }
}