<?php

namespace app\common;

use think\facade\Db;

//use think\facade\Queue;

class Wechat
{
    //获取平台的access_token
    public static function share_package($aid)
    {
        $set = Db::name('admin_set')->where('aid', $aid)->find();
        $appinfo = System::appinfo($aid, 'mp');
        $jsapiTicket = self::jsapi_ticket($aid);
        $appid = $appinfo['appid'];
        if (!$jsapiTicket) return [
            "appId" => '',
            "nonceStr" => '',
            "timestamp" => time(),
            "url" => '',
            "signature" => '',
            "rawString" => '',
        ];
        // 注意 URL 一定要动态获取，不能 hardcode.
        $url = PRE_URL . '/h5/' . $aid . '.html';
        $timestamp = time();
        $nonceStr = random(6);
        // 这里参数的顺序要按照 key 值 ASCII 码升序排序
        $string = "jsapi_ticket=$jsapiTicket&noncestr=$nonceStr&timestamp=$timestamp&url=$url";
        $signature = sha1($string);
        $signPackage = [
            "appId" => $appid,
            "nonceStr" => $nonceStr,
            "timestamp" => $timestamp,
            "url" => $url,
            "signature" => $signature,
            "rawString" => $string
        ];
        return $signPackage;
    }

    //获取平台appid

    public static function jsapi_ticket($aid)
    {
        $appinfo = System::appinfo($aid, 'mp');
        $appid = $appinfo['appid'];
        $tokendata = Db::name('access_token')->where('appid', $appid)->find();
        if ($tokendata['jsapi_ticket'] && $tokendata['ticket_expires_time'] > time()) {
            return $tokendata['jsapi_ticket'];
        } else {
            $access_token = self::access_token($aid, 'mp');
            if (!$access_token) return '';
            $url = 'https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token=' . $access_token . '&type=jsapi';
            $res = json_decode(request_get($url));
            $jsapi_ticket = $res->ticket;
            if ($jsapi_ticket) {
                Db::name('access_token')->where('appid', $appid)->update(['jsapi_ticket' => $jsapi_ticket, 'ticket_expires_time' => time() + 7000]);
                return $jsapi_ticket;
            } else {
                return '';
                //echojson(['status'=>0,'msg'=>self::geterror($res)]);
            }
        }
    }

    //获取access_token $platform mp:公众号 wx:小程序

    public static function access_token($aid, $platform = 'wx', $iscache = true)
    {
        $appinfo = System::appinfo($aid, $platform);
        $appid = $appinfo['appid'];
        $appsecret = $appinfo['appsecret'];
        if (!$appid) return '';
        $tokendata = Db::name('access_token')->where('appid', $appid)->find();
        if ($iscache && $tokendata && $tokendata['access_token'] && $tokendata['expires_time'] > time()) {
            return $tokendata['access_token'];
        } else {
            if ($appinfo['authtype'] == 1) { //授权接入
                //刷新调用凭证
                $url = 'https://api.weixin.qq.com/cgi-bin/component/api_authorizer_token?component_access_token=' . self::component_access_token();
                $data = array();
                $data['component_appid'] = self::component_appid();
                $data['authorizer_appid'] = $appid;
                $data['authorizer_refresh_token'] = $appinfo['refresh_token'];
                $rs = request_post($url, jsonEncode($data));
                $rs = json_decode($rs);
                if ($rs->authorizer_access_token) {
                    $access_token = $rs->authorizer_access_token;
                    Db::name('access_token')->where('appid', $appid)->update(['access_token' => $access_token, 'expires_time' => time() + 7000]);
                    return $access_token;
                } else {
                    //\think\facade\Log::write($rs);
                    //return '';
                    echojson(array('status' => 0, 'msg' => self::geterror($rs)));
                }
            } else { //普通接入
                if (!$appsecret) return '';
                $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$appid}&secret={$appsecret}";
                $res = json_decode(request_get($url));
                $access_token = $res->access_token;
                if ($access_token) {
                    if ($tokendata) {
                        Db::name('access_token')->where('appid', $appid)->update(['access_token' => $access_token, 'expires_time' => time() + 7000]);
                    } else {
                        Db::name('access_token')->insert(['appid' => $appid, 'access_token' => $access_token, 'expires_time' => time() + 7000]);
                    }
                    return $access_token;
                } else {
                    //\think\facade\Log::write($res);
                    //return '';
                    echojson(array('status' => 0, 'msg' => self::geterror($res)));
                }
            }
        }
    }

    public static function component_access_token($aid = 0)
    {
        $componentinfo = Db::name('sysset')->where('name', 'component')->value('value');
        $componentinfo = json_decode($componentinfo, true);
        $component_access_token = cache('component_access_token');
        if (!$component_access_token) {
            $url = 'https://api.weixin.qq.com/cgi-bin/component/api_component_token';
            $component_verify_ticket = cache('component_verify_ticket');
            $data = array();
            $data['component_appid'] = $componentinfo['appid'];
            $data['component_appsecret'] = $componentinfo['appsecret'];
            $data['component_verify_ticket'] = $component_verify_ticket;
            $rs = request_post($url, jsonEncode($data));
            $rs = json_decode($rs);
            if ($rs->component_access_token) {
                cache('component_access_token', $rs->component_access_token, 7000);
                $component_access_token = $rs->component_access_token;
            } else {
                echojson(['status' => 0, 'msg' => self::geterror((array)$rs)]);
                //return false;
            }
        }
        return $component_access_token;
    }

    public static function geterror($rs)
    {
        if (is_object($rs)) $rs = (array)$rs;
        $err = array('-1' => '系统繁忙', '0' => '请求成功', '40001' => '不合法的调用凭证', '40002' => '不合法的凭证类型', '40003' => '不合法的OpenID', '40004' => '不合法的媒体文件类型', '40005' => '不合法的文件类型', '40006' => '不合法的文件大小', '40007' => '不合法的media_id', '40008' => '不合法的消息类型', '40009' => '不合法的图片大小', '40010' => '不合法的语音大小', '40011' => '不合法的视频大小', '40012' => '不合法的缩略图大小', '40013' => '不合法的AppID', '40014' => '不合法的access_token', '40015' => '不合法的菜单类型', '40016' => '不合法的按钮个数', '40017' => '不合法的按钮类型', '40018' => '不合法的按钮名称长度', '40019' => '不合法的按钮KEY长度', '40019' => '不合法的按钮KEY长度', '40020' => '不合法的url长度', '40021' => '不合法的菜单版本号', '40022' => '不合法的子菜单级数', '40023' => '不合法的子菜单按钮个数', '40024' => '不合法的子菜单类型', '40025' => '不合法的子菜单按钮名称长度', '40026' => '不合法的子菜单按钮KEY长度', '40027' => '不合法的子菜单按钮url长度', '40028' => '不合法的自定义菜单使用用户', '40029' => '不合法或已过期的code', '40030' => '不合法的refresh_token', '40031' => '不合法的 openid 列表', '40032' => '不合法的 openid 列表长度', '40033' => '不合法的请求字符', '40035' => '不合法的参数', '40036' => '不合法的template_id长度', '40037' => '不合法的template_id', '40038' => '不合法的请求格式', '40039' => '不合法的url长度', '40048' => '不合法的url域名', '40050' => '不合法的分组 id', '40051' => '分组名字不合法', '40054' => '不合法的子菜单url域名', '40055' => '不合法的菜单url域名', '40056' => '不合法的agentid', '40057' => '不合法的callbackurl或者callbackurl验证失败', '40058' => '不合法的参数', '40059' => '不合法的上报地理位置标志位', '40060' => '删除单篇图文时，指定的 article_idx 不合法', '40063' => '参数为空', '40066' => '不合法的url或部门列表', '40068' => '不合法的标签ID', '40070' => '指定的标签范围结点全部无效', '40071' => '不合法的标签名字', '40072' => '不合法的标签名字长度', '40073' => '不合法的openid', '40074' => 'news消息不支持保密消息类型', '40077' => '不合法的pre_auth_code参数', '40078' => '不合法的auth_code参数', '40080' => '不合法的suite_secret', '40082' => '不合法的suite_token', '40083' => '不合法的suite_id', '40084' => '不合法的permanent_code参数', '40085' => '不合法的的suite_ticket参数', '40086' => '不合法的第三方应用appid', '40088' => 'jobid不存在', '40089' => '批量任务的结果已清理', '40091' => 'secret不合法', '40092' => '导入文件存在不合法的内容', '40093' => '不合法的jsapi_ticket参数', '40094' => '不合法的URL', '40096' => '不合法的外部联系人userid', '40097' => '参数错误', '40098' => '接替成员尚未实名认证', '40099' => '接替成员的外部联系人数量已达上限', '40100' => '此用户的外部联系人已经在转移流程中', '40117' => '分组名字不合法', '40118' => 'media_id 大小不合法', '40119' => 'button 类型错误', '40120' => 'button 类型错误', '40121' => '不合法的 media_id 类型', '40125' => '无效的appsecret', '40132' => '微信号不合法', '40137' => '不支持的图片格式', '40155' => '请勿添加其他公众号的主页链接', '40163' => 'oauth_code已使用', '41001' => '缺失access_token参数', '41002' => '缺失appid参数', '41003' => '缺失refresh_token参数', '41004' => '缺失secret参数', '41005' => '缺少多媒体文件数据', '41006' => '缺失media_id参数', '41007' => '缺失子菜单数据', '41008' => '缺失code参数', '41009' => '缺失openid参数', '41010' => '缺失url参数', '41011' => '缺少agentid参数', '41016' => '缺少title参数', '41017' => '缺少tagid参数', '41019' => '缺少 department 参数', '41021' => '缺少suite_id参数', '41022' => '缺少suite_access_token参数', '41023' => '缺少suite_ticket参数', '41024' => '缺少secret参数', '41025' => '缺少permanent_code参数', '41033' => '缺少 description 参数', '41035' => '缺少外部联系人userid参数', '42001' => 'access_token已过期', '42002' => 'refresh_token超时', '42003' => 'code超时', '42007' => 'pre_auth_code已过期', '42009' => 'suite_access_token已过期', '43001' => '需要使用GET方法请求', '43002' => '需要使用POST方法请求', '43003' => '需要使用HTTPS', '43004' => '需要接收者关注', '43005' => '需要好友关系', '43019' => '需要将接收者从黑名单中移除', '44001' => '多媒体文件为空', '44002' => 'POST 的数据包为空', '44003' => '图文消息内容为空', '44004' => '文本消息内容为空', '44005' => '空白的列表', '45001' => '多媒体文件大小超过限制', '45002' => '消息内容大小超过限制', '45003' => '标题字段超过限制', '45004' => '描述字段超过限制', '45005' => '链接字段超过限制', '45006' => '图片链接字段超过限制', '45007' => '语音播放时间超过限制', '45008' => '图文消息超过限制', '45009' => '接口调用超过限制', '45010' => '创建菜单个数超过限制', '45011' => 'API 调用太频繁，请稍候再试', '45012' => '模板大小超过限制', '45015' => '回复时间超过限制', '45016' => '不能修改默认组', '45017' => '分组名字过长', '45018' => '分组数量超过上限', '45022' => '应用name参数长度不符合系统限制', '45024' => '帐号数量超过上限', '45026' => '已添加的模板数量超过限制', '45032' => '图文消息author参数长度超过限制', '45033' => '接口并发调用超过限制', '45047' => '客服接口下行条数超过上限', '45064' => '创建菜单包含未关联的小程序', '45065' => '相同 clientmsgid 已存在群发记录，返回数据中带有已存在的群发任务的 msgid', '45066' => '相同 clientmsgid 重试速度过快，请间隔1分钟重试', '45067' => 'clientmsgid 长度超过限制', '46001' => '不存在媒体数据', '46002' => '不存在的菜单版本', '46003' => '不存在的菜单数据', '46004' => '指定的用户不存在', '47001' => '解析 JSON/XML 内容错误', '48001' => 'api 功能未授权，请确认公众号已获得该接口，可以在公众平台官网 - 开发者中心页中查看接口权限', '48002' => '粉丝拒收消息（粉丝在公众号选项中，关闭了 “ 接收消息 ” ）', '48003' => '不合法的suite_id', '48004' => 'api 接口被封禁或授权关系无效', '48005' => 'api 禁止删除被自动回复和自定义菜单引用的素材或API接口已废弃', '48006' => 'api 禁止清零调用次数，因为清零次数达到上限', '48008' => '没有该类型消息的发送权限', '50001' => '接口未授权或redirect_url未登记可信域名', '50002' => '用户受限，可能是违规后接口被封禁', '50003' => '应用已禁用', '50005' => '用户未关注公众号', '53010' => '名称格式不合法', '53011' => '名称检测命中频率限制', '53012' => '禁止使用该名称', '53013' => '公众号：名称与已有公众号名称重复;小程序：该名称与已有小程序名称重复', '53014' => '公众号：公众号已有{名称A+}时，需与该帐号相同主体才可申请{名称A};小程序：小程序已有{名称A+}时，需与该帐号相同主体才可申请{名称A}', '53015' => '公众号：该名称与已有小程序名称重复，需与该小程序帐号相同主体才可申请;小程序：该名称与已有公众号名称重复，需与该公众号帐号相同主体才可申请', '53016' => '公众号：该名称与已有多个小程序名称重复，暂不支持申请;小程序：该名称与已有多个公众号名称重复，暂不支持申请', '53017' => '公众号：小程序已有{名称A+}时，需与该帐号相同主体才可申请{名称A};小程序：公众号已有{名称A+}时，需与该帐号相同主体才可申请{名称A}', '53018' => '名称命中微信号', '53019' => '名称在保护期内', '53200' => '本月功能介绍修改次数已用完', '53201' => '功能介绍内容命中黑名单关键字', '53202' => '本月头像修改次数已用完', '53300' => '超出每月次数限制', '53301' => '超出可配置类目总数限制', '53302' => '当前账号主体类型不允许设置此种类目', '53303' => '提交的参数不合法', '53304' => '与已有类目重复', '53305' => '包含未通过IPC校验的类目', '53306' => '修改类目只允许修改类目资质，不允许修改类目ID', '53307' => '只有审核失败的类目允许修改', '53308' => '审核中的类目不允许删除', '60001' => '部门长度不符合限制', '60003' => '部门ID不存在', '60004' => '父部门不存在', '60005' => '部门下存在成员', '60006' => '部门下存在子部门', '60007' => '不允许删除根部门', '60008' => '部门已存在', '60009' => '部门名称含有非法字符', '60010' => '部门存在循环关系', '60011' => '指定的成员/部门/标签参数无权限', '60012' => '不允许删除默认应用', '60020' => '访问ip不在白名单之中', '60028' => '不允许修改第三方应用的主页 URL', '60102' => 'UserID已存在', '60103' => '手机号码不合法', '60104' => '手机号码已存在', '60105' => '邮箱不合法', '60106' => '邮箱已存在', '60107' => '微信号不合法', '60110' => '用户所属部门数量超过限制', '60111' => 'UserID不存在', '60112' => '成员name参数不合法', '60123' => '无效的部门id', '60124' => '无效的父部门id', '60125' => '非法部门名字', '60127' => '缺少department参数', '60129' => '成员手机和邮箱都为空', '61450' => '系统错误 (system error)', '61451' => '参数错误 (invalid parameter)', '61452' => '无效客服账号 (invalid kf_account)', '61453' => '客服帐号已存在 (kf_account exsited)', '61454' => '客服帐号名长度超过限制 ( 仅允许 10 个英文字符，不包括 @ 及 @ 后的公众号的微信号 )(invalid kf_acount length)', '61455' => '客服帐号名包含非法字符 ( 仅允许英文 + 数字 )(illegal character in kf_account)', '61456' => '客服帐号个数超过限制 (10 个客服账号 )(kf_account count exceeded)', '61457' => '无效头像文件类型 (invalid file type)', '61500' => '日期格式错误', '65301' => '不存在此 menuid 对应的个性化菜单', '65302' => '没有相应的用户', '65303' => '没有默认菜单，不能创建个性化菜单', '65304' => 'MatchRule 信息为空', '65305' => '个性化菜单数量受限', '65306' => '不支持个性化菜单的帐号', '65307' => '个性化菜单信息为空', '65308' => '包含没有响应类型的 button', '65309' => '个性化菜单开关处于关闭状态', '65310' => '填写了省份或城市信息，国家信息不能为空', '65311' => '填写了城市信息，省份信息不能为空', '65312' => '不合法的国家信息', '65313' => '不合法的省份信息', '65314' => '不合法的城市信息', '65316' => '该公众号的菜单设置了过多的域名外跳（最多跳转到 3 个域名的链接）', '65317' => '不合法的 URL', '72023' => '发票已被其他公众号锁定', '72024' => '发票状态错误', '72037' => '存在发票不属于该用户', '80001' => '可信域名不正确，或者无ICP备案', '81001' => '部门下的结点数超过限制（3W）', '81002' => '部门最多15层', '81011' => '无权限操作标签', '81013' => 'UserID、部门ID、标签ID全部非法或无权限', '81014' => '标签添加成员，单次添加user或party过多', '82001' => '指定的成员/部门/标签全部无效', '82002' => '不合法的PartyID列表长度', '82003' => '不合法的TagID列表长度', '84014' => '成员票据过期', '84015' => '成员票据无效', '84019' => '缺少templateid参数', '84020' => 'templateid不存在', '84021' => '缺少register_code参数', '84022' => '无效的register_code参数', '84023' => '不允许调用设置通讯录同步完成接口', '84024' => '无注册信息', '84025' => '不符合的state参数', '84052' => '缺少caller参数', '84053' => '缺少callee参数', '84054' => '缺少auth_corpid参数', '84055' => '超过拨打公费电话频率', '84056' => '被拨打用户安装应用时未授权拨打公费电话权限', '84057' => '公费电话余额不足', '84058' => 'caller 呼叫号码不支持', '84059' => '号码非法', '84060' => 'callee 呼叫号码不支持', '84061' => '不存在外部联系人的关系', '84062' => '未开启公费电话应用', '84063' => 'caller不存在', '84064' => 'callee不存在', '84065' => 'caller跟callee电话号码一致', '84066' => '服务商拨打次数超过限制', '84067' => '管理员收到的服务商公费电话个数超过限制', '84071' => '不合法的外部联系人授权码', '84072' => '应用未配置客服', '84073' => '客服userid不在应用配置的客服列表中', '84074' => '没有外部联系人权限', '85001' => '微信号不存在或微信号设置为不可搜索', '85002' => '小程序绑定的体验者数量达到上限', '85002' => '包含不合法的词语', '85003' => '微信号绑定的小程序体验者达到上限', '85004' => '微信号已经绑定', '85004' => '每企业每个月设置的可信域名不可超过20个', '85005' => '可信域名未通过所有权校验', '85006' => '标签格式错误', '85007' => '页面路径错误', '85008' => '类目填写错误', '85009' => '已经有正在审核的版本', '85010' => 'item_list有项目为空', '85011' => '标题填写错误', '85012' => '无效的审核id', '85012' => '无效的审核id', '85013' => '无效的自定义配置', '85014' => '无效的模版编号', '85015' => '该账号不是小程序账号', '85015' => '版本输入错误', '85016' => '域名数量超过限制', '85017' => '没有新增域名，请确认小程序已经添加了域名或该域名是否没有在第三方平台添加', '85018' => '域名没有在第三方平台设置', '85019' => '没有审核版本', '85020' => '审核状态未满足发布', '85023' => '审核列表填写的项目数不在1-5以内', '85026' => '微信号绑定管理员名额达到上限', '85027' => '身份证绑定管理员名额达到上限', '85043' => '模版错误', '85044' => '代码包超过大小限制', '85045' => 'ext_json有不存在的路径', '85046' => 'tabBar中缺少path', '85047' => 'pages字段为空', '85048' => 'ext_json解析失败', '85060' => '无效的taskid', '85061' => '手机号绑定管理员名额达到上限', '85062' => '手机号黑名单', '85063' => '身份证黑名单', '85064' => '找不到模版', '85064' => '找不到模版', '85064' => '找不到草稿', '85064' => '找不到草稿', '85065' => '模版库已满', '85066' => '链接错误', '85068' => '测试链接不是子链接', '85069' => '校验文件失败', '85070' => '链接为黑名单', '85071' => '已添加该链接，请勿重复添加', '85072' => '该链接已被占用', '85073' => '二维码规则已满', '85074' => '小程序未发布, 小程序必须先发布代码才可以发布二维码跳转规则', '85075' => '个人类型小程序无法设置二维码规则', '85076' => '链接没有ICP备案', '85077' => '小程序类目信息失效（类目中含有官方下架的类目，请重新选择类目）', '85079' => '小程序没有线上版本，不能进行灰度', '85080' => '小程序提交的审核未审核通过', '85081' => '无效的发布比例', '85082' => '当前的发布比例需要比之前设置的高', '85083' => '搜索标记位被封禁，无法修改', '85084' => '非法的status值，只能填0或者1', '85085' => '近7天提交审核的小程序数量过多，请耐心等待审核完毕后再次提交', '85086' => '提交代码审核之前需提前上传代码', '85087' => '小程序已使用api navigateToMiniProgram，请声明跳转appid列表后再次提交', '86000' => '不是由第三方代小程序进行调用', '86000' => '不是由第三方代小程序进行调用', '86000' => '不是由第三方代小程序进行调用', '86001' => '不存在第三方的已经提交的代码', '86001' => '不存在第三方的已经提交的代码', '86001' => '不存在第三方的已经提交的代码', '86001' => '参数 chatid 不合法', '86002' => '小程序未初始化完成，请确保已设置小程序昵称、头像、简介、服务类目', '86003' => '参数 chatid 不存在', '86004' => '参数 群名不合法', '86005' => '参数 群主不合法', '86006' => '群成员数过多或过少', '86007' => '不合法的群成员', '86008' => '非法操作非自己创建的群', '86101' => '仅群主才有操作权限', '86201' => '参数 需要chatid', '86202' => '参数 需要群名', '86203' => '参数 需要群主', '86204' => '参数 需要群成员', '86205' => '参数 字符串chatid过长', '86206' => '参数 数字chatid过大', '86207' => '群主不在群成员列表', '86215' => '会话ID已经存在', '86216' => '存在非法会话成员ID', '86217' => '会话发送者不在会话成员列表中', '86220' => '指定的会话参数不合法', '87011' => '现网已经在灰度发布，不能进行版本回退', '87012' => '该版本不能回退，可能的原因：1:无上一个线上版用于回退 2:此版本为已回退版本，不能回退 3:此版本为回退功能上线之前的版本，不能回退', '87013' => '撤回次数达到上限（每天一次，每个月10次）', '89000' => '该公众号 / 小程序 已经绑定了开放平台帐号', '89001' => 'not same contractor，Authorizer与开放平台帐号主体不相同', '89001' => 'not same contractor，Authorizer与开放平台帐号主体不相同', '89002' => 'open not exists，该公众号/小程序未绑定微信开放平台帐号。', '89003' => '该开放平台帐号并非通过api创建，不允许操作', '89003' => '该开放平台帐号并非通过api创建，不允许操作', '89004' => '该开放平台帐号所绑定的公众号/小程序已达上限（100个）', '89019' => '业务域名无更改，无需重复设置', '89020' => '尚未设置小程序业务域名，请先在第三方平台中设置小程序业务域名后在调用本接口', '89021' => '请求保存的域名不是第三方平台中已设置的小程序业务域名或子域名', '89029' => '业务域名数量超过限制', '89231' => '个人小程序不支持调用setwebviewdomain 接口', '89236' => '该插件不能申请', '89237' => '已经添加该插件', '89238' => '申请或使用的插件已经达到上限', '89239' => '该插件不存在', '89256' => 'token信息有误', '89257' => '该插件版本不支持快速更新', '89258' => '当前小程序帐号存在灰度发布中的版本，不可操作快速更新', '90001' => '未认证摇一摇周边', '90002' => '缺少摇一摇周边ticket参数', '90003' => '摇一摇周边ticket参数不合法', '90100' => '非法的对外属性类型', '90101' => '对外属性：文本类型长度不合法', '90102' => '对外属性：网页类型标题长度不合法', '90103' => '对外属性：网页url不合法', '90104' => '对外属性：小程序类型标题长度不合法', '90105' => '对外属性：小程序类型pagepath不合法', '90106' => '对外属性：请求参数不合法', '91001' => '不是公众号快速创建的小程序', '91002' => '小程序发布后不可改名', '91003' => '改名状态不合法', '91004' => '昵称不合法', '91005' => '昵称命中主体保护', '91006' => '昵称命中微信号', '91007' => '昵称已被占用', '91008' => '昵称命中7天侵权保护期', '91009' => '需要提交材料', '91010' => '其他错误', '91011' => '查不到昵称修改审核单信息', '91012' => '其它错误', '91040' => '获取ticket的类型无效', '301002' => '无权限操作指定的应用', '301005' => '不允许删除创建者', '301012' => '参数 position 不合法', '301013' => '参数 telephone 不合法', '301014' => '参数 english_name 不合法', '301015' => '参数 mediaid 不合法', '301016' => '上传语音文件不符合系统要求', '301017' => '上传语音文件仅支持AMR格式', '301021' => '参数 userid 无效', '301022' => '获取打卡数据失败', '301023' => 'useridlist非法或超过限额', '301024' => '获取打卡记录时间间隔超限', '301036' => '不允许更新该用户的userid', '302003' => '批量导入任务的文件中userid有重复', '302004' => '组织架构不合法（1不是一棵树，2 多个一样的partyid，3 partyid空，4 partyid name 空，5 同一个父节点下有两个子节点 部门名字一样 可能是以上情况，请一一排查）', '302005' => '批量导入系统失败，请重新尝试导入', '302006' => '批量导入任务的文件中partyid有重复', '302007' => '批量导入任务的文件中，同一个部门下有两个子部门名字一样', '600001' => '不合法的sn', '600002' => '设备已注册', '600003' => '不合法的硬件activecode', '600004' => '该硬件尚未授权任何企业', '600005' => '硬件Secret无效', '600007' => '缺少硬件sn', '600008' => '缺少nonce参数', '600009' => '缺少timestamp参数', '600010' => '缺少signature参数', '600011' => '签名校验失败', '600012' => '长连接已经注册过设备', '600013' => '缺少activecode参数', '600014' => '设备未网络注册', '600015' => '缺少secret参数', '600016' => '设备未激活', '600018' => '无效的起始结束时间', '600020' => '设备未登录', '600021' => '设备sn已存在', '600023' => '时间戳已失效', '600024' => '固件大小超过5M', '600025' => '固件名为空或者超过20字节', '600026' => '固件信息不存在', '600027' => '非法的固件参数', '600028' => '固件版本已存在', '600029' => '非法的固件版本', '600030' => '缺少固件版本参数', '600031' => '硬件固件不允许升级', '600032' => '无法解析硬件二维码', '2000002' => 'CorpId参数无效', '9001001' => 'POST 数据参数不合法', '9001002' => '远端服务不可用', '9001003' => 'Ticket 不合法', '9001004' => '获取摇周边用户信息失败', '9001005' => '获取商户信息失败', '9001006' => '获取 OpenID 失败', '9001007' => '上传文件缺失', '9001008' => '上传素材的文件类型不合法', '9001009' => '上传素材的文件尺寸不合法', '9001010' => '上传失败', '9001020' => '帐号不合法', '9001021' => '已有设备激活率低于 50% ，不能新增设备', '9001022' => '设备申请数不合法，必须为大于 0 的数字', '9001023' => '已存在审核中的设备 ID 申请', '9001024' => '一次查询设备 ID 数量不能超过 50', '9001025' => '设备 ID 不合法', '9001026' => '页面 ID 不合法', '9001027' => '页面参数不合法', '9001028' => '一次删除页面 ID 数量不能超过 10', '9001029' => '页面已应用在设备中，请先解除应用关系再删除', '9001030' => '一次查询页面 ID 数量不能超过 50', '9001031' => '时间区间不合法', '9001032' => '保存设备与页面的绑定关系参数错误', '9001033' => '门店 ID 不合法', '9001034' => '设备备注信息过长', '9001035' => '设备申请参数不合法', '9001036' => '查询起始值 begin 不合法',
            '89007' => '小程序本月被关联的名额已用完', '89008' => '小程序为海外帐号，不允许关联', '89009' => '小程序关联达到上限', '89010' => '已经发送关联邀请', '89011' => '在附近中展示的小程序不能取消关联', '89012' => '门店、小店小程序不能取消关联', '89013' => '小程序被封禁', '89015' => '已经关联该小程序', '89016' => '公众号本月关联相同主体达到上限', '89017' => '公众号本月关联不同主体达到上限', '89035' => '已经从公众平台后台发起关联申请，处于小程序管理员确认中，无法从第三方重复发起关联申请', '88000' => '公众号没有开通留言功能', '41030' => '小程序页面尚未发布,无法生成', '61051' => '公众号主体类型不允许快速创建', '61052' => '公众号未认证', '61053' => '超过主体可注册数量上限', '61054' => '主体黑名单', '61055' => '超出公众号每月可快速创建限额', '61056' => '政府、媒体、其他组织必须复选微信认证', '61057' => '公众号仍有快速创建的账号在流程中', '61058' => '用户扫码凭证校验不通过', '61028' => '第三方平台未发布', '61029' => '第三方平台缺少必备权限集(帐号服务权限、程序帐号管理权限、小程序开发管理与数据分析权限)', '61060' => '转 uri 不合法', '61061' => '海外帐号不允许快速创建', '89249' => '该主体已有任务执行中，距上次任务24h后再试', '89247' => '内部错误', '86004' => '无效微信号', '61070' => '企业代码类型无效，请选择正确类型填写', '89250' => '未找到该任务', '89251' => '待法人人脸核身校验', '89252' => '法人&企业信息一致性校验中	', '89253' => '缺少参数', '89254' => '第三方权限集不全，补全权限集全网发布后生效', '89255' => '企业代码不正确', '92000' => '该经营资质已添加，请勿重复添加', '92002' => '附近地点添加数量达到上线，无法继续添加', '92003' => '地点已被其它小程序占用', '92004' => '附近功能被封禁', '92005' => '地点正在审核中', '92006' => '地点正在展示小程序', '92007' => '地点审核失败', '92008' => '程序未展示在该地点', '93009' => '小程序未上架或不可见', '93010' => '地点不存在', '93011' => '个人类型小程序不可用', '93012' => '非普通类型小程序（门店小程序、小店小程序等）不可用', '93013' => '从腾讯地图获取地址详细信息失败', '93014' => '同一资质证件号重复添加', '61007' => '无接口权限或您授权了多个平台,请前往公众平台登录小程序账号,在[设置-第三方设置]中取消所有授权,然后重新授权', '1003' => '商品id不存在', '300001' => '禁止创建/更新商品 或 禁止编辑&更新房间', '300002' => '名称长度不符合规则', '300003' => '价格输入不合规（如：现价比原价大、传入价格非数字等）', '300004' => '商品名称存在违规违法内容', '300005' => '商品图片存在违规违法内容', '300006' => '图片上传失败（如：mediaID过期）', '300007' => '线上小程序版本不存在该链接', '300008：添加商品失败', '300009：商品审核撤回失败', '300010：商品审核状态不对（如：商品审核中）', '300011：操作非法（API不允许操作非API创建的商品）', '300012' => '没有提审额度（每天500次提审额度）', '300013' => '提审失败', '300014' => '审核中，无法删除（非零代表失败）', '300017' => '商品未提审', '300018' => '商品图片尺寸过大', '300021' => '商品添加成功，审核失败', '300022' => '此房间号不存在', '300023' => '房间状态 拦截（当前房间状态不允许此操作）', '300024' => '商品不存在', '300025' => '商品审核未通过', '300026' => '房间商品数量已经满额', '300027' => '导入商品失败', '300028' => '房间名称违规', '300029' => '主播昵称违规', '300030' => '主播微信号不合法', '300031' => '直播间封面图不合规', '300032' => '直播间分享图违规', '300033' => '添加商品超过直播间上限', '300034' => '主播微信昵称长度不符合要求', '300035' => '主播微信号不存在', '300036' => '主播微信号未实名认证', '300037' => '购物直播频道封面图不合规', '300038' => '未在小程序管理后台配置客服', '300039' => '主播副号微信号不合法', '300040' => '名称含有非限定字符（含有特殊字符）', '300041' => '创建者微信号不合法', '9410000' => '直播间列表为空', '9410001' => '获取房间失败', '9410002' => '获取商品失败', '9410003' => '获取回放失败', '400001' => '微信号不合规', '400002' => '微信号需要实名认证，仅设置主播角色时可能出现', '400003' => '添加角色达到上限（管理员10个，运营者500个，主播500个）', '400004' => '重复添加角色', '400005' => '主播角色删除失败，该主播存在未开播的直播间',
        );
        if (is_array($rs)) {
            return $err[$rs['errcode']] ? $err[$rs['errcode']] : $rs['errcode'] . ': ' . $rs['errmsg'];
        } else {
            return $err[$rs] ? $err[$rs] : $rs;
        }
    }

    //获取卡券js接口用的 card_ticket

    public static function component_appid()
    {
        $componentinfo = Db::name('sysset')->where('name', 'component')->value('value');
        $componentinfo = json_decode($componentinfo, true);
        $component_appid = $componentinfo['appid'];
        return $component_appid;
    }

    //上传到微信素材库

    public static function card_ticket($aid)
    {
        $appinfo = System::appinfo($aid, 'mp');
        $appid = $appinfo['appid'];
        $tokendata = Db::name('access_token')->where('appid', $appid)->find();
        if ($tokendata['card_ticket'] && $tokendata['card_ticket_expires_time'] > time()) {
            return $tokendata['card_ticket'];
        } else {
            $url = "https://api.weixin.qq.com/cgi-bin/ticket/getticket?type=wx_card&access_token=" . self::access_token($aid, 'mp');
            $res = json_decode(request_get($url));
            $jcard_ticket = $res->ticket;
            if ($jcard_ticket) {
                Db::name('access_token')->where('appid', $appid)->update(['card_ticket' => $jcard_ticket, 'card_ticket_expires_time' => time() + 7000]);
                return $jcard_ticket;
            } else {
                return '';
                //if(request()->domain() != 'http://wxtt.com')
                //echojson(['status'=>0,'msg'=>self::geterror($res)]);
            }
        }
    }

    public static function getmediaid($aid, $picurl, $type = "image", $description = null)
    {
        if (strpos($picurl, '/') === false) {
            return $picurl;
        }
        $material = Db::name('mp_material')->where('aid', $aid)->where('url', $picurl)->where('type', $type)->where('description', $description)->find();
        if ($material) {
            return $material['media_id'];
        }
        $url = \app\common\Pic::tolocal($picurl);
        $mediapath = ROOT_PATH . str_replace(PRE_URL . '/', '', $url);
        $data = array('media' => '@' . $mediapath);
        $data = [];
        $data['media'] = new \CurlFile($mediapath);
        if ($type == 'video') {
            $data['description'] = $description ? $description : jsonEncode(['title' => date('点击视频查看'), 'introduction' => '本视频上传于' . date('Y年m月d日')]);
        }
        $res = curl_post('https://api.weixin.qq.com/cgi-bin/material/add_material?access_token=' . self::access_token($aid, 'mp') . '&type=' . $type, $data);
        $res = json_decode($res, true);
        if ($res['media_id']) {
            Db::name('mp_material')->insert(['aid' => $aid, 'url' => $picurl, 'media_id' => $res['media_id'], 'type' => $type, 'description' => $description, 'createtime' => time()]);
            return $res['media_id'];
        } else {
            return ['status' => 0, 'msg' => self::geterror($res)];
        }
    }

    public static function pictomedia($aid, $platform, $picurl, $isyj = false)
    {
        if (strpos($picurl, '/') === false) {
            return $picurl;
        }
        $url = \app\common\Pic::tolocal($picurl);
        $mediapath = ROOT_PATH . str_replace(PRE_URL . '/', '', $url);
        $data = [];
        $data['media'] = new \CurlFile($mediapath);
        $access_token = self::access_token($aid, $platform);
        if ($isyj) {//上传永久素材 小程序不行
            $url = 'https://api.weixin.qq.com/cgi-bin/material/add_material?access_token=' . $access_token . '&type=image';
        } else {//上传临时素材
            $url = 'https://api.weixin.qq.com/cgi-bin/media/upload?access_token=' . $access_token . '&type=image';
        }
        $res = curl_post($url, $data);
        $res = json_decode($res, true);
        if ($res['media_id']) {
            return $res['media_id'];
        } else {
            return ['status' => 0, 'msg' => self::geterror($res)];
        }
    }

    //发模板消息

    /**
     * 生成公众号二维码和小程序码
     * @param $aid
     * @param $platform wx:小程序，mp 公众号
     * @param $page
     * @param array $scene 格式：['id' => 1, 'name' => 'demo']，最终参数格式：id_1-cid_2
     * @return array
     */
    public static function getQRCode($aid, $platform, $page, $scene = [])
    {
        if (strpos($page, '?')) {
            $pageArr = explode('?', $page);
            $page = $pageArr[0];
            $params = explode('&', $pageArr[1]);
        }
        if (!empty($params)) {
            foreach ($params as $v) {
                if ($v != '') {
                    $vArr = explode('=', $v);
                    $scene[$vArr[0]] = $vArr[1];
                }
            }
        }
        $sceneFormat = '';
        if ($scene) {
            $i = 1;
            foreach ($scene as $key => $val) {
                $sceneFormat .= $key . '_' . $val;
                if ($i < count($scene)) {
                    $sceneFormat .= '-';
                }
                $i++;
            }
        }
        if ($platform == 'wx') {
            $url = 'https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=' . self::access_token($aid, $platform);
            $data = array();
            $data['scene'] = $sceneFormat ? $sceneFormat : '0';
            $data['page'] = $page;
            $res = request_post($url, jsonEncode($data));
            $errmsg = json_decode($res, true);
            if ($errmsg) {
                return ['status' => 0, 'msg' => self::geterror($errmsg), 'rs' => $errmsg];
            }
            $dir = 'upload/' . date('Ym');
            if (!is_dir(ROOT_PATH . $dir)) mk_dir(ROOT_PATH . $dir);
            $filename = date('d_His') . rand(1000, 9999) . '.jpg';
            $mediapath = $dir . '/' . $filename;
            file_put_contents(ROOT_PATH . $mediapath, $res);
            $url = \app\common\Pic::uploadoss(PRE_URL . '/' . $mediapath);
            return ['status' => 1, 'url' => $url];
        } elseif ($platform == 'mp') {
            $url = 'https://api.weixin.qq.com/cgi-bin/qrcode/create?access_token=' . self::access_token($aid, $platform);
            $data = array();
            $data['action_name'] = 'QR_LIMIT_STR_SCENE';
            $data['action_info'] = array('scene' => array('scene_str' => $sceneFormat));
            $rs = request_post($url, jsonEncode($data));
            $rs = json_decode($rs, true);
            if ($rs['url']) {
                $url = createqrcode($rs['url']);
                return ['status' => 1, 'url' => $url];
            } else {
                return ['status' => 0, 'msg' => self::geterror($rs)];
            }
        }
    }

    //发订阅消息

    public static function sendtmpl($aid = 1, $mid, $tmpltype, $content, $tourl = '')
    {

        if (is_numeric($mid)) {
            $openid = Db::name('member')->where('id', $mid)->value('mpopenid');
        } else {
            $openid = $mid;
        }
        if (!$openid) return ['status' => 0, 'msg' => 'openid为空'];
        $set = Db::name('mp_tmplset')->where('aid', $aid)->find();
        $tmplid = $set[$tmpltype];
        if (!$tmplid) return ['status' => 0, 'msg' => '未配置模板ID'];
        $access_token = self::access_token($aid, 'mp');
        $url = 'https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=' . $access_token;
        $data = [];
        $data['template_id'] = trim($tmplid);
        if ($tourl) {
            $data['url'] = $tourl;
        }
        $newcontent = [];
        foreach ($content as $k => $v) {
            if (is_array($v)) {
                $newcontent[$k] = $v;
            } else {
                $newcontent[$k] = ['value' => strval($v)];
            }
        }
        $data['data'] = $newcontent;
        $data['touser'] = $openid;
        syncRequest($url, str_replace('\\/', '/', jsonEncode($data)));
        return ['status' => 1, 'msg' => '发送成功'];
        /*
        $rs = request_post($url,str_replace('\\/','/',jsonEncode($data)));
        $rs = json_decode($rs,true);
        if($rs['errcode']!=0){
            return ['status'=>0,'msg'=>self::geterror($rs)];
        }else{
            return ['status'=>1,'msg'=>'发送成功'];
        }
		*/
    }

    //发模板消息 后台人员

    public static function sendwxtmpl($aid = 1, $mid, $tmpltype, $contentnew, $tourl = '', $content)
    {
        if (is_numeric($mid)) {
            $openid = Db::name('member')->where('id', $mid)->value('wxopenid');
        } else {
            $openid = $mid;
        }
        if (!$openid) return ['status' => 0, 'msg' => 'openid为空'];
        $set = Db::name('wx_tmplset')->where('aid', $aid)->find();
        $tmpltypenew = $tmpltype . '_new';
        $tmplid = $set[$tmpltype . '_new'];
        if (!$tmplid) {
            $tmplid = $set[$tmpltype];
        } else {
            $content = $contentnew;
        }
        if (!$tmplid) return ['status' => 0, 'msg' => '未配置模板ID'];
        $access_token = self::access_token($aid, 'wx');
        $url = 'https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=' . $access_token;
        $data = [];
        $data['template_id'] = trim($tmplid);
        if ($tourl) {
            $data['page'] = $tourl;
        }
        $newcontent = [];
        foreach ($content as $k => $v) {
            if (is_array($v)) {
                $newcontent[$k] = $v;
            } else {
                if (strpos($k, 'thing') === 0) {
                    if (!$v) $v = '暂无';
                    $v = mb_substr($v, 0, 20);
                }
                if ($v == '' && strpos($k, 'character_string') === 0) {
                    $v = 'empty';
                }
                $v = strval($v);
                $newcontent[$k] = ['value' => $v];
            }
        }
        $data['data'] = $newcontent;
        $data['touser'] = $openid;
        //$rs = request_post($url,str_replace('\\/','/',jsonEncode($data)));
        syncRequest($url, str_replace('\\/', '/', jsonEncode($data)));
        //\think\facade\Log::write($rs);
        //\think\facade\Log::write($data);
        $rs = json_decode($rs, true);
        if ($rs['errcode'] != 0) {
            return ['status' => 0, 'msg' => self::geterror($rs)];
        } else {
            return ['status' => 1, 'msg' => '发送成功'];
        }
    }

    //会员卡信息变更

    public static function sendhttmpl($aid, $bid, $tmpltype, $content, $tourl = '', $mdid = 0)
    {

        if (!$mdid) $mdid = 0;
        if (!$bid) $bid = 0;
        if (strlen($tmpltype) == 43) {
            $tmplid = $tmpltype;
            $tmpltype = '1';
        } else {
            $set = Db::name('mp_tmplset')->where('aid', $aid)->find();
            $tmplid = $set[$tmpltype];
        }
        if (!$tmplid) return ['status' => 0, 'msg' => '未配置模板ID'];
        $access_token = self::access_token($aid, 'mp');
        $url = 'https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=' . $access_token;
        $data = [];
        $data['template_id'] = trim($tmplid);
        if ($tourl) {
            $data['url'] = $tourl;
        }
        $newcontent = [];
        foreach ($content as $k => $v) {
            if (is_array($v)) {
                $newcontent[$k] = $v;
            } else {
                $newcontent[$k] = ['value' => strval($v)];
            }
        }
        $data['data'] = $newcontent;
        $userlist = Db::name('admin_user')->where("`aid`={$aid} and `bid`={$bid} and `mid`!=0 and `{$tmpltype}`=1 and (`mdid`=0 or `mdid`='{$mdid}')")->select()->toArray();
        //\think\facade\Log::write(Db::getlastsql());
        if (!$userlist) {
            return ['status' => 0, 'msg' => '没有设置接收用户'];
        }
        $mids = [];
        foreach ($userlist as $user) {
            $mids[] = $user['mid'];
            $user['mpopenid'] = Db::name('member')->where('id', $user['mid'])->value('mpopenid');
            if (!$user['mpopenid']) continue;
            if ($user['isadmin'] == 0) {
                $notice_auth_data = json_decode($user['notice_auth_data'], true);
                if (!$notice_auth_data) continue;
                if (!in_array($tmpltype, $notice_auth_data)) {
                    continue;
                }
            }
            $data['touser'] = $user['mpopenid'];
            //$rs = request_post($url,str_replace('\\/','/',jsonEncode($data)));
            syncRequest($url, str_replace('\\/', '/', jsonEncode($data)));
            //\think\facade\Log::write($rs);
            //\think\facade\Log::write($user);
        }

        if (defined('PRE_URL2') && PRE_URL2 != '') {
            $pre_url = PRE_URL2;
        } elseif (defined('PRE_URL')) {
            $pre_url = PRE_URL;
        }
        if ($pre_url) {
            $urlstart = $pre_url . '/h5/' . $aid . '.html#';
            $pagepath = str_replace($urlstart, '', $tourl);
            send_socket(['type' => 'notice', 'data' => ['aid' => $aid, 'mids' => $mids, 'title' => $content['first'], 'desc' => $content['remark'], 'url' => $pagepath]]);
        } else {
            if ($tourl) {
                $url_arr = explode('.html#', $tourl);
                $pagepath = $url_arr[1];
                $search = '~^(([^:/?#]+):)?(//([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?~i';
                preg_match_all($search, $tourl, $array2);
                $local_url = $array2[1][0] . $array2[3][0];
                send_socket(['type' => 'notice', 'data' => ['aid' => $aid, 'mids' => $mids, 'title' => $content['first'], 'desc' => $content['remark'], 'url' => $pagepath]], $local_url);
            }
        }

        $rsArr = json_decode($rs, true);
        if ($rsArr['errcode'] != 0) {
            return ['status' => 0, 'msg' => self::geterror($rsArr)];
        } else {
            return ['status' => 1, 'msg' => '发送成功'];
        }
    }

    public static function updatemembercard($aid, $mid, $record_bonus = '')
    {
        $member = Db::name('member')->where('aid', $aid)->where('id', $mid)->find();
        if ($member['card_id'] && $member['card_code']) {
            $url = 'https://api.weixin.qq.com/card/get?access_token=' . self::access_token($aid, 'mp');
            $rs = request_post($url, jsonEncode(['card_id' => $member['card_id']]));
            $rs = json_decode($rs, true);
            //\think\facade\Log::write($rs);
            $custom_field1 = $rs['card']['member_card']['custom_field1'];
            $custom_field2 = $rs['card']['member_card']['custom_field2'];
            $custom_field3 = $rs['card']['member_card']['custom_field3'];
            $supply_bonus = $rs['card']['member_card']['supply_bonus'];

            $url = 'https://api.weixin.qq.com/card/membercard/updateuser?access_token=' . self::access_token($aid, 'mp');
            $postdata = [];
            $postdata['card_id'] = $member['card_id'];
            $postdata['code'] = $member['card_code'];
            if ($supply_bonus) {
                $postdata['bonus'] = $member['score'];
                if ($record_bonus) {
                    $postdata['record_bonus'] = $record_bonus;
                }
            }
            $notify_optional = [];
            if ($custom_field1) {
                if ($custom_field1['name_type'] == 'FIELD_NAME_TYPE_COUPON') {//优惠券
                    $couponcount = Db::name('coupon_record')->where('aid', $aid)->where('mid', $member['id'])->where('status', 0)->where('endtime', '>=', time())->count();
                    $postdata['custom_field_value1'] = $couponcount;
                }
                if ($custom_field1['name_type'] == 'FIELD_NAME_TYPE_LEVEL') {//等级
                    $memberlv = Db::name('member_level')->where('aid', $aid)->where('id', $member['levelid'])->find();
                    $postdata['custom_field_value1'] = $memberlv['name'];
                }
                if ($custom_field1['name'] == '余额' || $custom_field1['name'] == t('余额')) {//余额
                    $postdata['custom_field_value1'] = $member['money'];
                }
                $notify_optional['is_notify_custom_field1'] = true;
            }
            if ($custom_field2) {
                if ($custom_field2['name_type'] == 'FIELD_NAME_TYPE_COUPON') {//优惠券
                    $couponcount = Db::name('coupon_record')->where('aid', $aid)->where('mid', $member['id'])->where('status', 0)->where('endtime', '>=', time())->count();
                    $postdata['custom_field_value2'] = $couponcount;
                }
                if ($custom_field2['name_type'] == 'FIELD_NAME_TYPE_LEVEL') {//等级
                    $memberlv = Db::name('member_level')->where('aid', $aid)->where('id', $member['levelid'])->find();
                    $postdata['custom_field_value2'] = $memberlv['name'];
                }
                if ($custom_field2['name'] == '余额' || $custom_field2['name'] == t('余额')) {//余额
                    $postdata['custom_field_value2'] = $member['money'];
                }
                $notify_optional['is_notify_custom_field2'] = true;
            }
            if ($custom_field3) {
                if ($custom_field3['name_type'] == 'FIELD_NAME_TYPE_COUPON') {//优惠券
                    $couponcount = Db::name('coupon_record')->where('aid', $aid)->where('mid', $member['id'])->where('status', 0)->where('endtime', '>=', time())->count();
                    $postdata['custom_field_value3'] = $couponcount;
                }
                if ($custom_field3['name_type'] == 'FIELD_NAME_TYPE_LEVEL') {//等级
                    $memberlv = Db::name('member_level')->where('aid', $aid)->where('id', $member['levelid'])->find();
                    $postdata['custom_field_value3'] = $memberlv['name'];
                }
                if ($custom_field3['name'] == '余额' || $custom_field3['name'] == t('余额')) {//余额
                    $postdata['custom_field_value3'] = $member['money'];
                }
                $notify_optional['is_notify_custom_field3'] = true;
            }
            if ($notify_optional) {
                $postdata['notify_optional'] = $notify_optional;
            }
            syncRequest($url, jsonEncode($postdata));
            //\think\facade\Log::write($rs);
            return [];
        }
    }

    //微信公众平台返回错误码对应含义

    public static function setauthinfo($aid, $authorization_code, $createtype = 0)
    {
        //使用授权码换取公众号或小程序的接口调用凭据和授权信息
        $url = 'https://api.weixin.qq.com/cgi-bin/component/api_query_auth?component_access_token=' . self::component_access_token();
        $data = array();
        $data['component_appid'] = self::component_appid();
        $data['authorization_code'] = $authorization_code;
        $rs = request_post($url, jsonEncode($data));
        //dump($rs);die;
        $rs = json_decode($rs, true);
        if (isset($rs['errcode']) && $rs['errcode'] != 0) {
            return ['status' => 0, 'msg' => self::geterror($rs)];
        }
        $info = $rs['authorization_info'];
        $appid = $info['authorizer_appid'];
        $refresh_token = $info['authorizer_refresh_token'];
        if ($info) {
            $update = array();
            $update['appid'] = $appid;
            $update['access_token'] = $info['authorizer_access_token'];
            $update['expires_time'] = time() + 7000;
            $update['jsapi_ticket'] = '';
            $update['ticket_expires_time'] = null;
            if (Db::name('access_token')->where('appid', $update['appid'])->find()) {
                Db::name('access_token')->where('appid', $update['appid'])->update($update);
            } else {
                Db::name('access_token')->insert($update);
            }
        }
        //获取授权方的帐号基本信息
        $url = 'https://api.weixin.qq.com/cgi-bin/component/api_get_authorizer_info?component_access_token=' . self::component_access_token();
        $data = array();
        $data['component_appid'] = self::component_appid();
        $data['authorizer_appid'] = $appid;
        $rs = request_post($url, jsonEncode($data));
        $rs = json_decode($rs, true);
        if (isset($rs['errcode']) && $rs['errcode'] != 0) {
            return ['status' => 0, 'msg' => self::geterror($rs)];
        }

        $authorizer_info = $rs['authorizer_info'];
        $authorization_info = $rs['authorization_info'];

        $infodata = array();
        $infodata['aid'] = $aid;
        $infodata['authtype'] = 1;
        $infodata['appid'] = $appid;
        $infodata['nickname'] = $authorizer_info['nick_name'];
        $infodata['headimg'] = \app\common\Pic::tolocal($authorizer_info['head_img']);
        $infodata['qrcode'] = \app\common\Pic::tolocal($authorizer_info['qrcode_url']);
        $infodata['refresh_token'] = $refresh_token;
        if ($authorizer_info['MiniProgramInfo']) {
            $apptype = 'wx';
            $verify_type_info = $authorizer_info['verify_type_info']['id'];
            if ($verify_type_info > -1) {
                $infodata['level'] = 1; //已认证
            } else {
                $infodata['level'] = 0; //未认证
            }
            $infodata['createtype'] = $createtype;
            if (Db::name('admin_setapp_wx')->where('aid', $aid)->find()) {
                Db::name('admin_setapp_wx')->where('aid', $aid)->update($infodata);
            } else {
                Db::name('admin_setapp_wx')->insert($infodata);
            }
        } else {
            $apptype = 'mp';
            $service_type_info = $authorizer_info['service_type_info']['id'];
            $verify_type_info = $authorizer_info['verify_type_info']['id'];
            $infodata['level'] = 0;
            if ($service_type_info == 2) {
                if ($verify_type_info > -1) {
                    $infodata['level'] = 4; //认证服务号
                } else {
                    $infodata['level'] = 2; //未认证服务号
                }
            } else {
                if ($verify_type_info > -1) {
                    $infodata['level'] = 3; //认证订阅号
                } else {
                    $infodata['level'] = 1; //未认证订阅号
                }
            }
            if (Db::name('admin_setapp_mp')->where('aid', $aid)->find()) {
                Db::name('admin_setapp_mp')->where('aid', $aid)->update($infodata);
            } else {
                Db::name('admin_setapp_mp')->insert($infodata);
            }
            Db::name('mp_material')->where('aid', $aid)->delete();
        }
        $set = Db::name('admin_set')->where('aid', $aid)->find();
        if ($set['name'] == '商城系统' && $set['logo'] == request()->domain() . '/static/imgsrc/logo.jpg') {
            Db::name('admin_set')->where('aid', $aid)->update(['name' => $infodata['nickname'], 'logo' => $infodata['headimg'], 'desc' => $authorizer_info['signature']]);
        }
        $access_token = \app\common\Wechat::access_token($aid, $apptype);
        if ($apptype == 'wx') {
            //设置小程序服务器域名
            $url = 'https://api.weixin.qq.com/wxa/modify_domain?access_token=' . $access_token;
            $postdata = array();
            $postdata['action'] = 'set';
            $postdata['requestdomain'] = array(str_replace('http://', 'https://', request()->domain()));
            $postdata['wsrequestdomain'] = array('wss://' . $_SERVER['HTTP_HOST']);
            $postdata['uploaddomain'] = array(str_replace('http://', 'https://', request()->domain()));
            $postdata['downloaddomain'] = array(str_replace('http://', 'https://', request()->domain()));
            $rs = request_post($url, jsonEncode($postdata));
            //dump($rs);
            //设置小程序业务域名
            $url = 'https://api.weixin.qq.com/wxa/setwebviewdomain?access_token=' . $access_token;
            $postdata = array();
            $postdata['action'] = 'add';
            $postdata['webviewdomain'] = array(request()->domain());
            $rs = request_post($url, jsonEncode($postdata));
            //dump($rs);die;
        }
        //获取开放平台账号appid
        $url = 'https://api.weixin.qq.com/cgi-bin/open/get?access_token=' . $access_token;
        $postdata = array();
        $postdata['appid'] = $appid;
        $rs = request_post($url, jsonEncode($postdata));
        $rs = json_decode($rs, true);
        if ($rs && $rs['open_appid']) {
            $open_appid = $rs['open_appid'];
        } elseif ($rs['errcode'] == 0 || $rs['errcode'] == 89002) {
            $open_appid = null;
        } else {
            $open_appid = '-1'; //没有权限 获取不到
        }
        if ($apptype == 'wx') {
            Db::name('admin_setapp_wx')->where('aid', $aid)->update(['open_appid' => $open_appid]);
            \app\common\System::plog('授权绑定小程序');
        } else {
            Db::name('admin_setapp_mp')->where('aid', $aid)->update(['open_appid' => $open_appid]);
            \app\common\System::plog('授权绑定公众号');
        }
        return ['status' => 1];
    }
}