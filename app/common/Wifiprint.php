<?php

namespace app\common;

class Wifiprint
{
    //打印小票 aid 模块 订单号
    public static function print($aid, $type, $orderid, $autoprint = 1)
    {
        //type:cashier,182
        $order = db($type . '_order')->where('id', $orderid)->find();
        halt($order);
        if (!$order['bid']) $order['bid'] = 0;
        $where = [];
        $where[] = ['aid', '=', $aid];
        $where[] = ['status', '=', 1];
        if ($autoprint == 1) {
            $where[] = ['autoprint', '=', $autoprint];
        }
        $machinelist = db('wifiprint_set')->where($where)->where('bid', $order['bid'])->select()->toArray(); //打印机列表
        if (!$machinelist) return ['status' => 0, 'msg' => '没有配置小票打印机'];
        //if($type == 'shop' && $order['bid']!=0) return [];

        $order['formdata'] = \app\model\Freight::getformdata($order['id'], $type . '_order');
        $printnum = 0;
        foreach ($machinelist as $machine) {
            if ($order['freight_type'] == 0 && $machine['print_ps'] == 0) { //配送订单
                continue;
            }
            if ($order['freight_type'] == 1 || $order['freight_type'] == 5) { //自提订单
                if ($machine['print_zt_type'] == 0) continue;
                if ($machine['print_zt_type'] == 2) { //指定门店
                    $mdids = explode(',', $machine['print_zt_mdid']);
                    if (!in_array($order['mdid'], $mdids)) continue;
                }
            }
            if ($machine['type'] == 0) { //普通打印机
                $content = '';
                $content = "<MS>1," . $machine['voice'] . "</MS>";
                $content .= "<center>@@2 ** " . $machine['title'] . " **</center>\r\r";
                $content .= "<B>订单编号：" . $order['ordernum'] . "</B>\r";
                $content .= "<B>配送方式：" . $order['freight_text'] . "</B>\r";
                if ($order['freight_time']) {
                    $content .= "配送时间：<FS>" . $order['freight_time'] . "</FS>\r";
                }
                $content .= "收货人:<FS>" . $order['linkman'] . "</FS>\r";
                $content .= "联系电话:<FS>" . $order['tel'] . "</FS>\r";
                $content .= "收货地址:<FS>" . $order['area'] . " " . $order['address'] . "</FS>\r";
                $content .= "付款时间：<B>" . date('Y-m-d H:i:s', $order['paytime']) . "</B>\r";
                $content .= "付款方式：<B>" . $order['paytype'] . "</B>\r\r";
                $content .= "<table>";
                $content .= "<tr><td>商品名称</td><td>数量</td><td>总价</td></tr>";
                if ($type == 'shop') {
                    $ordergoods = db($type . '_order_goods')->where('orderid', $order['id'])->select()->toArray();
                    foreach ($ordergoods as $item) {
                        $content .= "<tr><td><FB><B>" . $item['name'] . '(' . $item['ggname'] . ')' . "</B></FB></td><td><B>" . $item['num'] . "</B></td><td><B>" . $item['totalprice'] . "</B></td></tr>";
                    }
                } elseif ($type == 'collage') {
                    $content .= "<tr><td><FB><B>" . $order['proname'] . '(' . $order['ggname'] . ')' . "</B></FB></td><td><B>" . $order['num'] . "</B></td><td><B>" . $order['totalprice'] . "</B></td></tr>";
                } elseif ($type == 'lucky_collage') {
                    $content .= "<tr><td><FB><B>" . $order['proname'] . '(' . $order['ggname'] . ')' . "</B></FB></td><td><B>" . $order['num'] . "</B></td><td><B>" . $order['totalprice'] . "</B></td></tr>";
                } elseif ($type == 'seckill') {
                    $content .= "<tr><td><FB>" . $order['proname'] . '(' . $order['ggname'] . ')' . "</FB></td><td>" . $order['num'] . "</td><td>" . $order['totalprice'] . "</td></tr>";
                } elseif ($type == 'kanjia') {
                    $content .= "<tr><td>" . $order['proname'] . "</td><td>1</td><td>" . $order['totalprice'] . "</td></tr>";
                } elseif ($type == 'scoreshop') {

                    $ordergoods = db('scoreshop_order_goods')->where('orderid', $order['id'])->select()->toArray();
                    foreach ($ordergoods as $item) {
                        if ($item['totalmoney'] > 0 && $item['totalscore'] > 0) {
                            $price = $item['totalmoney'] . "元+" . $item['totalscore'] . t('积分');
                        } elseif ($item['totalmoney'] > 0) {
                            $price = $item['totalmoney'] . "元";
                        } else {
                            $price = $item['totalscore'] . t('积分');
                        }

                        $content .= "<tr><td><FB>" . $item['name'] . "</FB></td><td>" . $item['num'] . " </td><td>" . $price . "</td></tr>";
                    }
                }
                $content .= "</table>";
                $content .= "\r";
                if ($order['message']) {
                    $content .= "备注：<FS>" . $order['message'] . "</FS>\r";
                } else {
//					$content .= "备注：无\r";
                }
                if ($type == 'scoreshop') {
                    $content .= "<right>实付金额：" . $price . "</right>";
                } else {
                    $content .= "<right>实付金额：￥" . $order['totalprice'] . "</right>";
                }
                if ($order['formdata']) {
                    foreach ($order['formdata'] as $formdata) {
                        if ($formdata[2] != 'upload') {
                            if ($formdata[0] == '备注') {
                                $content .= $formdata[0] . "：<FS>" . $formdata[1] . "</FS>\r\r";
                            } else {
                                $content .= $formdata[0] . "：<FS>" . $formdata[1] . "</FS>\r";
                            }
                        }
                    }
                }
                $content .= "\r\r";
                $rs = self::yilianyun_print($machine['client_id'], $machine['client_secret'], $machine['access_token'], $machine['machine_code'], $machine['msign'], $content);
                $printnum++;
            } elseif ($machine['type'] == 1) {
                if ($machine['machine_type'] == 1) { //标签打印机

                    if ($type == 'shop') {
                        $ordergoods = db($type . '_order_goods')->where('orderid', $order['id'])->select()->toArray();
                        $count = count($ordergoods);
                        foreach ($ordergoods as $k => $item) {
                            $content = '';
                            $content .= '<TEXT x="9" y="5" font="12" w="1" h="1" r="0">#' . $order['ordernum'] . '   ' . ($k + 1) . '/' . $count . '</TEXT>';
                            $content .= '<TEXT x="9" y="40" font="12" w="1" h="1" r="0">' . $item['name'] . '</TEXT>';
                            $content .= '<TEXT x="9" y="70" font="12" w="1" h="1" r="0">' . $item['ggname'] . ' × ' . $item['num'] . '  共' . $item['totalprice'] . '元</TEXT>';
                            $content .= '<TEXT x="9" y="100" font="12" w="1" h="1" r="0">配送方式:' . $order['freight_text'] . '</TEXT>';
                            $content .= '<TEXT x="9" y="130" font="12" w="1" h="1" r="0">收货人:' . $order['linkman'] . '(' . $order['tel'] . ')</TEXT>';
                            $content .= '<TEXT x="9" y="155" font="12" w="1" h="1" r="0">' . $order['area'] . '</TEXT>';
                            $content .= '<TEXT x="9" y="180" font="12" w="1" h="1" r="0">' . $order['address'] . '</TEXT>';
                            $content .= '<TEXT x="9" y="210" font="12" w="1" h="1" r="0">实付金额：￥' . $order['totalprice'] . '</TEXT>';
                            $rs = self::feie_print($machine['client_id'], $machine['client_secret'], $machine['machine_code'], $machine['msign'], $content, 1);
                            $printnum++;
                        }
                    } elseif ($type == 'collage') {
                        $content = '';
                        $content .= '<TEXT x="9" y="5" font="12" w="1" h="1" r="0">#' . $order['ordernum'] . '   ' . ($k + 1) . '/' . $count . '</TEXT>';
                        $content .= '<TEXT x="9" y="40" font="12" w="1" h="1" r="0">' . $order['proname'] . '</TEXT>';
                        $content .= '<TEXT x="9" y="70" font="12" w="1" h="1" r="0">    × 1  共' . $order['totalprice'] . '元</TEXT>';
                        $content .= '<TEXT x="9" y="100" font="12" w="1" h="1" r="0">配送方式:' . $order['freight_text'] . '</TEXT>';
                        $content .= '<TEXT x="9" y="130" font="12" w="1" h="1" r="0">收货人:' . $order['linkman'] . '(' . $order['tel'] . ')</TEXT>';
                        $content .= '<TEXT x="9" y="155" font="12" w="1" h="1" r="0">' . $order['area'] . '</TEXT>';
                        $content .= '<TEXT x="9" y="180" font="12" w="1" h="1" r="0">' . $order['address'] . '</TEXT>';
                        $content .= '<TEXT x="9" y="210" font="12" w="1" h="1" r="0">实付金额：￥' . $order['totalprice'] . '</TEXT>';
                        $rs = self::feie_print($machine['client_id'], $machine['client_secret'], $machine['machine_code'], $machine['msign'], $content, 1);
                        $printnum++;
                    } elseif ($type == 'lucky_collage') {
                        $content = '';
                        $content .= '<TEXT x="9" y="5" font="12" w="1" h="1" r="0">#' . $order['ordernum'] . '   ' . ($k + 1) . '/' . $count . '</TEXT>';
                        $content .= '<TEXT x="9" y="40" font="12" w="1" h="1" r="0">' . $order['proname'] . '</TEXT>';
                        $content .= '<TEXT x="9" y="70" font="12" w="1" h="1" r="0">    × 1  共' . $order['totalprice'] . '元</TEXT>';
                        $content .= '<TEXT x="9" y="100" font="12" w="1" h="1" r="0">配送方式:' . $order['freight_text'] . '</TEXT>';
                        $content .= '<TEXT x="9" y="130" font="12" w="1" h="1" r="0">收货人:' . $order['linkman'] . '(' . $order['tel'] . ')</TEXT>';
                        $content .= '<TEXT x="9" y="155" font="12" w="1" h="1" r="0">' . $order['area'] . '</TEXT>';
                        $content .= '<TEXT x="9" y="180" font="12" w="1" h="1" r="0">' . $order['address'] . '</TEXT>';
                        $content .= '<TEXT x="9" y="210" font="12" w="1" h="1" r="0">实付金额：￥' . $order['totalprice'] . '</TEXT>';
                        $rs = self::feie_print($machine['client_id'], $machine['client_secret'], $machine['machine_code'], $machine['msign'], $content, 1);
                        $printnum++;
                    } elseif ($type == 'seckill') {
                        $content = '';
                        $content .= '<TEXT x="9" y="5" font="12" w="1" h="1" r="0">#' . $order['ordernum'] . '   ' . ($k + 1) . '/' . $count . '</TEXT>';
                        $content .= '<TEXT x="9" y="40" font="12" w="1" h="1" r="0">' . $order['proname'] . '</TEXT>';
                        $content .= '<TEXT x="9" y="70" font="12" w="1" h="1" r="0">    × 1  共' . $order['totalprice'] . '元</TEXT>';
                        $content .= '<TEXT x="9" y="100" font="12" w="1" h="1" r="0">配送方式:' . $order['freight_text'] . '</TEXT>';
                        $content .= '<TEXT x="9" y="130" font="12" w="1" h="1" r="0">收货人:' . $order['linkman'] . '(' . $order['tel'] . ')</TEXT>';
                        $content .= '<TEXT x="9" y="155" font="12" w="1" h="1" r="0">' . $order['area'] . '</TEXT>';
                        $content .= '<TEXT x="9" y="180" font="12" w="1" h="1" r="0">' . $order['address'] . '</TEXT>';
                        $content .= '<TEXT x="9" y="210" font="12" w="1" h="1" r="0">实付金额：￥' . $order['totalprice'] . '</TEXT>';
                        $rs = self::feie_print($machine['client_id'], $machine['client_secret'], $machine['machine_code'], $machine['msign'], $content, 1);
                        $printnum++;
                    } elseif ($type == 'kanjia') {
                        $content = '';
                        $content .= '<TEXT x="9" y="5" font="12" w="1" h="1" r="0">#' . $order['ordernum'] . '   ' . ($k + 1) . '/' . $count . '</TEXT>';
                        $content .= '<TEXT x="9" y="40" font="12" w="1" h="1" r="0">' . $order['proname'] . '</TEXT>';
                        $content .= '<TEXT x="9" y="70" font="12" w="1" h="1" r="0">    × 1  共' . $order['totalprice'] . '元</TEXT>';
                        $content .= '<TEXT x="9" y="100" font="12" w="1" h="1" r="0">配送方式:' . $order['freight_text'] . '</TEXT>';
                        $content .= '<TEXT x="9" y="130" font="12" w="1" h="1" r="0">收货人:' . $order['linkman'] . '(' . $order['tel'] . ')</TEXT>';
                        $content .= '<TEXT x="9" y="155" font="12" w="1" h="1" r="0">' . $order['area'] . '</TEXT>';
                        $content .= '<TEXT x="9" y="180" font="12" w="1" h="1" r="0">' . $order['address'] . '</TEXT>';
                        $content .= '<TEXT x="9" y="210" font="12" w="1" h="1" r="0">实付金额：￥' . $order['totalprice'] . '</TEXT>';
                        $rs = self::feie_print($machine['client_id'], $machine['client_secret'], $machine['machine_code'], $machine['msign'], $content, 1);
                        $printnum++;
                    } elseif ($type == 'scoreshop') {
                        $content = '';
                        $content .= '<TEXT x="9" y="5" font="12" w="1" h="1" r="0">#' . $order['ordernum'] . '   1/1</TEXT>';
                        $content .= '<TEXT x="9" y="40" font="12" w="1" h="1" r="0">' . $order['proname'] . '</TEXT>';

                        if ($order['totalprice'] > 0 && $order['score_price'] > 0) {
                            $price = $order['totalprice'] . "元+" . $order['score_price'] . t('积分');
                        } elseif ($order['totalprice'] > 0) {
                            $price = $order['totalprice'] . "元";
                        } else {
                            $price = $order['score_price'] . t('积分');
                        }
                        $content .= '<TEXT x="9" y="70" font="12" w="1" h="1" r="0"> ×' . $order['num'] . ' 共' . $price . '</TEXT>';
                        $content .= '<TEXT x="9" y="100" font="12" w="1" h="1" r="0">配送方式:' . $order['freight_text'] . '</TEXT>';
                        $content .= '<TEXT x="9" y="130" font="12" w="1" h="1" r="0">收货人:' . $order['linkman'] . '(' . $order['tel'] . ')</TEXT>';
                        $content .= '<TEXT x="9" y="155" font="12" w="1" h="1" r="0">' . $order['area'] . '</TEXT>';
                        $content .= '<TEXT x="9" y="180" font="12" w="1" h="1" r="0">' . $order['address'] . '</TEXT>';
                        $content .= '<TEXT x="9" y="210" font="12" w="1" h="1" r="0">实付金额：￥' . $order['totalprice'] . '</TEXT>';
                        $rs = self::feie_print($machine['client_id'], $machine['client_secret'], $machine['machine_code'], $machine['msign'], $content, 1);
                        $printnum++;
                    }
                } else {
                    if ($machine['tmpltype'] == 1) {
                        if ($type == 'shop') {
                            $ordergoods = db('shop_order_goods')->where('orderid', $order['id'])->select()->toArray();
                            $order['num'] = db('shop_order_goods')->where('orderid', $order['id'])->sum('num');
                        } elseif ($type == 'scoreshop') {
                            $ordergoods = db('scoreshop_order_goods')->where('orderid', $order['id'])->select()->toArray();
                            $order['num'] = db('scoreshop_order_goods')->where('orderid', $order['id'])->sum('num');
                        } else {
                            $ordergoods = [['name' => $order['name'], 'ggname' => $order['ggname'], 'num' => $order['num'], 'totalprice' => $order['product_price']]];
                        }
                        if ($type == 'scoreshop') {
                            if ($order['totalprice'] > 0 && $order['score_price'] > 0) {
                                $order['totalprice'] = $order['totalprice'] . "元 + " . $order['score_price'] . t('积分');
                            } elseif ($order['totalprice'] > 0) {
                                $order['totalprice'] = $order['totalprice'] . "元";
                            } else {
                                $order['totalprice'] = $order['score_price'] . t('积分');
                            }
                        }
                        $message = '';
                        if ($order['formdata']) {
                            foreach ($order['formdata'] as $formdata) {
                                if ($formdata[2] != 'upload') {
                                    if ($formdata[0] == '备注') {
                                        $message = $formdata[1];
                                    }
                                }
                            }
                        }

                        $tmplcontent = $machine['tmplcontent'];
                        if (strpos($tmplcontent, '<PLOOP>') !== false) {
                            $tmplcontentArr = explode('<PLOOP>', $tmplcontent);
                            $tmplcontent1 = $tmplcontentArr[0];
                            $tmplcontentArr = explode('</PLOOP>', $tmplcontentArr[1]);
                            $tmplcontent2 = $tmplcontentArr[0];
                            $tmplcontent3 = $tmplcontentArr[1];
                        } else {
                            $tmplcontent1 = $tmplcontent;
                            $tmplcontent2 = '';
                            $tmplcontent3 = '';
                        }
                        $textReplaceArr = [
                            '[订单号]' => $order['ordernum'],
                            '[配送方式]' => $order['freight_text'],
                            '[配送时间]' => $order['freight_time'],
                            '[收货人]' => $order['linkman'],
                            '[联系电话]' => $order['tel'],
                            '[收货地址]' => $order['area'] . " " . $order['address'],
                            '[付款时间]' => date('Y-m-d H:i:s', $order['paytime']),
                            '[付款方式]' => $order['paytype'],
                            '[商品名称]' => $order['title'],
                            '[数量]' => $order['num'],
                            '[价格]' => $order['product_price'],
                            '[实付金额]' => $order['totalprice'],
                            '[备注]' => $message,
                        ];
                        $tmplcontent1 = str_replace(array_keys($textReplaceArr), array_values($textReplaceArr), $tmplcontent1);
                        if ($tmplcontent3) {
                            $tmplcontent3 = str_replace(array_keys($textReplaceArr), array_values($textReplaceArr), $tmplcontent3);
                        }
                        if ($tmplcontent2) {
                            $tmplcontent2Arr = [];
                            foreach ($ordergoods as $item) {
                                if ($type == 'scoreshop') {
                                    if ($item['totalscore'] > 0 && $item['totalmoney'] > 0) {
                                        $item['totalprice'] = $item['totalmoney'] . "元 + " . $item['totalscore'] . t('积分');
                                    } elseif ($item['totalmoney'] > 0) {
                                        $item['totalprice'] = $item['totalmoney'] . "元";
                                    } else {
                                        $item['totalprice'] = $item['totalscore'] . t('积分');
                                    }
                                } else {
                                    $item['totalprice'] = '￥' . $item['totalprice'];
                                }
                                $textReplaceArr2 = [
                                    '[订单号]' => $order['ordernum'],
                                    '[配送方式]' => $order['freight_text'],
                                    '[配送时间]' => $order['freight_time'],
                                    '[收货人]' => $order['linkman'],
                                    '[联系电话]' => $order['tel'],
                                    '[收货地址]' => $order['area'] . " " . $order['address'],
                                    '[付款时间]' => date('Y-m-d H:i:s', $order['paytime']),
                                    '[付款方式]' => $order['paytype'],
                                    '[商品名称]' => $item['name'],
                                    '[规格]' => $item['ggname'],
                                    '[数量]' => $item['num'],
                                    '[价格]' => $item['totalprice'],
                                    '[实付金额]' => $order['totalprice'],
                                    '[备注]' => $message,
                                ];
                                $tmplcontent2Arr[] = str_replace(array_keys($textReplaceArr2), array_values($textReplaceArr2), $tmplcontent2);
                            }
                            $tmplcontent2 = implode('', $tmplcontent2Arr);
                        }
                        $content = $tmplcontent1 . $tmplcontent2 . $tmplcontent3;
                        $content = str_replace(["\r", "\n"], '', $content);
                    } else {
                        $content = '';
                        $content .= "<CB>** " . $machine['title'] . " **</CB><BR><BR>";
                        $content .= "订单编号：" . $order['ordernum'] . "<BR>";
                        $content .= "配送方式：" . $order['freight_text'] . "<BR>";
                        if ($order['freight_time']) {
                            $content .= "配送时间：<B>" . $order['freight_time'] . "</B><BR>";
                        }
                        $content .= "收货人:<B>" . $order['linkman'] . "</B><BR>";
                        $content .= "联系电话:<B>" . $order['tel'] . "</B><BR>";
                        $content .= "收货地址:<B>" . $order['area'] . " " . $order['address'] . "</B><BR>";
                        $content .= "付款时间：" . date('Y-m-d H:i:s', $order['paytime']) . "<BR>";
                        $content .= "付款方式：" . $order['paytype'] . "<BR><BR>";
                        $content .= "商品名称     数量     总价<BR>";
                        if ($type == 'shop') {
                            $ordergoods = db($type . '_order_goods')->where('orderid', $order['id'])->select()->toArray();
                            foreach ($ordergoods as $item) {
                                $content .= "<BOLD>" . $item['name'] . "(" . $item['ggname'] . ")</BOLD>   " . $item['num'] . "  " . $item['totalprice'] . "<BR>";
                            }
                        } elseif ($type == 'collage') {
                            $content .= "<BOLD>" . $order['proname'] . "(" . $order['ggname'] . ")</BOLD>   " . $order['num'] . "  " . $order['totalprice'] . "<BR>";
                        } elseif ($type == 'lucky_collage') {
                            $content .= "<BOLD>" . $order['proname'] . "(" . $order['ggname'] . ")</BOLD>   " . $order['num'] . "  " . $order['totalprice'] . "<BR>";
                        } elseif ($type == 'seckill') {
                            $content .= "<BOLD>" . $order['proname'] . "(" . $order['ggname'] . ")</BOLD>   " . $order['num'] . "  " . $order['totalprice'] . "<BR>";
                        } elseif ($type == 'kanjia') {
                            $content .= "" . $order['proname'] . "  1  " . $order['totalprice'] . "<BR>";
                        } elseif ($type == 'scoreshop') {
                            $ordergoods = db('scoreshop_order_goods')->where('orderid', $order['id'])->select()->toArray();
                            foreach ($ordergoods as $item) {
                                if ($item['totalmoney'] > 0 && $item['totalscore'] > 0) {
                                    $price = $item['totalmoney'] . "元+" . $item['totalscore'] . t('积分');
                                } elseif ($item['totalmoney'] > 0) {
                                    $price = $item['totalmoney'] . "元";
                                } else {
                                    $price = $item['totalscore'] . t('积分');
                                }
                                $content .= "<BOLD>" . $item['name'] . "</BOLD>   " . $item['num'] . "  " . $price . "<BR>";
                            }
                        }
                        $content .= "<BR>";
                        if ($order['message']) {
                            $content .= "备注：<B>" . $order['message'] . "</B><BR><BR>";
                        } else {
                            //						$content .= "备注：无<BR>";
                        }
                        if ($type == 'scoreshop') {
                            $content .= "<RIGHT>实付金额：" . $price . "</RIGHT>";
                        } else {
                            $content .= "<RIGHT>实付金额：￥" . $order['totalprice'] . "</RIGHT>";
                        }
                        if ($order['formdata']) {
                            foreach ($order['formdata'] as $formdata) {
                                if ($formdata[2] != 'upload') {
                                    if ($formdata[0] == '备注') {
                                        $content .= $formdata[0] . "：<B>" . $formdata[1] . "</B><BR>";
                                    } else {
                                        $content .= $formdata[0] . "：" . $formdata[1] . "<BR>";
                                    }
                                }
                            }
                        }
                        $content .= "<BR><BR>";
                    }
                    $rs = self::feie_print($machine['client_id'], $machine['client_secret'], $machine['machine_code'], $machine['msign'], $content);
                    $printnum++;
                }
            }
        }
        return ['status' => 1, 'msg' => '成功打印' . $printnum . '张'];
    }

    //易联云文本打印 $machine_code:易联云打印机终端号 $msign:易联云终端密钥 $content:打印内容
    public static function yilianyun_print($client_id, $client_secret, $access_token, $machine_code, $msign, $content)
    {
        if (!$machine_code || !$content) return ['status' => 0, 'error' => 1, 'msg' => '参数错误'];
        $data = [];
        $data['client_id'] = $client_id;
        $data['access_token'] = $access_token;
        $data['machine_code'] = $machine_code;
        $data['content'] = $content;
        $data['origin_id'] = date('YmdHis') . rand(10000, 99999);
        $data['timestamp'] = time();
        $data['id'] = self::uuid4();
        $data['sign'] = md5($data['client_id'] . $data['timestamp'] . $client_secret);
        $rs = request_post('https://open-api.10ss.net/print/index', $data);
        $rs = json_decode($rs, true);
        //dump($rs);
        if ($rs['error'] == 8) {
            //授权
            $data2 = [];
            $data2['client_id'] = $client_id;
            $data2['machine_code'] = $machine_code;
            $data2['msign'] = $msign;
            $data2['access_token'] = $access_token;
            $data2['timestamp'] = time();
            $data2['id'] = self::uuid4();
            $data2['sign'] = md5($data2['client_id'] . $data2['timestamp'] . $client_secret);
            $rs2 = request_post('https://open-api.10ss.net/printer/addprinter', $data2);
            //dump($rs2);
            $rs2 = json_decode($rs2, true);
            if ($rs2['error'] == 0) {
                $rs = request_post('https://open-api.10ss.net/print/index', $data);
                //dump($rs);
                $rs = json_decode($rs, true);
            } else {
                $rs = $rs2;
            }
        }
        //dump($rs);
        if ($rs['error'] == 0) {
            $rs['status'] = 1;
            $rs['msg'] = '打印成功';
        } else {
            $rs['status'] = 0;
            $rs['msg'] = $rs['error_description'];
            if ($rs['error'] == 16) {
                $rs['msg'] = '终端号配置错误，请检查终端号';
            }
        }
        return $rs;
    }

    //飞鹅小票打印机文本打印

    public static function uuid4()
    {
        mt_srand((double)microtime() * 10000);
        $charid = strtolower(md5(uniqid(rand(), true)));
        $hyphen = '-';
        $uuidV4 =
            substr($charid, 0, 8) . $hyphen .
            substr($charid, 8, 4) . $hyphen .
            substr($charid, 12, 4) . $hyphen .
            substr($charid, 16, 4) . $hyphen .
            substr($charid, 20, 12);
        return $uuidV4;
    }

    public static function feie_print($user, $ukey, $sn, $key, $content, $type = 0)
    {
        $postdata = [];
        $postdata['user'] = $user;
        $postdata['stime'] = time();
        $postdata['sig'] = sha1($user . $ukey . $postdata['stime']);
        if ($type == 1) {
            $postdata['apiname'] = 'Open_printLabelMsg';
        } else {
            $postdata['apiname'] = 'Open_printMsg';
        }
        $postdata['sn'] = $sn;
        $postdata['content'] = $content;
        $rs = request_post('http://api.feieyun.cn/Api/Open/', $postdata);
        $rs = json_decode($rs, true);
        if ($rs['ret'] == 0) {
            $rs['status'] = 1;
        } else {
            $rs['status'] = 0;
        }
        return $rs;
    }
}