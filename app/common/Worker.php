<?php

namespace app\common;

use think\facade\Db;
use think\facade\Log;
use think\worker\Server;

// $config = include(ROOT_PATH . 'config.php');

// define('kfsocket', 'websocket://0.0.0.0:' . $config['kfport']);

class Worker extends Server
{
    protected $socket = 'websocket://0.0.0.0:6228';
    protected $count = 1;
    protected $processes = 1;

    /**
     * 收到信息
     * @param $connection
     * @param $data
     */
    public function onMessage($connection, $res)
    {
        error_reporting(0);
        //$connection->send('我收到你的信息了!');
        // $config = include(ROOT_PATH . 'config.php');
        $authtoken = 'm8bH2dbw3w';
        //Log::write($res);
        $res = json_decode($res, true);
        if (!$res) return;


        if ($res['type'] == 'khinit') {
            $data = $res['data'];
            $aid = $data['aid'];
            $mid = $data['mid'];
            $member = Db::name('member')->where('id', $mid)->find();
            if ($res['token'] != $member['random_str']) {
                return;
            }
            //Log::write($data);
            $connection->ctype = 'kehu';
            $connection->aid = $aid;
            $connection->mid = $mid;
            $connection->send(json_encode(['data' => $data]));
        }

        if ($res['type'] == 'notice') { //消息提醒
            $data = $res['data'];
            if ($data) {
                $aid = $data['aid'];
                if ($data['mids']) {
                    $mids = $data['mids'];
                } else {
                    $mids = [$data['mid']];
                }

                foreach ($this->worker->connections as $con) {
                    if (isset($con->aid) && $con->aid == $aid && $con->ctype == 'kehu' && in_array($con->mid, $mids)) {
                        $con->send(json_encode(['type' => 'notice', 'data' => ['title' => $data['title'], 'desc' => $data['desc'], 'url' => $data['url']]]));
                    }
                }
            }
            $connection->send(json_encode(['type' => 'response', 'data' => ['status' => 1]]));
        }

        if ($res['type'] == 'tokehu') { //管理员发送消息给客户
            $data = $res['data'];
            if ($data) {
                if ($data['pre_url'] && !defined('PRE_URL')) {
                    define('PRE_URL', $data['pre_url']);
                }
                $uinfo = Db::name('admin_user')->where(['id' => $data['uid']])->find();
                if ($res['token'] != $uinfo['random_str']) {
                    Log::write('鉴权失败');
                    return;
                }

                $aid = $data['aid'];
                $bid = $uinfo['bid'];
                $uid = $data['uid'];
                $umid = $data['umid'];
                $mid = $data['mid'];
                $msgtype = $data['msgtype'];

                $lastmessage = Db::name('kefu_message')->where(['aid' => $aid, 'bid' => $bid, 'mid' => $mid, 'isreply' => 0])->order('id desc')->find();
                $platform = $lastmessage['platform'];
                $iswx = $lastmessage['iswx'];

                $minfo = Db::name('member')->where(['aid' => $aid, 'id' => $mid])->find();
                if (!$minfo) return;
                if ($bid == 0) {
                    $business = Db::name('admin_set')->where(['aid' => $aid])->field('name,logo')->find();
                } else {
                    $business = Db::name('business')->where(['id' => $bid])->field('name,logo')->find();
                }
                $hasonline = 0;
                if ($iswx) {
                    if ($msgtype == 'text') {
                        $rs = $this->send_text($aid, $platform, $data['content'], $minfo[$platform . 'openid']);
                    } elseif ($msgtype == 'image') {
                        $rs = $this->send_image($aid, $platform, $data['content'], $minfo[$platform . 'openid']);
                    }
                    if ($rs['status'] != 1) {
                        $connection->send(json_encode(['type' => 'response', 'data' => $rs]));
                        return;
                    } else {
                        $hasonline = 1;
                    }
                }
                $insertdata = [];
                $insertdata['aid'] = $aid;
                $insertdata['bid'] = $bid;
                $insertdata['mid'] = $mid;
                $insertdata['uid'] = $uid;
                $insertdata['nickname'] = $minfo['nickname'];
                $insertdata['headimg'] = $minfo['headimg'];
                $insertdata['tel'] = $minfo['tel'];
                $insertdata['unickname'] = $business['name'];
                $insertdata['uheadimg'] = $business['logo'];
                $insertdata['msgtype'] = $msgtype;
                $insertdata['content'] = getshowcontent($data['content']);
                $insertdata['createtime'] = time();
                $insertdata['isreply'] = 1;
                $insertdata['platform'] = $platform;
                $insertdata['isread'] = 0;
                foreach ($this->worker->connections as $con) {
                    if (isset($con->aid) && $con->aid == $aid && $con->ctype == 'kehu' && ($con->mid == $mid || $con->mid == $umid)) {
                        if ($con->mid == $mid) {
                            $hasonline = 1;
                        }
                        $con->send(json_encode(['type' => 'tokehu', 'data' => $insertdata]));
                    }
                }
                $insertdata['content'] = $data['content'];
                $insertdata['isread'] = $hasonline;
                Db::name('kefu_message')->insert($insertdata);
                //不在线 发消息通知
                if ($hasonline == 0) {
                    if ($msgtype == 'text') {
                        $content = $data['content'];
                    } elseif ($msgtype == 'image') {
                        $content = '[图片]';
                    } elseif ($msgtype == 'voice') {
                        $content = '[语音]';
                    } elseif ($msgtype == 'video') {
                        $content = '[小视频]';
                    } elseif ($msgtype == 'miniprogrampage') {
                        $content = json_decode($data['content']);
                        $content = '小程序页面[' . $content->Title . ']';
                    } else {
                        $content = $data['content'];
                    }
                    $tmplcontent = array();
                    $tmplcontent['first'] = '咨询回复通知';
                    $tmplcontent['keyword1'] = $data['nickname'];
                    $tmplcontent['keyword2'] = $data['tel'];
                    $tmplcontent['keyword3'] = date('Y-m-d H:i:s');
                    $tmplcontent['remark'] = '回复内容：' . $content . '，请点击进入查看~';
                    $rs = \app\common\Wechat::sendtmpl($aid, $mid, 'tmpl_kehuzixun', $tmplcontent, m_url('/pages/kefu/index?bid=' . $bid, $aid));
                }
            }
        }
        if ($res['type'] == 'tokefu') { //客户发送消息给管理员
            $data = $res['data'];
            if ($data) {
                if ($data['pre_url'] && !defined('PRE_URL')) {
                    define('PRE_URL', $data['pre_url']);
                }
                $aid = $data['aid'];
                $mid = $data['mid'];
                $bid = $data['bid'];
                $platform = $data['platform'];

                $member = Db::name('member')->where('id', $mid)->find();
                if ($res['token'] != $member['random_str']) {
                    return;
                }
                //可接收消息的管理员
                $umids = Db::name('admin_user')->where('aid', $aid)->where('bid', $bid)->where('mid', '<>', 0)
                ->where('tmpl_kehuzixun', 1)->where('mdid', 0)->column('mid');

                $insertdata = [];
                $insertdata['aid'] = $aid;
                $insertdata['mid'] = $mid;
                $insertdata['bid'] = $bid;
                $insertdata['nickname'] = $member['nickname'];
                $insertdata['headimg'] = $member['headimg'];
                $insertdata['tel'] = $member['tel'];
                $insertdata['msgtype'] = $data['msgtype'];
                // $insertdata['content'] = getshowcontent($data['content']);
                $insertdata['content'] = getshowcontent($data['content']);
                $insertdata['createtime'] = time();
                $insertdata['isreply'] = 0;
                $insertdata['platform'] = $platform;
                $insertdata['isread'] = 0;

                $hasonline = 0;
                foreach ($this->worker->connections as $con) {
                    if (isset($con->aid) && $con->aid == $aid && $con->ctype == 'kehu' && ($con->mid == $mid || in_array($con->mid, $umids))) {
                        if (in_array($con->mid, $umids)) $hasonline = 1;
                        $con->send(json_encode(['type' => 'tokefu', 'data' => $insertdata]));
                    }
                }
                $insertdata['content'] = $data['content'];
                $insertdata['iswx'] = ($data['iswx'] == 1 ? 1 : 0);
                Db::name('kefu_message')->insert($insertdata);

                if ($hasonline == 0) { //没有在线的客服 发送通知
                    if ($data['msgtype'] == 'text') {
                        $content = $data['content'];
                    } elseif ($data['msgtype'] == 'image') {
                        $content = '[图片]';
                    } elseif ($data['msgtype'] == 'voice') {
                        $content = '[语音]';
                    } elseif ($data['msgtype'] == 'video') {
                        $content = '[小视频]';
                    } elseif ($data['msgtype'] == 'miniprogrampage') {
                        $content = json_decode($data['content']);
                        $content = '小程序页面[' . $content->Title . ']';
                    } else {
                        $content = $data['content'];
                    }
                    $tmplcontent = array();
                    if ($data['platform'] == 'h5') {
                        $tmplcontent['first'] = '用户[' . $member['nickname'] . ']正在通过在线客服咨询您';
                    } elseif ($data['platform'] == 'mp') {
                        $tmplcontent['first'] = '用户[' . $member['nickname'] . ']正在通过公众号咨询您';
                    } elseif ($data['platform'] == 'wx') {
                        $tmplcontent['first'] = '用户[' . $member['nickname'] . ']正在通过小程序咨询您';
                    }
                    $tmplcontent['keyword1'] = $member['nickname'];
                    $tmplcontent['keyword2'] = $member['tel'];
                    $tmplcontent['keyword3'] = date('Y-m-d H:i:s');
                    $tmplcontent['remark'] = '咨询内容：' . $content . '，请点击进入查看~';
                    //Log::write($tmplcontent);
                    $rs = \app\common\Wechat::sendhttmpl($data['aid'], $data['bid'], 'tmpl_kehuzixun', $tmplcontent, m_url('/admin/kefu/index', $data['aid']));
                }
            }
            $connection->send(json_encode(['type' => 'response', 'data' => ['status' => 1]]));
        }
        if ($res['type'] == 'peisong') { //发配送单
            $data = $res['data'];
            if ($data) {
                $aid = $data['aid'];
                $psorderid = $data['psorderid'];
                $psorder = Db::name('peisong_order')->where('id', $psorderid)->find();
                $binfo = json_decode($psorder['binfo'], true);
                $orderinfo = json_decode($psorder['orderinfo'], true);

                $desc = $binfo['name'] . '->' . $orderinfo['address'];
                if ($psorder['psid']) {
                    $title = '有新的订单待配送';
                    $mids = Db::name('peisong_user')->where('aid', $aid)->where('id', $psorder['psid'])->column('mid');
                    $type = '1';
                } else {
                    $title = '有新的配送订单待接单';
                    $mids = Db::name('peisong_user')->where('aid', $aid)->where('status', 1)->column('mid');
                    $type = '0';
                }
                foreach ($this->worker->connections as $con) {
                    if (isset($con->aid) && $con->aid == $aid && $con->ctype == 'kehu' && in_array($con->mid, $mids)) {
                        $con->send(json_encode(['type' => 'peisong', 'data' => ['title' => $title, 'desc' => $desc, 'type' => $type]]));
                    }
                }
            }
            $connection->send(json_encode(['type' => 'response', 'data' => ['status' => 1]]));
        }
        if ($res['type'] == 'peisong_jiedan') { //配送单被接单
            $data = $res['data'];
            if ($data) {
                $aid = $data['aid'];
                $mid = $data['mid'];
                $psorderid = $data['psorderid'];
                $psorder = Db::name('peisong_order')->where('id', $psorderid)->find();
                $mids = Db::name('peisong_user')->where('aid', $aid)->where('status', 1)->where('mid', '<>', $mid)->column('mid');
                foreach ($this->worker->connections as $con) {
                    if (isset($con->aid) && $con->aid == $aid && $con->ctype == 'kehu' && in_array($con->mid, $mids)) {
                        $con->send(json_encode(['type' => 'peisong_jiedan', 'data' => $data]));
                    }
                }
            }
            $connection->send(json_encode(['type' => 'response', 'data' => ['status' => 1]]));
        }

        if ($res['type'] == 'worker_paidan') { //预约服务发配送单
            $data = $res['data'];
            if ($data) {
                $aid = $data['aid'];
                $worker_orderid = $data['worker_orderid'];
                $worker_order = Db::name('yuyue_worker_order')->where('id', $worker_orderid)->find();
                $binfo = json_decode($worker_order['binfo'], true);
                $orderinfo = json_decode($worker_order['orderinfo'], true);

                $desc = $binfo['name'] . '->' . $orderinfo['address'];
                if ($worker_order['worker_id']) {
                    $title = '有新的订单';
                    $mids = Db::name('yuyue_worker')->where('aid', $aid)->where('id', $worker_order['worker_id'])->column('mid');
                    $type = '1';
                } else {
                    $title = '有新的配送订单待接单';
                    $mids = Db::name('yuyue_worker')->where('aid', $aid)->where('status', 1)->column('mid');
                    $type = '0';
                }
                foreach ($this->worker->connections as $con) {
                    if (isset($con->aid) && $con->aid == $aid && $con->ctype == 'kehu' && in_array($con->mid, $mids)) {
                        $con->send(json_encode(['type' => 'worker_paidan', 'data' => ['title' => $title, 'desc' => $desc, 'type' => $type]]));
                    }
                }
            }
            $connection->send(json_encode(['type' => 'response', 'data' => ['status' => 1]]));
        }
        if ($res['type'] == 'worker_jiedan') { //预约服务配送单被接单
            $data = $res['data'];
            if ($data) {
                $aid = $data['aid'];
                $mid = $data['mid'];
                $worker_orderid = $data['worker_orderid'];
                $psorder = Db::name('yuyue_worker_order')->where('id', $worker_orderid)->find();
                $mids = Db::name('yuyue_worker')->where('aid', $aid)->where('status', 1)->where('mid', '<>', $mid)->column('mid');
                foreach ($this->worker->connections as $con) {
                    if (isset($con->aid) && $con->aid == $aid && $con->ctype == 'kehu' && in_array($con->mid, $mids)) {
                        $con->send(json_encode(['type' => 'worker_jiedan', 'data' => $data]));
                    }
                }
            }
            $connection->send(json_encode(['type' => 'response', 'data' => ['status' => 1]]));
        }

        if ($res['type'] == 'restaurant_queue') {
            $data = $res['data'];
            if ($res['token'] != md5(md5($authtoken . $data['aid'] . $data['bid']))) {
                Log::write('token校验失败');
                return;
            }
            Log::write($data);
            $connection->ctype = 'restaurant_queue';
            $connection->aid = $data['aid'];
            $connection->bid = $data['bid'];
            $connection->send(json_encode(['type' => 'response', 'data' => ['status' => 1]]));
        }
        if ($res['type'] == 'restaurant_queue_callno') {
            $data = $res['data'];
            if ($res['token'] != md5(md5($authtoken . $data['aid'] . $data['bid']))) {
                Log::write('token校验失败' . $authtoken . $data['aid'] . $data['bid']);
                return;
            }
            Log::write($data);
            foreach ($this->worker->connections as $con) {
                if ($con->ctype == 'restaurant_queue' && $con->aid == $data['aid'] && $con->bid == $data['bid']) {
                    $con->send(json_encode(['type' => 'restaurant_queue_callno', 'data' => ['call_id' => $data['call_id'], 'call_no' => $data['call_no']]]));
                }
            }
            $connection->send(json_encode(['type' => 'response', 'data' => ['status' => 1]]));
        }
        if ($res['type'] == 'restaurant_queue_add') {
            $data = $res['data'];
            if ($res['token'] != md5(md5($authtoken . $data['aid'] . $data['bid']))) {
                Log::write('token校验失败' . $authtoken . $data['aid'] . $data['bid']);
                return;
            }
            Log::write($data);
            foreach ($this->worker->connections as $con) {
                if ($con->ctype == 'restaurant_queue' && $con->aid == $data['aid'] && $con->bid == $data['bid']) {
                    $con->send(json_encode(['type' => 'restaurant_queue_add', 'data' => ['queue_id' => $data['queue_id'], 'queue_no' => $data['queue_no']]]));
                }
            }
            $connection->send(json_encode(['type' => 'response', 'data' => ['status' => 1]]));
        }
        if ($res['type'] == 'restaurant_queue_cancel') {
            $data = $res['data'];
            if ($res['token'] != md5(md5($authtoken . $data['aid'] . $data['bid']))) {
                Log::write('token校验失败' . $authtoken . $data['aid'] . $data['bid']);
                return;
            }
            Log::write($data);
            foreach ($this->worker->connections as $con) {
                if ($con->ctype == 'restaurant_queue' && $con->aid == $data['aid'] && $con->bid == $data['bid']) {
                    $con->send(json_encode(['type' => 'restaurant_queue_cancel', 'data' => ['queue_id' => $data['queue_id'], 'queue_no' => $data['queue_no']]]));
                }
            }
            $connection->send(json_encode(['type' => 'response', 'data' => ['status' => 1]]));
        }
        $connection->send(json_encode(['type' => 'response', 'data' => ['status' => 1]]));
    }

    function send_text($aid, $platform, $content, $openid)
    {
        $access_token = \app\common\Wechat::access_token($aid, $platform);
        $url = 'https://api.weixin.qq.com/cgi-bin/message/custom/send?access_token=' . $access_token;
        $data = array();
        $data['touser'] = trim($openid);
        $data['msgtype'] = 'text';
        $data['text'] = array('content' => $content);
        $rs = curl_post($url, jsonEncode($data));
        $rs = json_decode($rs, true);
        if ($rs['errcode'] != 0) {
            return ['status' => 0, 'msg' => \app\common\Wechat::geterror($rs)];
        } else {
            return ['status' => 1, 'msg' => '发送成功'];
        }
    }

    function send_image($aid, $platform, $picurl, $openid)
    {
        $access_token = \app\common\Wechat::access_token($aid, $platform);
        $media_id = \app\common\Wechat::pictomedia($aid, $platform, $picurl);
        $url = 'https://api.weixin.qq.com/cgi-bin/message/custom/send?access_token=' . $access_token;
        $data = array();
        $data['touser'] = trim($openid);
        $data['msgtype'] = 'image';
        $data['image'] = array('media_id' => $media_id);
        //Log::write($data);
        //Log::write($platform);
        $rs = curl_post($url, jsonEncode($data));
        $rs = json_decode($rs, true);
        //Log::write($rs);
        if ($rs['errcode'] != 0) {
            return ['status' => 0, 'msg' => \app\common\Wechat::geterror($rs)];
        } else {
            return ['status' => 1, 'msg' => '发送成功'];
        }
    }

    /**
     * 当连接建立时触发的回调函数
     * @param $connection
     */
    public function onConnect($connection)
    {

    }

    /**
     * 当连接断开时触发的回调函数
     * @param $connection
     */
    public function onClose($connection)
    {

    }

    /**
     * 当客户端的连接上发生错误时触发
     * @param $connection
     * @param $code
     * @param $msg
     */
    public function onError($connection, $code, $msg)
    {
        echo "error $code $msg\n";
    }

    /**
     * 每个进程启动
     * @param $worker
     */
    public function onWorkerStart($worker)
    {

    }
}

/*
启动
以debug（调试）方式启动
php server.php start

以daemon（守护进程）方式启动
php server.php start -d

停止
php server.php stop

重启
php server.php restart

平滑重启
php server.php reload

查看状态
php server.php status

查看连接状态（需要Workerman版本>=3.5.0）
php server.php connections
*/