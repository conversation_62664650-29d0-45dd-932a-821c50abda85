<?php

namespace app\common;

use think\facade\Db;

class Wxpay
{
    //创建微支付 微信公众号
    public static function build_mp($aid, $bid, $mid, $title, $ordernum, $price, $tablename, $notify_url = '')
    {
        if (!$notify_url) $notify_url = PRE_URL . '/notify.php';
        $member = Db::name('member')->where('id', $mid)->find();
        $package = array();
        $appinfo = \app\common\System::appinfo($aid, 'mp');
        $appid = $appinfo['appid'];
        $openid = $member[platform . 'openid'];

        $isbusinesspay = false;
        if ($bid > 0) {
            $bset = Db::name('business_sysset')->where('aid', $aid)->find();
            if ($bset['wxfw_status'] == 1) {
                $business = Db::name('business')->where('id', $bid)->find();
                if ($business['wxpayst'] == 1 && $business['wxpay_submchid']) {
                    $isbusinesspay = true;
                    $package['appid'] = $bset['wxfw_appid'];
                    $package['mch_id'] = $bset['wxfw_mchid'];
                    $package['sub_appid'] = $appid;
                    $package['sub_openid'] = $openid;
                    $package['sub_mch_id'] = $business['wxpay_submchid'];
                    $mchkey = $bset['wxfw_mchkey'];

                    $chouchengmoney = 0;
                    if ($business['feepercent'] > 0) {
                        $chouchengmoney = floatval($business['feepercent']) * 0.01 * $price;
                    }
                    if ($chouchengmoney > 0.01 && $price * 0.3 >= $chouchengmoney) { //需要分账
                        $package['profit_sharing'] = 'Y';
                    }
                    $package['attach'] = $aid . ':' . $tablename . ':mp:' . $bid;
                }
            }
        }
        if (!$isbusinesspay) {
            if ($appinfo['wxpay_type'] == 0) {
                $package['appid'] = $appid;
                $package['mch_id'] = $appinfo['wxpay_mchid'];
                $package['openid'] = $openid;
                $mchkey = $appinfo['wxpay_mchkey'];
            } elseif ($appinfo['wxpay_type'] == 1) {
                $dbwxpayset = Db::name('sysset')->where('name', 'wxpayset')->value('value');
                $dbwxpayset = json_decode($dbwxpayset, true);
                if (!$dbwxpayset) {
                    return ['status' => 0, 'msg' => '未配置服务商微信支付信息'];
                }
                $package['appid'] = $dbwxpayset['appid'];
                $package['sub_appid'] = $appid;
                $package['sub_openid'] = $openid;
                $package['mch_id'] = $dbwxpayset['mchid'];
                $package['sub_mch_id'] = $appinfo['wxpay_sub_mchid'];
                $mchkey = $dbwxpayset['mchkey'];

                $chouchengmoney = 0;
                $admindata = Db::name('admin')->where('id', aid)->find();
                if ($admindata['chouchengset'] == 0) { //默认抽成
                    if ($dbwxpayset && $dbwxpayset['chouchengset'] != 0) {
                        if ($dbwxpayset['chouchengset'] == 1) {
                            $chouchengmoney = floatval($dbwxpayset['chouchengrate']) * 0.01 * $price;
                            if ($dbwxpayset['chouchengmin'] && $chouchengmoney < floatval($dbwxpayset['chouchengmin'])) {
                                $chouchengmoney = floatval($dbwxpayset['chouchengmin']);
                            }
                        } else {
                            $chouchengmoney = floatval($dbwxpayset['chouchengmoney']);
                        }
                    }
                } elseif ($admindata['chouchengset'] == 1) { //按比例抽成
                    $chouchengmoney = floatval($admindata['chouchengrate']) * 0.01 * $price;
                    if ($chouchengmoney < floatval($admindata['chouchengmin'])) {
                        $chouchengmoney = floatval($admindata['chouchengmin']);
                    }
                } elseif ($admindata['chouchengset'] == 2) { //按固定金额抽成
                    $chouchengmoney = floatval($admindata['chouchengmoney']);
                }
                if ($chouchengmoney > 0 && $price * 0.3 >= $chouchengmoney) { //需要分账
                    $package['profit_sharing'] = 'Y';
                }
            } elseif ($appinfo['wxpay_type'] == 3) {
                $rs = \app\custom\Sxpay::build_mp($aid, $bid, $mid, $title, $ordernum, $price, $tablename, $notify_url);
                //\think\facade\Log::write($rs);
                return $rs;
            }
            $package['attach'] = $aid . ':' . $tablename . ':mp';
        }
        $package['nonce_str'] = random(8);
        $package['body'] = $title;
        $package['out_trade_no'] = $ordernum;
        $package['total_fee'] = $price * 100;
        //$package['spbill_create_ip'] = CLIENT_IP;
        //$package['time_start'] = date('YmdHis', TIMESTAMP);
        //$package['time_expire'] = date('YmdHis', TIMESTAMP + 600);
        $package['notify_url'] = $notify_url;
        $package['trade_type'] = 'JSAPI';
        ksort($package, SORT_STRING);
        $string1 = '';
        foreach ($package as $key => $v) {
            if (empty($v)) {
                continue;
            }
            $string1 .= "{$key}={$v}&";
        }
        $string1 .= "key=" . $mchkey;
        $package['sign'] = strtoupper(md5($string1));
        $dat = array2xml($package);
        //dump($price);
        //dump($package);
        //dump($dat);
        //dump($mchkey);
        $response = request_post('https://api.mch.weixin.qq.com/pay/unifiedorder', $dat);

        $xml = @simplexml_load_string($response, 'SimpleXMLElement', LIBXML_NOCDATA);
        if (strval($xml->return_code) == 'FAIL') {
            return ['status' => 0, 'msg' => strval($xml->return_msg)];
        }
        if (strval($xml->result_code) == 'FAIL') {
            return ['status' => 0, 'msg' => strval($xml->err_code_des)];
        }
        $prepayid = $xml->prepay_id;
        $wOpt = [];
        if (platform == 'wx') {
            $wOpt['appId'] = $appid;
        } else {
            $wOpt['appId'] = $package['appid'];
        }
        $wOpt['timeStamp'] = time() . "";
        $wOpt['nonceStr'] = random(8);
        $wOpt['package'] = 'prepay_id=' . $prepayid;
        $wOpt['signType'] = 'MD5';
        ksort($wOpt, SORT_STRING);
        foreach ($wOpt as $key => $v) {
            $string .= "{$key}={$v}&";
        }
        $string .= "key=" . $mchkey;
        $wOpt['paySign'] = strtoupper(md5($string));
        return ['status' => 1, 'data' => $wOpt];
    }

    //创建微支付 微信小程序
    public static function build_wx($aid, $bid, $mid, $title, $ordernum, $price, $tablename, $notify_url = '')
    {
        if (!$notify_url) $notify_url = PRE_URL . '/notify.php';
        $time = time();
        $member = Db::name('member')->where('id', $mid)->find();
        $package = array();
        $appinfo = \app\common\System::appinfo($aid, 'wx');
        $appid = $appinfo['appid'];
        $openid = $member[platform . 'openid'];

        if ($tablename == 'shop') {
            $order_detail = [];
            $order = Db::name('shop_order')->where('aid', $aid)->where('ordernum', $ordernum)->find();
            if ($order['fromwxvideo'] == 1) {
                if (!$order['wxvideo_order_id']) {
                    $rs = \app\common\Wxvideo::createorder($order['id']);
                    if ($rs['status'] == 0) {
                        return $rs;
                    }
                }
                //生成支付参数
                $url = 'https://api.weixin.qq.com/shop/order/getpaymentparams?access_token=' . \app\common\Wechat::access_token($aid, 'wx');
                $rs = curl_post($url, jsonEncode(['out_order_id' => strval($order['id']), 'openid' => $openid]));
                $rs = json_decode($rs, true);
                if ($rs['errcode'] == 0 && $rs['payment_params']) {
                    $wOpt = $rs['payment_params'];
                    return ['status' => 1, 'data' => $wOpt, 'fromwxvideo' => 1];
                } else {
                    return ['status' => 0, 'msg' => \app\common\Wechat::geterror($rs)];
                }
            }
        }

        $isbusinesspay = false;
        if ($bid > 0) {
            $bset = Db::name('business_sysset')->where('aid', $aid)->find();
            if ($bset['wxfw_status'] == 1) {
                $business = Db::name('business')->where('id', $bid)->find();
                if ($business['wxpayst'] == 1 && $business['wxpay_submchid']) {
                    $isbusinesspay = true;
                    $package['appid'] = $bset['wxfw_appid'];
                    $package['mch_id'] = $bset['wxfw_mchid'];
                    $package['sub_appid'] = $appid;
                    $package['sub_openid'] = $openid;
                    $package['sub_mch_id'] = $business['wxpay_submchid'];
                    $mchkey = $bset['wxfw_mchkey'];

                    $chouchengmoney = 0;
                    if ($business['feepercent'] > 0) {
                        $chouchengmoney = floatval($business['feepercent']) * 0.01 * $price;
                    }
                    if ($chouchengmoney > 0.01 && $price * 0.3 >= $chouchengmoney) { //需要分账
                        $package['profit_sharing'] = 'Y';
                    }
                    $package['attach'] = $aid . ':' . $tablename . ':wx:' . $bid;
                }
            }
        }

        if (!$isbusinesspay) {
            if ($appinfo['wxpay_type'] == 0) {
                $package['appid'] = $appid;
                $package['mch_id'] = $appinfo['wxpay_mchid'];
                $package['openid'] = $openid;
                $mchkey = $appinfo['wxpay_mchkey'];
            } elseif ($appinfo['wxpay_type'] == 3) {
                $rs = \app\custom\Sxpay::build_wx($aid, $bid, $mid, $title, $ordernum, $price, $tablename, $notify_url);
                return $rs;
            } else {
                $dbwxpayset = Db::name('sysset')->where('name', 'wxpayset')->value('value');
                $dbwxpayset = json_decode($dbwxpayset, true);
                if (!$dbwxpayset) {
                    return ['status' => 0, 'msg' => '未配置服务商微信支付信息'];
                }
                $package['appid'] = $dbwxpayset['appid'];
                $package['sub_appid'] = $appid;
                $package['sub_openid'] = $openid;
                $package['mch_id'] = $dbwxpayset['mchid'];
                $package['sub_mch_id'] = $appinfo['wxpay_sub_mchid'];
                $mchkey = $dbwxpayset['mchkey'];

                $chouchengmoney = 0;
                $admindata = Db::name('admin')->where('id', aid)->find();
                if ($admindata['chouchengset'] == 0) { //默认抽成
                    if ($dbwxpayset && $dbwxpayset['chouchengset'] != 0) {
                        if ($dbwxpayset['chouchengset'] == 1) {
                            $chouchengmoney = floatval($dbwxpayset['chouchengrate']) * 0.01 * $price;
                            if ($dbwxpayset['chouchengmin'] && $chouchengmoney < floatval($dbwxpayset['chouchengmin'])) {
                                $chouchengmoney = floatval($dbwxpayset['chouchengmin']);
                            }
                        } else {
                            $chouchengmoney = floatval($dbwxpayset['chouchengmoney']);
                        }
                    }
                } elseif ($admindata['chouchengset'] == 1) { //按比例抽成
                    $chouchengmoney = floatval($admindata['chouchengrate']) * 0.01 * $price;
                    if ($chouchengmoney < floatval($admindata['chouchengmin'])) {
                        $chouchengmoney = floatval($admindata['chouchengmin']);
                    }
                } elseif ($admindata['chouchengset'] == 2) { //按固定金额抽成
                    $chouchengmoney = floatval($admindata['chouchengmoney']);
                }
                if ($chouchengmoney > 0 && $price * 0.3 >= $chouchengmoney) { //需要分账
                    $package['profit_sharing'] = 'Y';
                }
            }
            $package['attach'] = $aid . ':' . $tablename . ':wx';
        }
        $package['nonce_str'] = random(8);
        $package['body'] = $title;
        $package['out_trade_no'] = $ordernum;
        $package['total_fee'] = $price * 100;
        //$package['spbill_create_ip'] = CLIENT_IP;
        //$package['time_start'] = date('YmdHis', TIMESTAMP);
        //$package['time_expire'] = date('YmdHis', TIMESTAMP + 600);
        $package['notify_url'] = $notify_url;
        $package['trade_type'] = 'JSAPI';
        ksort($package, SORT_STRING);
        $string1 = '';
        foreach ($package as $key => $v) {
            if (empty($v)) {
                continue;
            }
            $string1 .= "{$key}={$v}&";
        }
        $string1 .= "key=" . $mchkey;
        $package['sign'] = strtoupper(md5($string1));
        $dat = array2xml($package);
        //dump($price);
        //dump($package);
        //dump($dat);
        //dump($mchkey);
        $response = request_post('https://api.mch.weixin.qq.com/pay/unifiedorder', $dat);

        $xml = @simplexml_load_string($response, 'SimpleXMLElement', LIBXML_NOCDATA);
        if (strval($xml->return_code) == 'FAIL') {
            return ['status' => 0, 'msg' => strval($xml->return_msg)];
        }
        if (strval($xml->result_code) == 'FAIL') {
            return ['status' => 0, 'msg' => strval($xml->err_code_des)];
        }
        $prepayid = strval($xml->prepay_id);
        $wOpt = [];
        if (platform == 'wx') {
            $wOpt['appId'] = $appid;
        } else {
            $wOpt['appId'] = $package['appid'];
        }
        $wOpt['timeStamp'] = $time . "";
        $wOpt['nonceStr'] = random(8);
        $wOpt['package'] = 'prepay_id=' . $prepayid;
        $wOpt['signType'] = 'MD5';
        ksort($wOpt, SORT_STRING);
        foreach ($wOpt as $key => $v) {
            $string .= "{$key}={$v}&";
        }
        $string .= "key=" . $mchkey;
        $wOpt['paySign'] = strtoupper(md5($string));

        return ['status' => 1, 'data' => $wOpt];
    }

    //创建微支付参数H5
    public static function build_h5($aid, $bid, $mid, $title, $ordernum, $price, $tablename, $notify_url = '')
    {
        if (!$notify_url) $notify_url = PRE_URL . '/notify.php';
        $set = Db::name('admin_set')->where('aid', $aid)->find();
        $package = array();

        $appinfo = \app\common\System::appinfo($aid, 'h5');
        $appid = $appinfo['appid'];

        $isbusinesspay = false;
        if ($bid > 0) {
            $bset = Db::name('business_sysset')->where('aid', $aid)->find();
            if ($bset['wxfw_status'] == 1) {
                $business = Db::name('business')->where('id', $bid)->find();
                if ($business['wxpayst'] == 1 && $business['wxpay_submchid']) {
                    $isbusinesspay = true;
                    $package['appid'] = $bset['wxfw_appid'];
                    $package['mch_id'] = $bset['wxfw_mchid'];
                    if ($appid) {
                        $package['sub_appid'] = $appid;
                    }
                    $package['sub_mch_id'] = $business['wxpay_submchid'];
                    $mchkey = $bset['wxfw_mchkey'];

                    $chouchengmoney = 0;
                    if ($business['feepercent'] > 0) {
                        $chouchengmoney = floatval($business['feepercent']) * 0.01 * $price;
                    }
                    if ($chouchengmoney > 0.01 && $price * 0.3 >= $chouchengmoney) { //需要分账
                        $package['profit_sharing'] = 'Y';
                    }
                    $package['attach'] = $aid . ':' . $tablename . ':h5:' . $bid;
                }
            }
        }

        if (!$isbusinesspay) {
            if ($appinfo['wxpay_type'] == 0) {
                $package['appid'] = $appid;
                $package['mch_id'] = $appinfo['wxpay_mchid'];
                $mchkey = $appinfo['wxpay_mchkey'];
            } else {
                $dbwxpayset = Db::name('sysset')->where('name', 'wxpayset')->value('value');
                $dbwxpayset = json_decode($dbwxpayset, true);
                if (!$dbwxpayset) {
                    return ['status' => 0, 'msg' => '未配置服务商微信支付信息'];
                }
                $package['appid'] = $dbwxpayset['appid'];
                if ($appid) {
                    $package['sub_appid'] = $appid;
                }
                $package['mch_id'] = $dbwxpayset['mchid'];
                $package['sub_mch_id'] = $appinfo['wxpay_sub_mchid'];
                $mchkey = $dbwxpayset['mchkey'];

                $chouchengmoney = 0;
                $admindata = Db::name('admin')->where('id', aid)->find();
                if ($admindata['chouchengset'] == 0) { //默认抽成
                    if ($dbwxpayset && $dbwxpayset['chouchengset'] != 0) {
                        if ($dbwxpayset['chouchengset'] == 1) {
                            $chouchengmoney = floatval($dbwxpayset['chouchengrate']) * 0.01 * $price;
                            if ($chouchengmoney < floatval($dbwxpayset['chouchengmin'])) {
                                $chouchengmoney = floatval($dbwxpayset['chouchengmin']);
                            }
                        } else {
                            $chouchengmoney = floatval($dbwxpayset['chouchengmoney']);
                        }
                    }
                } elseif ($admindata['chouchengset'] == 1) { //按比例抽成
                    $chouchengmoney = floatval($admindata['chouchengrate']) * 0.01 * $price;
                    if ($chouchengmoney < floatval($admindata['chouchengmin'])) {
                        $chouchengmoney = floatval($admindata['chouchengmin']);
                    }
                } elseif ($admindata['chouchengset'] == 2) { //按固定金额抽成
                    $chouchengmoney = floatval($admindata['chouchengmoney']);
                }
                if ($chouchengmoney > 0 && $price * 0.3 >= $chouchengmoney) { //需要分账
                    $package['profit_sharing'] = 'Y';
                }
            }
            $package['attach'] = $aid . ':' . $tablename . ':h5';
        }
        $package['nonce_str'] = random(8);
        $package['body'] = $title;
        $package['out_trade_no'] = $ordernum;
        $package['total_fee'] = $price * 100;
        $package['spbill_create_ip'] = request()->ip();
        //$package['time_start'] = date('YmdHis', TIMESTAMP);
        //$package['time_expire'] = date('YmdHis', TIMESTAMP + 600);
        $package['notify_url'] = $notify_url;
        $package['trade_type'] = 'MWEB';
        $package['scene_info'] = '{"h5_info": {"type":"Wap","wap_url": "' . PRE_URL . '","wap_name": "' . $set['name'] . '"}}';
        //dump($package);
        ksort($package, SORT_STRING);
        $string1 = '';
        foreach ($package as $key => $v) {
            if (empty($v)) {
                continue;
            }
            $string1 .= "{$key}={$v}&";
        }
        $string1 .= "key=" . $mchkey;
        $package['sign'] = strtoupper(md5($string1));
        $dat = array2xml($package);
        //var_dump($string1);
        //var_dump($package);
        //var_dump($dat);
        $response = request_post('https://api.mch.weixin.qq.com/pay/unifiedorder', $dat);

        $xml = @simplexml_load_string($response, 'SimpleXMLElement', LIBXML_NOCDATA);
        if (strval($xml->return_code) == 'FAIL') {
            return ['status' => 0, 'msg' => strval($xml->return_msg)];
        }
        if (strval($xml->result_code) == 'FAIL') {
            return ['status' => 0, 'msg' => strval($xml->err_code_des)];
        }
        //var_dump($xml);
        //var_dump($xml->mweb_url.'');
        //$prepayid = $xml->prepay_id;
        $wOpt = [];
        $wOpt['app_id'] = $set['ttpayid'];
        $wOpt['sign_type'] = 'MD5';
        $wOpt['out_order_no'] = $ordernum;
        $wOpt['merchant_id'] = $set['ttmchid'];
        $wOpt['timestamp'] = time() . "";
        $wOpt['product_code'] = 'pay';
        $wOpt['payment_type'] = 'direct';
        $wOpt['total_amount'] = $price * 100;
        $wOpt['trade_type'] = 'H5';
        $wOpt['uid'] = mid;
        $wOpt['version'] = '2.0';
        $wOpt['currency'] = 'CNY';
        $wOpt['subject'] = $title;
        $wOpt['body'] = $title;
        $wOpt['trade_time'] = time() . "";
        $wOpt['valid_time'] = '300';
        $wOpt['notify_url'] = $notify_url;
        $wOpt['wx_url'] = strval($xml->mweb_url);
        $wOpt['wx_type'] = 'MWEB';
        //$wOpt['alipay_url'] = '';
        ksort($wOpt, SORT_STRING);
        foreach ($wOpt as $key => $v) {
            $string .= "{$key}={$v}&";
        }
        $string = rtrim($string, '&');
        $string .= "" . $set['ttpaysecret'];
        $wOpt['sign'] = md5($string);
        return ['status' => 1, 'data' => $wOpt];
    }

    //创建微支付参数H5 QQ小程序
    public static function build_qq($aid, $bid, $mid, $title, $ordernum, $price, $tablename, $notify_url = '')
    {
        if (!$notify_url) $notify_url = PRE_URL . '/notify.php';
        $set = Db::name('admin_set')->where('aid', $aid)->find();
        $package = array();

        $appinfo = \app\common\System::appinfo($aid, 'qq');
        $appid = $appinfo['wxpay_appid'];

        $isbusinesspay = false;
        if ($bid > 0) {
            $bset = Db::name('business_sysset')->where('aid', $aid)->find();
            if ($bset['wxfw_status'] == 1) {
                $business = Db::name('business')->where('id', $bid)->find();
                if ($business['wxpayst'] == 1 && $business['wxpay_submchid']) {
                    $isbusinesspay = true;
                    $package['appid'] = $bset['wxfw_appid'];
                    $package['mch_id'] = $bset['wxfw_mchid'];
                    if ($appid) {
                        $package['sub_appid'] = $appid;
                    }
                    $package['sub_mch_id'] = $business['wxpay_submchid'];
                    $mchkey = $bset['wxfw_mchkey'];

                    $chouchengmoney = 0;
                    if ($business['feepercent'] > 0) {
                        $chouchengmoney = floatval($business['feepercent']) * 0.01 * $price;
                    }
                    if ($chouchengmoney > 0.01 && $price * 0.3 >= $chouchengmoney) { //需要分账
                        $package['profit_sharing'] = 'Y';
                    }
                    $package['attach'] = $aid . ':' . $tablename . ':qq:' . $bid;
                }
            }
        }

        if (!$isbusinesspay) {
            if ($appinfo['wxpay_type'] == 0) {
                $package['appid'] = $appid;
                $package['mch_id'] = $appinfo['wxpay_mchid'];
                $mchkey = $appinfo['wxpay_mchkey'];
            } else {
                $dbwxpayset = Db::name('sysset')->where('name', 'wxpayset')->value('value');
                $dbwxpayset = json_decode($dbwxpayset, true);
                if (!$dbwxpayset) {
                    return ['status' => 0, 'msg' => '未配置服务商微信支付信息'];
                }
                $package['appid'] = $dbwxpayset['appid'];
                if ($appid) {
                    $package['sub_appid'] = $appid;
                }
                $package['mch_id'] = $dbwxpayset['mchid'];
                $package['sub_mch_id'] = $appinfo['wxpay_sub_mchid'];
                $mchkey = $dbwxpayset['mchkey'];

                $chouchengmoney = 0;
                $admindata = Db::name('admin')->where('id', aid)->find();
                if ($admindata['chouchengset'] == 0) { //默认抽成
                    if ($dbwxpayset && $dbwxpayset['chouchengset'] != 0) {
                        if ($dbwxpayset['chouchengset'] == 1) {
                            $chouchengmoney = floatval($dbwxpayset['chouchengrate']) * 0.01 * $price;
                            if ($chouchengmoney < floatval($dbwxpayset['chouchengmin'])) {
                                $chouchengmoney = floatval($dbwxpayset['chouchengmin']);
                            }
                        } else {
                            $chouchengmoney = floatval($dbwxpayset['chouchengmoney']);
                        }
                    }
                } elseif ($admindata['chouchengset'] == 1) { //按比例抽成
                    $chouchengmoney = floatval($admindata['chouchengrate']) * 0.01 * $price;
                    if ($chouchengmoney < floatval($admindata['chouchengmin'])) {
                        $chouchengmoney = floatval($admindata['chouchengmin']);
                    }
                } elseif ($admindata['chouchengset'] == 2) { //按固定金额抽成
                    $chouchengmoney = floatval($admindata['chouchengmoney']);
                }
                if ($chouchengmoney > 0 && $price * 0.3 >= $chouchengmoney) { //需要分账
                    $package['profit_sharing'] = 'Y';
                }
            }
            $package['attach'] = $aid . ':' . $tablename . ':qq';
        }
        $package['nonce_str'] = random(8);
        $package['body'] = $title;
        $package['out_trade_no'] = $ordernum;
        $package['total_fee'] = $price * 100;
        $package['spbill_create_ip'] = request()->ip();
        //$package['time_start'] = date('YmdHis', TIMESTAMP);
        //$package['time_expire'] = date('YmdHis', TIMESTAMP + 600);
        $package['notify_url'] = 'https://api.q.qq.com/wxpay/notify';
        $package['trade_type'] = 'MWEB';
        $package['scene_info'] = '{"h5_info": {"type":"Wap","wap_url": "' . PRE_URL . '","wap_name": "' . $set['name'] . '"}}';
        //dump($package);
        ksort($package, SORT_STRING);
        $string1 = '';
        foreach ($package as $key => $v) {
            if (empty($v)) {
                continue;
            }
            $string1 .= "{$key}={$v}&";
        }
        $string1 .= "key=" . $mchkey;
        $package['sign'] = strtoupper(md5($string1));
        $dat = array2xml($package);
        //var_dump($string1);
        //var_dump($package);
        //var_dump($dat);
        $response = request_post('https://api.q.qq.com/wxpay/unifiedorder?appid=' . $appinfo['appid'] . '&access_token=' . \app\common\Qq::access_token($aid) . '&real_notify_url=' . urlencode($notify_url), $dat);
        //var_dump($response);
        $xml = @simplexml_load_string($response, 'SimpleXMLElement', LIBXML_NOCDATA);
        if (strval($xml->return_code) == 'FAIL') {
            return ['status' => 0, 'msg' => strval($xml->return_msg)];
        }
        if (strval($xml->result_code) == 'FAIL') {
            return ['status' => 0, 'msg' => strval($xml->err_code_des)];
        }
        //var_dump($xml);
        //var_dump($xml->mweb_url.'');
        //$prepayid = $xml->prepay_id;
        $wOpt = [];
        $wOpt['wx_url'] = strval($xml->mweb_url);
        $wOpt['referer'] = PRE_URL;
        return ['status' => 1, 'data' => $wOpt];
    }

    public static function build_app($aid, $bid, $mid, $title, $ordernum, $price, $tablename, $notify_url = '')
    {
        if (!$notify_url) $notify_url = PRE_URL . '/notify.php';
        $package = array();
        $appinfo = \app\common\System::appinfo($aid, 'app');
        $appid = $appinfo['appid'];

        $isbusinesspay = false;
        if ($bid > 0) {
            $bset = Db::name('business_sysset')->where('aid', $aid)->find();
            if ($bset['wxfw_status'] == 1) {
                $business = Db::name('business')->where('id', $bid)->find();
                if ($business['wxpayst'] == 1 && $business['wxpay_submchid']) {
                    $isbusinesspay = true;
                    $package['appid'] = $bset['wxfw_appid'];
                    $package['mch_id'] = $bset['wxfw_mchid'];
                    if ($appid) {
                        $package['sub_appid'] = $appid;
                    }
                    $package['sub_mch_id'] = $business['wxpay_submchid'];
                    $mchkey = $bset['wxfw_mchkey'];

                    $chouchengmoney = 0;
                    if ($business['feepercent'] > 0) {
                        $chouchengmoney = floatval($business['feepercent']) * 0.01 * $price;
                    }
                    if ($chouchengmoney > 0.01 && $price * 0.3 >= $chouchengmoney) { //需要分账
                        $package['profit_sharing'] = 'Y';
                    }
                    $package['attach'] = $aid . ':' . $tablename . ':app:' . $bid;
                }
            }
        }

        if (!$isbusinesspay) {
            if ($appinfo['wxpay_type'] == 0) {
                $package['appid'] = $appid;
                $package['mch_id'] = $appinfo['wxpay_mchid'];
                $mchkey = $appinfo['wxpay_mchkey'];
            } else {
                $dbwxpayset = Db::name('sysset')->where('name', 'wxpayset')->value('value');
                $dbwxpayset = json_decode($dbwxpayset, true);
                if (!$dbwxpayset) {
                    return ['status' => 0, 'msg' => '未配置服务商微信支付信息'];
                }
                $package['appid'] = $dbwxpayset['appid'];
                if ($appid) {
                    $package['sub_appid'] = $appid;
                }
                $package['mch_id'] = $dbwxpayset['mchid'];
                $package['sub_mch_id'] = $appinfo['wxpay_sub_mchid'];
                $mchkey = $dbwxpayset['mchkey'];

                $chouchengmoney = 0;
                $admindata = Db::name('admin')->where('id', aid)->find();
                if ($admindata['chouchengset'] == 0) { //默认抽成
                    if ($dbwxpayset && $dbwxpayset['chouchengset'] != 0) {
                        if ($dbwxpayset['chouchengset'] == 1) {
                            $chouchengmoney = floatval($dbwxpayset['chouchengrate']) * 0.01 * $price;
                            if ($chouchengmoney < floatval($dbwxpayset['chouchengmin'])) {
                                $chouchengmoney = floatval($dbwxpayset['chouchengmin']);
                            }
                        } else {
                            $chouchengmoney = floatval($dbwxpayset['chouchengmoney']);
                        }
                    }
                } elseif ($admindata['chouchengset'] == 1) { //按比例抽成
                    $chouchengmoney = floatval($admindata['chouchengrate']) * 0.01 * $price;
                    if ($chouchengmoney < floatval($admindata['chouchengmin'])) {
                        $chouchengmoney = floatval($admindata['chouchengmin']);
                    }
                } elseif ($admindata['chouchengset'] == 2) { //按固定金额抽成
                    $chouchengmoney = floatval($admindata['chouchengmoney']);
                }
                if ($chouchengmoney > 0 && $price * 0.3 >= $chouchengmoney) { //需要分账
                    $package['profit_sharing'] = 'Y';
                }
            }
            $package['attach'] = $aid . ':' . $tablename . ':app';
        }
        $package['nonce_str'] = random(8);
        $package['body'] = $title;
        $package['out_trade_no'] = $ordernum;
        $package['total_fee'] = $price * 100;
        $package['spbill_create_ip'] = request()->ip();
        //$package['time_start'] = date('YmdHis', TIMESTAMP);
        //$package['time_expire'] = date('YmdHis', TIMESTAMP + 600);
        $package['notify_url'] = $notify_url;
        $package['trade_type'] = 'APP';
        //dump($package);
        ksort($package, SORT_STRING);
        $string1 = '';
        foreach ($package as $key => $v) {
            if (empty($v)) {
                continue;
            }
            $string1 .= "{$key}={$v}&";
        }
        $string1 .= "key=" . $mchkey;
        $package['sign'] = strtoupper(md5($string1));
        $dat = array2xml($package);
        //var_dump($string1);
        //var_dump($package);
        //var_dump($dat);
        $response = request_post('https://api.mch.weixin.qq.com/pay/unifiedorder', $dat);

        $xml = @simplexml_load_string($response, 'SimpleXMLElement', LIBXML_NOCDATA);
        if (strval($xml->return_code) == 'FAIL') {
            return ['status' => 0, 'msg' => strval($xml->return_msg)];
        }
        if (strval($xml->result_code) == 'FAIL') {
            return ['status' => 0, 'msg' => strval($xml->err_code_des)];
        }
        //var_dump($xml);
        //var_dump($xml->mweb_url.'');
        $prepayid = strval($xml->prepay_id);
        $wOpt = [];
        $wOpt['appid'] = $appid;
        if ($appinfo['wxpay_type'] == 0) {
            $wOpt['partnerid'] = $appinfo['wxpay_mchid'];
        } else {
            $wOpt['partnerid'] = $appinfo['wxpay_sub_mchid'];
        }
        $wOpt['prepayid'] = $prepayid;
        $wOpt['package'] = 'Sign=WXPay';
        $wOpt['noncestr'] = random(8);
        $wOpt['timestamp'] = time() . "";
        //$wOpt['signType'] = 'MD5';
        ksort($wOpt, SORT_STRING);
        foreach ($wOpt as $key => $v) {
            $string .= "{$key}={$v}&";
        }
        $string .= "key=" . $mchkey;
        $wOpt['sign'] = strtoupper(md5($string));
        return ['status' => 1, 'data' => $wOpt];
    }

    //关闭订单
    public static function closeorder($aid, $ordernum, $platform)
    {
        $appinfo = \app\common\System::appinfo($aid, $platform);
        $appid = $appinfo['appid'];

        $package = [];
        $package['appid'] = $appid;
        $package['mch_id'] = $appinfo['wxpay_mchid'];
        $mchkey = $appinfo['wxpay_mchkey'];
        $package['out_trade_no'] = $ordernum;
        $package['nonce_str'] = random(8);
        ksort($package, SORT_STRING);
        $string1 = '';
        foreach ($package as $key => $v) {
            if (empty($v)) {
                continue;
            }
            $string1 .= "{$key}={$v}&";
        }
        $string1 .= "key=" . $mchkey;
        $package['sign'] = strtoupper(md5($string1));
        $dat = array2xml($package);
        $response = request_post('https://api.mch.weixin.qq.com/pay/closeorder', $dat);

        $xml = @simplexml_load_string($response, 'SimpleXMLElement', LIBXML_NOCDATA);
        if (strval($xml->return_code) == 'FAIL') {
            return ['status' => 0, 'msg' => strval($xml->return_msg)];
        }
        if (strval($xml->result_code) == 'FAIL') {
            return ['status' => 0, 'msg' => strval($xml->err_code_des)];
        }
        return ['status' => 1, 'msg' => '操作成功'];
    }

    //微信退款
    public static function refund($aid, $platform, $ordernum, $totalprice, $refundmoney, $refund_desc = '退款')
    {
        if (!$refund_desc) $refund_desc = '退款';
        $appinfo = \app\common\System::appinfo($aid, $platform);
        if ($platform == 'qq') $appinfo['appid'] = $appinfo['wxpay_appid'];
        //是否有分账 分账回退
        $paylog = Db::name('wxpay_log')->where('aid', $aid)->where('ordernum', $ordernum)->find();
        if ($paylog && $paylog['fenzhangmoney'] > 0 && $paylog['isfenzhang'] == 1) {
            if ($paylog['bid'] == 0) {
                $dbwxpayset = Db::name('sysset')->where('name', 'wxpayset')->value('value');
                $dbwxpayset = json_decode($dbwxpayset, true);
            } else {
                $bset = Db::name('business_sysset')->where('aid', $aid)->find();
                $dbwxpayset = [
                    'mchname' => $bset['wxfw_mchname'],
                    'appid' => $bset['wxfw_appid'],
                    'mchid' => $bset['wxfw_mchid'],
                    'mchkey' => $bset['wxfw_mchkey'],
                    'apiclient_cert' => $bset['wxfw_apiclient_cert'],
                    'apiclient_key' => $bset['wxfw_apiclient_key'],
                ];
            }
            $mchkey = $dbwxpayset['mchkey'];
            $sslcert = ROOT_PATH . $dbwxpayset['apiclient_cert'];
            $sslkey = ROOT_PATH . $dbwxpayset['apiclient_key'];
            $pars = array();
            $pars['mch_id'] = $dbwxpayset['mchid'];
            $pars['sub_mch_id'] = $paylog['sub_mchid'];
            $pars['appid'] = $dbwxpayset['appid'];
            $pars['nonce_str'] = random(32);
            $pars['out_order_no'] = $paylog['fz_ordernum'];
            $pars['out_return_no'] = 'R' . date('YmdHis') . rand(1000, 9999);
            $pars['return_account_type'] = 'MERCHANT_ID';
            $pars['return_account'] = $dbwxpayset['mchid'];
            $pars['return_amount'] = $paylog['fenzhangmoney'] * 100;
            $pars['description'] = $refund_desc;
            //$pars['sign_type'] = 'MD5';
            ksort($pars, SORT_STRING);
            $string1 = '';
            foreach ($pars as $k => $v) {
                $string1 .= "{$k}={$v}&";
            }
            $string1 .= "key=" . $mchkey;
            //$pars['sign'] = strtoupper(md5($string1));
            $pars['sign'] = strtoupper(hash_hmac("sha256", $string1, $mchkey));
            $xml = array2xml($pars);

            $client = new \GuzzleHttp\Client(['timeout' => 30, 'verify' => false]);
            $response = $client->request('POST', "https://api.mch.weixin.qq.com/secapi/pay/profitsharingreturn", ['body' => $xml, 'cert' => $sslcert, 'ssl_key' => $sslkey]);
            $info = $response->getBody()->getContents();

            $resp = (array)(simplexml_load_string($info, 'SimpleXMLElement', LIBXML_NOCDATA));
            if ($resp['return_code'] == 'SUCCESS' && $resp['result'] == 'SUCCESS') {
                Db::name('wxpay_log')->where('aid', $aid)->where('ordernum', $ordernum)->update(['isfenzhang' => 3]);
            }
        }

        $pars = array();

        if ($paylog['bid'] > 0) {
            $bset = Db::name('business_sysset')->where('aid', $aid)->find();
            $dbwxpayset = [
                'mchname' => $bset['wxfw_mchname'],
                'appid' => $bset['wxfw_appid'],
                'mchid' => $bset['wxfw_mchid'],
                'mchkey' => $bset['wxfw_mchkey'],
                'apiclient_cert' => $bset['wxfw_apiclient_cert'],
                'apiclient_key' => $bset['wxfw_apiclient_key'],
            ];
            $pars['appid'] = $dbwxpayset['appid'];
            $pars['sub_appid'] = $appinfo['appid'];
            $pars['mch_id'] = $dbwxpayset['mchid'];
            $pars['sub_mch_id'] = $paylog['sub_mchid'];
            $mchkey = $dbwxpayset['mchkey'];
            $sslcert = ROOT_PATH . $dbwxpayset['apiclient_cert'];
            $sslkey = ROOT_PATH . $dbwxpayset['apiclient_key'];
        } else {
            if ($appinfo['wxpay_type'] == 0) {
                $pars['appid'] = $appinfo['appid'];
                $pars['mch_id'] = $appinfo['wxpay_mchid'];
                $mchkey = $appinfo['wxpay_mchkey'];
                $sslcert = ROOT_PATH . $appinfo['wxpay_apiclient_cert'];
                $sslkey = ROOT_PATH . $appinfo['wxpay_apiclient_key'];
            } elseif ($appinfo['wxpay_type'] == 3) {
                $rs = \app\custom\Sxpay::refund($aid, $platform, $ordernum, $totalprice, $refundmoney, $refund_desc);
                return $rs;
            } else {
                $dbwxpayset = Db::name('sysset')->where('name', 'wxpayset')->value('value');
                $dbwxpayset = json_decode($dbwxpayset, true);
                $pars['appid'] = $dbwxpayset['appid'];
                $pars['sub_appid'] = $appinfo['appid'];
                $pars['mch_id'] = $dbwxpayset['mchid'];
                $pars['sub_mch_id'] = $appinfo['wxpay_sub_mchid'];
                $mchkey = $dbwxpayset['mchkey'];
                $sslcert = ROOT_PATH . $dbwxpayset['apiclient_cert'];
                $sslkey = ROOT_PATH . $dbwxpayset['apiclient_key'];
            }
        }
        $pars['nonce_str'] = random(32);
        $pars['out_trade_no'] = $ordernum;
        $pars['out_refund_no'] = $ordernum . '_' . rand(1000, 9999);
        $pars['total_fee'] = $totalprice * 100;
        $pars['refund_fee'] = $refundmoney * 100;
        $pars['refund_desc'] = $refund_desc;
        ksort($pars, SORT_STRING);
        $string1 = '';
        foreach ($pars as $k => $v) {
            $string1 .= "{$k}={$v}&";
        }
        $string1 .= "key=" . $mchkey;
        $pars['sign'] = strtoupper(md5($string1));
        $xml = array2xml($pars);

        $client = new \GuzzleHttp\Client(['timeout' => 30, 'verify' => false]);
        $response = $client->request('POST', "https://api.mch.weixin.qq.com/secapi/pay/refund", ['body' => $xml, 'cert' => $sslcert, 'ssl_key' => $sslkey]);
        $info = $response->getBody()->getContents();

        $resp = (array)(simplexml_load_string($info, 'SimpleXMLElement', LIBXML_NOCDATA));
        if ($resp['return_code'] == 'SUCCESS' && $resp['result_code'] == 'SUCCESS') {
            //记录
            $data = [];
            $data['aid'] = $aid;
            $data['mch_id'] = $pars['mch_id'];
            $data['ordernum'] = $ordernum;
            $data['out_refund_no'] = $pars['out_refund_no'];
            $data['totalprice'] = $totalprice;
            $data['refundmoney'] = $refundmoney;
            $data['createtime'] = date('Y-m-d H:i:s');
            $data['status'] = 1;
            $data['remark'] = $refund_desc;
            Db::name('wxrefund_log')->insert($data);
            if ($paylog) {
                Db::name('wxpay_log')->where('id', $paylog['id'])->inc('refund_money', $refundmoney)->update();
            }
            return ['status' => 1, 'msg' => '退款成功', 'resp' => $resp];
        } else {
            $msg = '未知错误';
            if ($resp['return_code'] == 'FAIL') {
                $msg = $resp['return_msg'];
            }
            if ($resp['result_code'] == 'FAIL') {
                $msg = $resp['err_code_des'];
            }
            //记录
            $data = [];
            $data['aid'] = $aid;
            $data['mch_id'] = $pars['mch_id'];
            $data['ordernum'] = $ordernum;
            $data['out_refund_no'] = $pars['out_refund_no'];
            $data['totalprice'] = $totalprice;
            $data['refundmoney'] = $refundmoney;
            $data['createtime'] = date('Y-m-d H:i:s');
            $data['status'] = 2;
            $data['remark'] = $refund_desc;
            $data['errmsg'] = $msg;
            Db::name('wxrefund_log')->insert($data);
            return ['status' => 0, 'msg' => $msg, 'resp' => $resp];
        }
    }

    //发红包
    public static function sendredpackage($aid, $mid, $platform, $money, $act_name = '微信红包', $send_name = '微信红包', $wishing = '恭喜发财', $remark = '微信红包', $scene_id = '')
    {
        if (!$aid || !$mid || !$money) return ['status' => 0, 'msg' => '参数错误'];

        if ($platform == 'wx') {
            $openid = Db::name('member')->where('id', $mid)->value('wxopenid');
            $url = 'https://api.mch.weixin.qq.com/mmpaymkttransfers/sendminiprogramhb';
            $appinfo = \app\common\System::appinfo($aid, 'wx');
        } else {
            $openid = Db::name('member')->where('id', $mid)->value('mpopenid');
            $url = "https://api.mch.weixin.qq.com/mmpaymkttransfers/sendredpack";
            $appinfo = \app\common\System::appinfo($aid, 'mp');
        }

        $package = array();
        $package['wxappid'] = $appinfo['appid'];
        $package['mch_id'] = $appinfo['wxpay_mchid'];
        $mchkey = $appinfo['wxpay_mchkey'];
        $sslcert = ROOT_PATH . $appinfo['wxpay_apiclient_cert'];
        $sslkey = ROOT_PATH . $appinfo['wxpay_apiclient_key'];
        //dump($sslkey);
        $ordernum = date('ymdHis') . $aid . rand(1000, 9999);
        $package['mch_billno'] = $ordernum;
        $package['send_name'] = $send_name;    //商户名称 红包发送者名称
        $package['re_openid'] = $openid;    //用户openid
        $package['total_amount'] = $money * 100;    //付款金额
        $package['total_num'] = 1;    //红包发放总人数
        $package['wishing'] = $wishing;//红包祝福语
        //$package['client_ip'] = '127.0.0.1';
        $package['act_name'] = mb_substr($act_name, 0, 30);//活动名称
        $package['remark'] = $remark;  //备注信息
        if ($scene_id) {
            $package['scene_id'] = $scene_id;  //场景id
        }
        if ($platform == 'wx') {
            $package['notify_way'] = 'MINI_PROGRAM_JSAPI';
        }
        $nonce_str = '';
        $str = '1234567890abcdefghijklmnopqrstuvwxyz';
        for ($i = 0; $i < 30; $i++) {
            $j = rand(0, 35);
            $nonce_str .= $str[$j];
        }
        $package['nonce_str'] = $nonce_str;//随机字符串，不长于32位
        ksort($package, SORT_STRING);
        $string1 = '';
        foreach ($package as $key => $v) {
            if (empty($v)) {
                continue;
            }
            $string1 .= "{$key}={$v}&";
        }
        $string1 .= "key={$mchkey}";
        $package['sign'] = strtoupper(md5($string1));
        $xml = array2xml($package);
        //dump($package);

        $client = new \GuzzleHttp\Client(['timeout' => 30, 'verify' => false]);
        $response = $client->request('POST', $url, ['body' => $xml, 'cert' => $sslcert, 'ssl_key' => $sslkey]);
        $info = $response->getBody()->getContents();

        $resp = (array)(simplexml_load_string($info, 'SimpleXMLElement', LIBXML_NOCDATA));
        //dump($resp);die;
        if ($resp['return_code'] == 'SUCCESS' && $resp['result_code'] == 'SUCCESS') {
            //记录
            $data = [];
            $data['aid'] = $aid;
            $data['openid'] = $openid;
            $data['money'] = $money;
            $data['appid'] = $appinfo['appid'];
            $data['mchid'] = $appinfo['wxpay_mchid'];
            $data['ordernum'] = $ordernum;
            $data['createtime'] = date('Y-m-d H:i:s');
            $data['status'] = 1;
            $data['remark'] = '发送成功';
            Db::name('sendredpack_log')->insert($data);
            return ['status' => 1, 'msg' => '发送成功', 'resp' => $resp];
        } else {
            $msg = '未知错误';
            if ($resp['return_code'] == 'FAIL') {
                $msg = $resp['return_msg'];
            }
            if ($resp['result_code'] == 'FAIL') {
                $msg = $resp['err_code_des'];
            }
            //记录
            $data = [];
            $data['aid'] = $aid;
            $data['openid'] = $openid;
            $data['money'] = $money;
            $data['appid'] = $appinfo['appid'];
            $data['mchid'] = $appinfo['wxpay_mchid'];
            $data['ordernum'] = $ordernum;
            $data['createtime'] = date('Y-m-d H:i:s');
            $data['status'] = 2;
            $data['remark'] = $msg;
            Db::name('sendredpack_log')->insert($data);
            return ['status' => 0, 'msg' => $msg, 'resp' => $resp];
        }
    }

    //企业付款到零钱
    public static function transfers($aid, $mid, $money, $ordernum = '', $platform = 'wx', $desc = '打款')
    {
//		$set = Db::name('admin_set')->where('aid',$aid)->find();
        if (!$platform) {
            $openid = Db::name('member')->where('id', $mid)->value('mpopenid');
            if (!$openid) {
                $platform = 'wx';
            } else {
                $platform = 'mp';
            }
        }
        if ($platform == 'wx') { //小程序
            $openid = Db::name('member')->where('id', $mid)->value('wxopenid');
            $appinfo = \app\common\System::appinfo($aid, 'wx');
        } else { //公众号网页
            $openid = Db::name('member')->where('id', $mid)->value('mpopenid');
            $appinfo = \app\common\System::appinfo($aid, 'mp');
        }
        if (!$openid) return ['status' => 0, 'msg' => '未查找到' . t('会员') . 'openid'];

        $sslcert = ROOT_PATH . $appinfo['wxpay_apiclient_cert'];
        $sslkey = ROOT_PATH . $appinfo['wxpay_apiclient_key'];

        $pars = array();
        $pars['mch_appid'] = $appinfo['appid'];
        $pars['mchid'] = $appinfo['wxpay_mchid'];
        $pars['nonce_str'] = random(32);
        if ($ordernum == '') {
            $pars['partner_trade_no'] = date('ymdHis') . $aid . rand(1000, 9999);
        } else {
            $pars['partner_trade_no'] = $ordernum;
        }
        $pars['openid'] = $openid;
        $pars['check_name'] = 'NO_CHECK';
        $pars['amount'] = intval($money * 100);
        $pars['desc'] = $desc;
        $pars['spbill_create_ip'] = $_SERVER["REMOTE_ADDR"];
        ksort($pars, SORT_STRING);
        $string1 = '';
        foreach ($pars as $k => $v) {
            $string1 .= "{$k}={$v}&";
        }
        $string1 .= "key=" . $appinfo['wxpay_mchkey'];
        $pars['sign'] = strtoupper(md5($string1));
        $xml = array2xml($pars);

        $client = new \GuzzleHttp\Client(['timeout' => 30, 'verify' => false]);
        $response = $client->request('POST', 'https://api.mch.weixin.qq.com/mmpaymkttransfers/promotion/transfers', ['body' => $xml, 'cert' => $sslcert, 'ssl_key' => $sslkey]);
        $info = $response->getBody()->getContents();

        $resp = (array)(simplexml_load_string($info, 'SimpleXMLElement', LIBXML_NOCDATA));
        //Log::write([
        //    'file'=>__FILE__,
        //    'transfers'=>$pars,
        //    '$resp' => $resp
        //]);
        if ($resp['return_code'] == 'SUCCESS' && $resp['result_code'] == 'SUCCESS') {
            return ['status' => 1, 'msg' => '打款成功', 'resp' => $resp];
        } else {
            $msg = '未知错误';
            if ($resp['return_code'] == 'FAIL') {
                $msg = $resp['return_msg'];
            }
            if ($resp['result_code'] == 'FAIL') {
                $msg = $resp['err_code_des'];
            }
            return ['status' => 0, 'msg' => $msg, 'resp' => $resp];
        }
    }
}