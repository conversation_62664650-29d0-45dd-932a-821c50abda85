<?php
/*
 * @Author: cclils<PERSON> <EMAIL>
 * @Date: 2023-03-17 17:58:27
 * @LastEditors: cclilshy <EMAIL>
 * @Description: My house
 * Copyright (c) 2023 by user email: <EMAIL>, All Rights Reserved.
 */
// webhook.php
//
// Use this sample code to handle webhook events in your integration.
//
// 1) Paste this code into a new file (webhook.php)
//
// 2) Install dependencies
//   composer require stripe/stripe-php
//
// 3) Run the server on http://localhost:4242
//   php -S localhost:4242

require 'vendor/autoload.php';

// This is your Stripe CLI webhook secret for testing your endpoint locally.
$endpoint_secret = 'whsec_ldhXZwVb5et4F8EBxvNoHxmlZu74zkoe';

$payload = @file_get_contents('php://input');
$sig_header = $_SERVER['HTTP_STRIPE_SIGNATURE'];
$event = null;

try {
    $event = \Stripe\Webhook::constructEvent(
        $payload,
        $sig_header,
        $endpoint_secret
    );
} catch (\UnexpectedValueException $e) {
    // Invalid payload
    http_response_code(400);
    exit();
} catch (\Stripe\Exception\SignatureVerificationException $e) {
    // Invalid signature
    http_response_code(400);
    exit();
}

// Handle the event
echo 'Received unknown event type ' . $event->type . PHP_EOL;

http_response_code(200);
$input = $payload;
if (isset($input)) {
    @file_put_contents(__DIR__ . '/logs/' . md5(microtime(true)) . '.log', $input);
    $inputData = json_decode($input, true);

    if (isset($inputData['type']) && ($inputData['type'] === 'checkout.session.async_payment_succeeded' || $inputData['type'] === 'checkout.session.completed')) {
        echo 'Notify Type : ' . $event->type . PHP_EOL;
        $object = $inputData['data']['object'];
        $amount = intval($object['amount_total']) / 100;
        $payId = $object['id'];
        echo "https://shop.sgmall.sg/?s=/ApiStripe/callback&id={$payId}&amount={$amount}&stripepsy=true";

        $arrContextOptions = array(
            "ssl" => array(
                "verify_peer" => false,
                "verify_peer_name" => false,
            ),
        );

        $response = file_get_contents("https://shop.sgmall.sg/?s=/ApiStripe/callback&id={$payId}&amount={$amount}&stripepsy=true", false, stream_context_create($arrContextOptions));
        echo "Notify Server Success id:{$payId}, amount: {$amount}";
    }
}

return;
