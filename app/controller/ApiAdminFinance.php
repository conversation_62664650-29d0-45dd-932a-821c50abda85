<?php


//管理员中心 - 财务管理
namespace app\controller;

use think\facade\Db;

class ApiAdminFinance extends ApiAdmin
{
    //财务管理
    function index()
    {
        $aid = aid;
        $lastDayStart = strtotime(date('Y-m-d', time() - 86400));
        $lastDayEnd = $lastDayStart + 86400;
        $thisMonthStart = strtotime(date('Y-m-1'));
        $nowtime = time();
        $info = [];

        //退款金额
        $info['refundCount'] = Db::name('shop_order')->where('aid', aid)->where('bid', bid)->where('refund_status', 2)->sum('refund_money');
        $info['refundLastDayCount'] = Db::name('shop_refund_order')->where('aid', aid)->where('bid', bid)->where('refund_time', '>=', $lastDayStart)->where('refund_time', '<', $lastDayEnd)->where('refund_status', 2)->sum('refund_money');
        $info['refundThisMonthCount'] = Db::name('shop_refund_order')->where('aid', aid)->where('bid', bid)->where('refund_time', '>=', $thisMonthStart)->where('refund_time', '<', $nowtime)->where('refund_status', 2)->sum('refund_money');
        if (bid == 0) {
            //收款金额
            $info['wxpayCount'] = Db::name('payorder')->where('aid', aid)->where('bid', bid)->where('status', 1)->where('paytypeid', 'not in', '1,4')->sum('money');
            $info['wxpayCount'] = round($info['wxpayCount'], 2);
            $info['wxpayLastDayCount'] = Db::name('payorder')->where('aid', aid)->where('bid', bid)->where('status', 1)->where('paytypeid', 'not in', '1,4')->where('createtime', '>=', $lastDayStart)->where('createtime', '<', $lastDayEnd)->sum('money');
            $info['wxpayLastDayCount'] = round($info['wxpayLastDayCount'], 2);
            $info['wxpayThisMonthCount'] = 0 + Db::name('payorder')->where('aid', aid)->where('bid', bid)->where('status', 1)->where('paytypeid', 'not in', '1,4')->where('paytime', '>=', $thisMonthStart)->where('paytime', '<', $nowtime)->sum('money');
            $info['wxpayThisMonthCount'] = round($info['wxpayThisMonthCount'], 2);
            //提现金额
            $info['withdrawCount'] = Db::name('member_withdrawlog')->where('aid', aid)->where('status', 3)->sum('money') + Db::name('member_commission_withdrawlog')->where('aid', aid)->where('status', 3)->sum('money');
            $info['withdrawCount'] = round($info['withdrawCount'], 2);
            $info['withdrawLastDayCount'] = Db::name('member_withdrawlog')->where('aid', aid)->where('status', 3)->where('createtime', '>=', $lastDayStart)->where('createtime', '<', $lastDayEnd)->sum('money') + Db::name('member_commission_withdrawlog')->where('aid', aid)->where('status', 3)->where('createtime', '>=', $lastDayStart)->where('createtime', '<', $lastDayEnd)->sum('money');
            $info['withdrawLastDayCount'] = round($info['withdrawLastDayCount'], 2);
            $info['withdrawThisMonthCount'] = Db::name('member_withdrawlog')->where('aid', aid)->where('status', 3)->where('createtime', '>=', $thisMonthStart)->where('createtime', '<', $nowtime)->sum('money') + Db::name('member_commission_withdrawlog')->where('aid', aid)->where('status', 3)->where('createtime', '>=', $thisMonthStart)->where('createtime', '<', $nowtime)->sum('money');
            $info['withdrawThisMonthCount'] = round($info['withdrawThisMonthCount'], 2);
        } else {
            //收款金额
            $info['wxpayCount'] = Db::name('payorder')->where('aid', aid)->where('bid', bid)->where('status', 1)->where('paytypeid', '<>', '4')->sum('money');
            $info['wxpayCount'] = round($info['wxpayCount'], 2);
            $info['wxpayLastDayCount'] = Db::name('payorder')->where('aid', aid)->where('bid', bid)->where('status', 1)->where('paytypeid', '<>', '4')->where('createtime', '>=', $lastDayStart)->where('createtime', '<', $lastDayEnd)->sum('money');
            $info['wxpayLastDayCount'] = round($info['wxpayLastDayCount'], 2);
            $info['wxpayThisMonthCount'] = 0 + Db::name('payorder')->where('aid', aid)->where('bid', bid)->where('status', 1)->where('paytypeid', '<>', '4')->where('paytime', '>=', $thisMonthStart)->where('paytime', '<', $nowtime)->sum('money');
            $info['wxpayThisMonthCount'] = round($info['wxpayThisMonthCount'], 2);
        }
        $info['commissiontotal'] = Db::name('member')->where('aid', aid)->sum('totalcommission');
        $info['commission'] = Db::name('member')->where('aid', aid)->sum('commission');
        $info['commissionwithdraw'] = Db::name('member_commission_withdrawlog')->where('aid', aid)->where('status', 3)->sum('txmoney');

        $rdata = [];
        $rdata['status'] = 1;

        //余额宝收益
        $rdata['showyuebao_moneylog'] = false;
        //余额宝提现
        $rdata['showyuebao_withdrawlog'] = false;
        if (getcustom('plug_yuebao')) {
            if ($this->user['auth_type'] == 0) {
                $auth_data = json_decode($this->user['auth_data'], true);
                $auth_path = [];
                foreach ($auth_data as $v) {
                    $auth_path = array_merge($auth_path, explode(',', $v));
                }
                $auth_data = $auth_path;
            } else {
                $auth_data = 'all';
            }
            if ($auth_data == 'all') {
                //余额宝收益
                $rdata['showyuebao_moneylog'] = true;
                //余额宝提现
                $rdata['showyuebao_withdrawlog'] = true;
            } else {
                if (in_array('Yuebao/*', $auth_data)) {
                    //余额宝收益
                    $rdata['showyuebao_moneylog'] = true;
                    //余额宝提现
                    $rdata['showyuebao_withdrawlog'] = true;
                } else {
                    if (in_array('Yuebao/moneylog', $auth_data)) {
                        //余额宝收益
                        $rdata['showyuebao_moneylog'] = true;
                    }
                    if (in_array('Yuebao/withdrawlog', $auth_data)) {
                        //余额宝提现
                        $rdata['showyuebao_withdrawlog'] = true;
                    }
                }
            }
            $info['yuebaowithdrawCount'] = Db::name('member_yuebao_withdrawlog')->where('aid', aid)->where('status', 3)->sum('money');
            $info['withdrawCount'] += round($info['yuebaowithdrawCount'], 2);

            $info['yuebaowithdrawLastDayCount'] = Db::name('member_yuebao_withdrawlog')->where('aid', aid)->where('status', 3)->where('createtime', '>=', $lastDayStart)->where('createtime', '<', $lastDayEnd)->sum('money');
            $info['withdrawLastDayCount'] += round($info['yuebaowithdrawLastDayCount'], 2);

            $info['yuebaowithdrawThisMonthCount'] = Db::name('member_yuebao_withdrawlog')->where('aid', aid)->where('status', 3)->where('createtime', '>=', $thisMonthStart)->where('createtime', '<', $nowtime)->sum('money');
            $info['withdrawThisMonthCount'] += round($info['yuebaowithdrawThisMonthCount'], 2);
        }

        $rdata['info'] = $info;
        $rdata['bid'] = bid;
        $rdata['auth_data'] = $this->auth_data;
        return $this->json($rdata);
    }

    //余额充值记录
    function rechargelog()
    {
        $pagenum = input('post.pagenum');
        if (!$pagenum) $pagenum = 1;
        $pernum = 20;
        $order = 'id desc';
        $where = [];
        $where[] = ['recharge_order.aid', '=', aid];
        $where[] = ['recharge_order.status', '=', 1];

        if (input('param.keyword')) $where[] = ['member.nickname', 'like', '%' . trim(input('param.keyword')) . '%'];
        $datalist = Db::name('recharge_order')->alias('recharge_order')->field('member.nickname,member.headimg,recharge_order.*')->join('member member', 'member.id=recharge_order.mid')->where($where)->page($pagenum, $pernum)->order($order)->select()->toArray();
        if ($pagenum == 1) {
            $count = 0 + Db::name('recharge_order')->alias('recharge_order')->field('member.nickname,member.headimg,recharge_order.*')->join('member member', 'member.id=recharge_order.mid')->where($where)->count();
        }
        return $this->json(['status' => 1, 'count' => $count, 'data' => $datalist]);
    }

    //余额明细
    function moneylog()
    {
        $pagenum = input('post.pagenum');
        if (!$pagenum) $pagenum = 1;
        $order = 'id desc';
        $pernum = 20;
        $where = [];
        $where[] = ['member_moneylog.aid', '=', aid];
        if (input('param.keyword')) $where[] = ['member.nickname', 'like', '%' . trim(input('param.keyword')) . '%'];
        if (input('?param.status') && input('param.status') !== '') $where[] = ['member_moneylog.status', '=', input('param.status')];
        $datalist = Db::name('member_moneylog')->alias('member_moneylog')->field('member.nickname,member.headimg,member_moneylog.*')->join('member member', 'member.id=member_moneylog.mid')->where($where)->page($pagenum, $pernum)->order($order)->select()->toArray();
        if ($pagenum == 1) {
            $count = 0 + Db::name('member_moneylog')->alias('member_moneylog')->field('member.nickname,member.headimg,member_moneylog.*')->join('member member', 'member.id=member_moneylog.mid')->where($where)->count();
        }
        return $this->json(['status' => 1, 'count' => $count, 'data' => $datalist]);
    }

    //佣金明细
    function commissionlog()
    {
        $pagenum = input('post.pagenum');
        if (!$pagenum) $pagenum = 1;
        $order = 'id desc';
        $pernum = 20;
        $where = [];
        $where[] = ['member_commissionlog.aid', '=', aid];
        if (input('param.keyword')) $where[] = ['member.nickname', 'like', '%' . trim(input('param.keyword')) . '%'];
        if (input('?param.status') && input('param.status') !== '') $where[] = ['member_commissionlog.status', '=', input('param.status')];
        $datalist = Db::name('member_commissionlog')->alias('member_commissionlog')->field('member.nickname,member.headimg,member_commissionlog.*')->join('member member', 'member.id=member_commissionlog.mid')->where($where)->page($pagenum, $pernum)->order($order)->select()->toArray();
        if ($pagenum == 1) {
            $count = 0 + Db::name('member_commissionlog')->alias('member_commissionlog')->field('member.nickname,member.headimg,member_commissionlog.*')->join('member member', 'member.id=member_commissionlog.mid')->where($where)->count();
        }
        return $this->json(['status' => 1, 'count' => $count, 'data' => $datalist]);
    }

    //余额提现记录
    function withdrawlog()
    {
        $pagenum = input('post.pagenum');
        $st = input('post.st');
        if (!$pagenum) $pagenum = 1;
        $pernum = 20;
        $where = [];
        $where[] = ['withdrawlog.aid', '=', aid];
        if ($st == 'all') {

        } elseif ($st == '0') {
            $where[] = ['status', '=', 0];
        } elseif ($st == '1') {
            $where[] = ['status', '=', 1];
        } elseif ($st == '2') {
            $where[] = ['status', '=', 2];
        } elseif ($st == '3') {
            $where[] = ['status', '=', 3];
        }

        if (input('param.keyword')) $where[] = ['member.nickname', 'like', '%' . trim(input('param.keyword')) . '%'];
        if (input('?param.status') && input('param.status') !== '') $where[] = ['withdrawlog.status', '=', input('param.status')];
        $datalist = Db::name('member_withdrawlog')->alias('withdrawlog')->field('member.nickname,member.headimg,withdrawlog.*')->join('member member', 'member.id=withdrawlog.mid')->where($where)->page($pagenum, $pernum)->order('withdrawlog.id desc')->select()->toArray();
        if ($pagenum == 1) {
            $count = 0 + Db::name('member_withdrawlog')->alias('withdrawlog')->field('member.nickname,member.headimg,withdrawlog.*')->join('member member', 'member.id=withdrawlog.mid')->where($where)->count();
        }
        return $this->json(['status' => 1, 'count' => $count, 'data' => $datalist]);
    }

    //余额提现明细
    function withdrawdetail()
    {
        $id = input('param.id/d');
        $info = Db::name('member_withdrawlog')->where(['aid' => aid, 'id' => $id])->find();
        $member = Db::name('member')->where(['id' => $info['mid']])->find();
        $info['nickname'] = $member['nickname'];
        $info['headimg'] = $member['headimg'];
        return $this->json(['status' => 1, 'info' => $info]);
    }

    //余额提现审核通过
    function widthdrawpass()
    {
        $id = input('post.id/d');
        $info = Db::name('member_withdrawlog')->where(['aid' => aid, 'id' => $id])->update(['status' => 1, 'reason' => '']);
        return $this->json(['status' => 1, 'msg' => '操作成功']);
    }

    //余额提现审核不通过
    function widthdrawnopass()
    {
        $id = input('post.id/d');
        $reason = input('post.reason');
        Db::name('member_withdrawlog')->where(['aid' => aid, 'id' => $id])->update(['status' => 2, 'reason' => $reason]);
        $info = Db::name('member_withdrawlog')->where(['aid' => aid, 'id' => $id])->find();
        \app\common\Member::addmoney(aid, $info['mid'], $info['txmoney'], t('余额') . '提现返还');
        //提现失败通知
        $tmplcontent = [];
        $tmplcontent['first'] = '您的提现申请被商家驳回，可与商家协商沟通。';
        $tmplcontent['remark'] = $reason . '，请点击查看详情~';
        $tmplcontent['money'] = (string)$info['txmoney'];
        $tmplcontent['time'] = date('Y-m-d H:i', $info['createtime']);
        \app\common\Wechat::sendtmpl(aid, $info['mid'], 'tmpl_tixianerror', $tmplcontent, m_url('/pages/my/usercenter'));
        //订阅消息
        $tmplcontent = [];
        $tmplcontent['amount1'] = $info['txmoney'];
        $tmplcontent['time3'] = date('Y-m-d H:i', $info['createtime']);
        $tmplcontent['thing4'] = $reason;

        $tmplcontentnew = [];
        $tmplcontentnew['thing1'] = '提现失败';
        $tmplcontentnew['amount2'] = $info['txmoney'];
        $tmplcontentnew['date4'] = date('Y-m-d H:i', $info['createtime']);
        $tmplcontentnew['thing12'] = $reason;
        \app\common\Wechat::sendwxtmpl(aid, $info['mid'], 'tmpl_tixianerror', $tmplcontentnew, 'pages/my/usercenter', $tmplcontent);
        //短信通知
        $member = Db::name('member')->where(['id' => $info['mid']])->find();
        if ($member['tel']) {
            \app\common\Sms::send(aid, $member['tel'], 'tmpl_tixianerror', ['reason' => $reason]);
        }
        return $this->json(['status' => 1, 'msg' => '操作成功']);
    }

    //余额提现改为打款
    function widthdsetydk()
    {
        $id = input('post.id/d');
        Db::name('member_withdrawlog')->where(['aid' => aid, 'id' => $id])->update(['status' => 3, 'reason' => '']);
        $info = Db::name('member_withdrawlog')->where(['aid' => aid, 'id' => $id])->find();
        //提现成功通知
        $tmplcontent = [];
        $tmplcontent['first'] = '您的提现申请已打款，请留意查收';
        $tmplcontent['remark'] = '请点击查看详情~';
        $tmplcontent['money'] = (string)$info['money'];
        $tmplcontent['timet'] = date('Y-m-d H:i', $info['createtime']);
        \app\common\Wechat::sendtmpl(aid, $info['mid'], 'tmpl_tixiansuccess', $tmplcontent, m_url('pages/my/usercenter'));
        //订阅消息
        $tmplcontent = [];
        $tmplcontent['amount1'] = $info['money'];
        $tmplcontent['thing3'] = $info['paytype'];
        $tmplcontent['time5'] = date('Y-m-d H:i');

        $tmplcontentnew = [];
        $tmplcontentnew['amount3'] = $info['money'];
        $tmplcontentnew['phrase9'] = $info['paytype'];
        $tmplcontentnew['date8'] = date('Y-m-d H:i');
        \app\common\Wechat::sendwxtmpl(aid, $info['mid'], 'tmpl_tixiansuccess', $tmplcontentnew, 'pages/my/usercenter', $tmplcontent);
        //短信通知
        $member = Db::name('member')->where(['id' => $info['mid']])->find();
        if ($member['tel']) {
            \app\common\Sms::send(aid, $member['tel'], 'tmpl_tixiansuccess', ['money' => $info['money']]);
        }
        return $this->json(['status' => 1, 'msg' => '操作成功']);
    }

    //余额提现 微信打款
    function widthdwxdakuan()
    {
        $id = input('post.id/d');
        $info = Db::name('member_withdrawlog')->where(['aid' => aid, 'id' => $id])->find();
        if ($info['status'] != 1) return $this->json(['status' => 0, 'msg' => '已审核状态才能打款']);
        $rs = \app\common\Wxpay::transfers(aid, $info['mid'], $info['money'], $info['ordernum'], $info['platform'], t('余额') . '提现');
        if ($rs['status'] == 0) {
            return $this->json(['status' => 0, 'msg' => $rs['msg']]);
        } else {
            Db::name('member_withdrawlog')->where(['aid' => aid, 'id' => $id])->update(['status' => 3, 'paytime' => time(), 'paynum' => $rs['resp']['payment_no']]);
            //提现成功通知
            $tmplcontent = [];
            $tmplcontent['first'] = '您的提现申请已打款，请留意查收';
            $tmplcontent['remark'] = '请点击查看详情~';
            $tmplcontent['money'] = (string)$info['money'];
            $tmplcontent['timet'] = date('Y-m-d H:i', $info['createtime']);
            \app\common\Wechat::sendtmpl(aid, $info['mid'], 'tmpl_tixiansuccess', $tmplcontent, m_url('pages/my/usercenter'));
            //订阅消息
            $tmplcontent = [];
            $tmplcontent['amount1'] = $info['money'];
            $tmplcontent['thing3'] = $info['paytype'];
            $tmplcontent['time5'] = date('Y-m-d H:i');

            $tmplcontentnew = [];
            $tmplcontentnew['amount3'] = $info['money'];
            $tmplcontentnew['phrase9'] = $info['paytype'];
            $tmplcontentnew['date8'] = date('Y-m-d H:i');
            \app\common\Wechat::sendwxtmpl(aid, $info['mid'], 'tmpl_tixiansuccess', $tmplcontentnew, 'pages/my/usercenter', $tmplcontent);
            //短信通知
            $member = Db::name('member')->where(['id' => $info['mid']])->find();
            if ($member['tel']) {
                $tel = $member['tel'];
                \app\common\Sms::send(aid, $tel, 'tmpl_tixiansuccess', ['money' => $info['money']]);
            }
            return $this->json(['status' => 1, 'msg' => $rs['msg']]);
        }
    }

    //佣金提现记录
    function comwithdrawlog()
    {
        $pagenum = input('post.pagenum');
        $st = input('post.st');
        if (!$pagenum) $pagenum = 1;
        $pernum = 20;
        $where = [];
        $where[] = ['withdrawlog.aid', '=', aid];
        if ($st == 'all') {

        } elseif ($st == '0') {
            $where[] = ['status', '=', 0];
        } elseif ($st == '1') {
            $where[] = ['status', '=', 1];
        } elseif ($st == '2') {
            $where[] = ['status', '=', 2];
        } elseif ($st == '3') {
            $where[] = ['status', '=', 3];
        }

        if (input('param.keyword')) $where[] = ['member.nickname', 'like', '%' . trim(input('param.keyword')) . '%'];
        if (input('?param.status') && input('param.status') !== '') $where[] = ['withdrawlog.status', '=', input('param.status')];
        $datalist = Db::name('member_commission_withdrawlog')->alias('withdrawlog')->field('member.nickname,member.headimg,withdrawlog.*')->join('member member', 'member.id=withdrawlog.mid')->where($where)->page($pagenum, $pernum)->order('withdrawlog.id desc')->select()->toArray();
        if ($pagenum == 1) {
            $count = 0 + Db::name('member_commission_withdrawlog')->alias('withdrawlog')->field('member.nickname,member.headimg,withdrawlog.*')->join('member member', 'member.id=withdrawlog.mid')->where($where)->count();
        }
        return $this->json(['status' => 1, 'count' => $count, 'data' => $datalist]);
    }

    function comwithdrawdetail()
    {
        $id = input('param.id/d');
        $info = Db::name('member_commission_withdrawlog')->where(['aid' => aid, 'id' => $id])->find();

        $comwithdrawbl = Db::name('admin_set')->where('aid', aid)->value('comwithdrawbl');
        if ($comwithdrawbl > 0 && $comwithdrawbl < 100) {
            $money = $info['money'];
            $info['money'] = round($money * $comwithdrawbl * 0.01, 2);
            $info['tomoney'] = round($money - $info['money'], 2);
        } else {
            $info['tomoney'] = 0;
        }

        $member = Db::name('member')->where(['id' => $info['mid']])->find();
        $info['nickname'] = $member['nickname'];
        $info['headimg'] = $member['headimg'];
        return $this->json(['status' => 1, 'info' => $info]);
    }

    function comwidthdrawpass()
    {
        $id = input('post.id/d');
        $info = Db::name('member_commission_withdrawlog')->where(['aid' => aid, 'id' => $id])->update(['status' => 1]);
        return $this->json(['status' => 1, 'msg' => '操作成功']);
    }

    function comwidthdrawnopass()
    {
        $id = input('post.id/d');
        $reason = input('post.reason');
        Db::name('member_commission_withdrawlog')->where(['aid' => aid, 'id' => $id])->update(['status' => 2, 'reason' => $reason]);
        $info = Db::name('member_commission_withdrawlog')->where(['aid' => aid, 'id' => $id])->find();
        \app\common\Member::addcommission(aid, $info['mid'], 0, $info['txmoney'], t('佣金') . '提现返还', 0);
        //提现失败通知
        $tmplcontent = [];
        $tmplcontent['first'] = '您的提现申请被商家驳回，可与商家协商沟通。';
        $tmplcontent['remark'] = $reason . '，请点击查看详情~';
        $tmplcontent['money'] = (string)$info['txmoney'];
        $tmplcontent['time'] = date('Y-m-d H:i', $info['createtime']);
        \app\common\Wechat::sendtmpl(aid, $info['mid'], 'tmpl_tixianerror', $tmplcontent, m_url('pages/commission/commissionlog?st=1'));
        //订阅消息
        $tmplcontent = [];
        $tmplcontent['amount1'] = $info['txmoney'];
        $tmplcontent['time3'] = date('Y-m-d H:i', $info['createtime']);
        $tmplcontent['thing4'] = $reason;

        $tmplcontentnew = [];
        $tmplcontentnew['thing1'] = '提现失败';
        $tmplcontentnew['amount2'] = $info['txmoney'];
        $tmplcontentnew['date4'] = date('Y-m-d H:i', $info['createtime']);
        $tmplcontentnew['thing12'] = $reason;
        \app\common\Wechat::sendwxtmpl(aid, $info['mid'], 'tmpl_tixianerror', $tmplcontentnew, 'pages/my/usercenter', $tmplcontent);
        //短信通知
        $member = Db::name('member')->where(['id' => $info['mid']])->find();
        if ($member['tel']) {
            $tel = $member['tel'];
            \app\common\Sms::send(aid, $tel, 'tmpl_tixianerror', ['reason' => $reason]);
        }
        return $this->json(['status' => 1, 'msg' => '操作成功']);
    }

    function comwidthdsetydk()
    {
        $id = input('post.id/d');
        Db::name('member_commission_withdrawlog')->where(['aid' => aid, 'id' => $id])->update(['status' => 3]);
        $info = Db::name('member_commission_withdrawlog')->where(['aid' => aid, 'id' => $id])->find();
        //提现成功通知
        $tmplcontent = [];
        $tmplcontent['first'] = '您的提现申请已打款，请留意查收';
        $tmplcontent['remark'] = '请点击查看详情~';
        $tmplcontent['money'] = (string)$info['money'];
        $tmplcontent['timet'] = date('Y-m-d H:i', $info['createtime']);
        \app\common\Wechat::sendtmpl(aid, $info['mid'], 'tmpl_tixiansuccess', $tmplcontent, m_url('pages/my/usercenter'));
        //订阅消息
        $tmplcontent = [];
        $tmplcontent['amount1'] = $info['money'];
        $tmplcontent['thing3'] = $info['paytype'];
        $tmplcontent['time5'] = date('Y-m-d H:i');

        $tmplcontentnew = [];
        $tmplcontentnew['amount3'] = $info['money'];
        $tmplcontentnew['phrase9'] = $info['paytype'];
        $tmplcontentnew['date8'] = date('Y-m-d H:i');
        \app\common\Wechat::sendwxtmpl(aid, $info['mid'], 'tmpl_tixiansuccess', $tmplcontentnew, 'pages/my/usercenter', $tmplcontent);
        //短信通知
        $member = Db::name('member')->where(['id' => $info['mid']])->find();
        if ($member['tel']) {
            $tel = $member['tel'];
            \app\common\Sms::send(aid, $tel, 'tmpl_tixiansuccess', ['money' => $info['money']]);
        }
        return $this->json(['status' => 1, 'msg' => '操作成功']);
    }

    function comwidthdwxdakuan()
    {
        $id = input('post.id/d');
        $info = db('member_commission_withdrawlog')->where(['aid' => aid, 'id' => $id])->find();
        if ($info['status'] != 1) return ['status' => 0, 'msg' => '已审核状态才能打款'];
        $comwithdrawbl = Db::name('admin_set')->where('aid', aid)->value('comwithdrawbl');
        if ($comwithdrawbl > 0 && $comwithdrawbl < 100) {
            $paymoney = round($info['money'] * $comwithdrawbl * 0.01, 2);
            $tomoney = round($info['money'] - $paymoney, 2);
        } else {
            $paymoney = $info['money'];
            $tomoney = 0;
        }

        $rs = \app\common\Wxpay::transfers(aid, $info['mid'], $paymoney, $info['ordernum'], $info['platform'], t('佣金') . '提现');
        if ($rs['status'] == 0) {
            return $this->json(['status' => 0, 'msg' => $rs['msg']]);
        } else {
            if ($tomoney > 0) {
                \app\common\Member::addmoney(aid, $info['mid'], $tomoney, t('佣金') . '提现');
            }
            Db::name('member_commission_withdrawlog')->where(['aid' => aid, 'id' => $id])->update(['status' => 3, 'paytime' => time(), 'paynum' => $rs['resp']['payment_no']]);
            //提现成功通知
            $tmplcontent = [];
            $tmplcontent['first'] = '您的提现申请已打款，请留意查收';
            $tmplcontent['remark'] = '请点击查看详情~';
            $tmplcontent['money'] = (string)$info['money'];
            $tmplcontent['timet'] = date('Y-m-d H:i', $info['createtime']);
            \app\common\Wechat::sendtmpl(aid, $info['mid'], 'tmpl_tixiansuccess', $tmplcontent, m_url('pages/my/usercenter'));
            //订阅消息
            $tmplcontent = [];
            $tmplcontent['amount1'] = $info['money'];
            $tmplcontent['thing3'] = $info['paytype'];
            $tmplcontent['time5'] = date('Y-m-d H:i');

            $tmplcontentnew = [];
            $tmplcontentnew['amount3'] = $info['money'];
            $tmplcontentnew['phrase9'] = $info['paytype'];
            $tmplcontentnew['date8'] = date('Y-m-d H:i');
            \app\common\Wechat::sendwxtmpl(aid, $info['mid'], 'tmpl_tixiansuccess', $tmplcontentnew, 'pages/my/usercenter', $tmplcontent);
            //短信通知
            $member = Db::name('member')->where(['id' => $info['mid']])->find();
            if ($member['tel']) {
                $tel = $member['tel'];
                \app\common\Sms::send(aid, $tel, 'tmpl_tixiansuccess', ['money' => $info['money']]);
            }

            return $this->json(['status' => 1, 'msg' => $rs['msg']]);
        }
    }

    //商家余额明细
    public function bmoneylog()
    {
        $pagenum = input('post.pagenum');
        if (!$pagenum) $pagenum = 1;
        $pernum = 20;
        $where = [];
        $where[] = ['aid', '=', aid];
        $where[] = ['bid', '=', bid];
        $datalist = Db::name('business_moneylog')->field("id,money,`after`,createtime,remark")->where($where)->page($pagenum, $pernum)->order('id desc')->select()->toArray();
        if (!$datalist) $datalist = [];
        return $this->json(['status' => 1, 'data' => $datalist]);
    }

    //商家余额提现记录
    public function bwithdrawlog()
    {
        $pagenum = input('post.pagenum');
        if (!$pagenum) $pagenum = 1;
        $pernum = 20;
        $where = [];
        $where[] = ['aid', '=', aid];
        $where[] = ['bid', '=', bid];
        $datalist = Db::name('business_withdrawlog')->where($where)->page($pagenum, $pernum)->order('id desc')->select()->toArray();
        if (!$datalist) $datalist = [];
        return $this->json(['status' => 1, 'data' => $datalist]);
    }

    //提现信息设置
    public function txset()
    {
        if (request()->isPost()) {
            $postinfo = input('post.');
            $data = [];
            $data['weixin'] = $postinfo['weixin'];
            $data['aliaccount'] = $postinfo['aliaccount'];
            $data['bankname'] = $postinfo['bankname'];
            $data['bankcarduser'] = $postinfo['bankcarduser'];
            $data['bankcardnum'] = $postinfo['bankcardnum'];
            Db::name('business')->where('id', bid)->update($data);
            return $this->json(['status' => 1, 'msg' => '保存成功']);
        }
        $info = Db::name('business')->field('id,weixin,aliaccount,bankname,bankcarduser,bankcardnum')->where(['id' => bid])->find();
        return $this->json(['status' => 1, 'info' => $info]);
    }

    public function bwithdraw()
    {
        $set = Db::name('business_sysset')->where(['aid' => aid])->field('withdrawmin,withdrawfee,withdraw_weixin,withdraw_aliaccount,withdraw_bankcard,commission_autotransfer')->find();
        if (request()->isPost()) {
            $post = input('post.');
            //if($set['withdraw'] == 0){
            //	return ['status'=>0,'msg'=>'余额提现功能未开启'];
            //}
            $binfo = Db::name('business')->where('id', bid)->find();
            if ($post['paytype'] == '支付宝' && $binfo['aliaccount'] == '') {
                return $this->json(['status' => 0, 'msg' => '请先设置支付宝账号']);
            }
            if ($post['paytype'] == '银行卡' && ($binfo['bankname'] == '' || $binfo['bankcarduser'] == '' || $binfo['bankcardnum'] == '')) {
                return $this->json(['status' => 0, 'msg' => '请先设置完整银行卡信息']);
            }

            $money = $post['money'];
            if ($money <= 0 || $money < $set['withdrawmin']) {
                return $this->json(['status' => 0, 'msg' => '提现金额必须大于' . ($set['withdrawmin'] ? $set['withdrawmin'] : 0)]);
            }
            if ($money > $binfo['money']) {
                return $this->json(['status' => 0, 'msg' => '可提现余额不足']);
            }

            $ordernum = date('ymdHis') . aid . rand(1000, 9999);
            $record['aid'] = aid;
            $record['bid'] = bid;
            $record['createtime'] = time();
            $record['money'] = $money * (1 - $set['withdrawfee'] * 0.01);
            $record['txmoney'] = $money;
            $record['ordernum'] = $ordernum;
            $record['paytype'] = $post['paytype'];
            if ($post['paytype'] == '微信' || $post['paytype'] == '微信钱包') {
                if ($set['commission_autotransfer'] == 1) {
                    \app\common\Business::addmoney(aid, bid, -$money, '余额提现');
                    $mid = Db::name('admin_user')->where('aid', aid)->where('bid', bid)->where('isadmin', 1)->value('mid');
                    if (!$mid) return json(['status' => 0, 'msg' => '商户主管理员未绑定微信']);
                    $rs = \app\common\Wxpay::transfers(aid, $mid, $record['money'], $record['ordernum'], 'mp', '余额提现');
                    if ($rs['status'] == 0) {
                        \app\common\Business::addmoney(aid, bid, $money, '余额提现失败返还');
                        return json(['status' => 0, 'msg' => $rs['msg']]);
                    } else {
                        $record['weixin'] = t('会员') . 'ID：' . $mid;
                        $record['status'] = 3;
                        $record['paytime'] = time();
                        $record['paynum'] = $rs['resp']['payment_no'];
                        $id = db('business_withdrawlog')->insertGetId($record);

                        //提现成功通知
                        $tmplcontent = [];
                        $tmplcontent['first'] = '您的提现申请已打款，请留意查收';
                        $tmplcontent['remark'] = '请点击查看详情~';
                        $tmplcontent['money'] = (string)$record['money'];
                        $tmplcontent['timet'] = date('Y-m-d H:i', $record['createtime']);
                        \app\common\Wechat::sendtmpl(aid, $mid, 'tmpl_tixiansuccess', $tmplcontent, m_url('admin/index/index'));
                        //短信通知
                        $member = Db::name('member')->where('id', $mid)->find();
                        if ($member['tel']) {
                            $tel = $member['tel'];
                            \app\common\Sms::send(aid, $tel, 'tmpl_tixiansuccess', ['money' => $record['money']]);
                        }
                        return json(['status' => 1, 'msg' => $rs['msg']]);
                    }
                }
                if ($binfo['weixin'] == '') {
                    return json(['status' => 0, 'msg' => '请填写完整提现信息', 'url' => 'txset']);
                }
                $record['weixin'] = $binfo['weixin'];
            }
            if ($post['paytype'] == '支付宝') {
                $record['aliaccount'] = $binfo['aliaccount'];
            }
            if ($post['paytype'] == '银行卡') {
                $record['bankname'] = $binfo['bankname'];
                $record['bankcarduser'] = $binfo['bankcarduser'];
                $record['bankcardnum'] = $binfo['bankcardnum'];
            }
            $recordid = db('business_withdrawlog')->insertGetId($record);

            \app\common\Business::addmoney(aid, bid, -$money, '余额提现');

            return $this->json(['status' => 1, 'msg' => '提交成功,请等待打款']);
        }
        $userinfo = db('business')->where(['id' => bid])->field('id,money,weixin,aliaccount,bankname,bankcarduser,bankcardnum')->find();

        $rdata = [];
        $rdata['userinfo'] = $userinfo;
        $rdata['sysset'] = $set;
        return $this->json($rdata);
    }

    //余额宝明细
    function yuebaolog()
    {
        $pagenum = input('post.pagenum');
        if (!$pagenum) $pagenum = 1;
        $order = 'id desc';
        $pernum = 20;
        $where = [];
        $where[] = ['member_moneylog.aid', '=', aid];
        if (input('param.keyword')) $where[] = ['member.nickname', 'like', '%' . trim(input('param.keyword')) . '%'];
        if (input('?param.status') && input('param.status') !== '') $where[] = ['member_moneylog.status', '=', input('param.status')];
        $datalist = Db::name('member_yuebao_moneylog')->alias('member_moneylog')->field('member.nickname,member.headimg,member_moneylog.*')->join('member member', 'member.id=member_moneylog.mid')->where($where)->page($pagenum, $pernum)->order($order)->select()->toArray();
        if ($pagenum == 1) {
            $count = 0 + Db::name('member_yuebao_moneylog')->alias('member_moneylog')->field('member.nickname,member.headimg,member_moneylog.*')->join('member member', 'member.id=member_moneylog.mid')->where($where)->count();
        }
        return $this->json(['status' => 1, 'count' => $count, 'data' => $datalist]);
    }

    //余额宝提现记录
    function yuebaowithdrawlog()
    {
        $pagenum = input('post.pagenum');
        $st = input('post.st');
        if (!$pagenum) $pagenum = 1;
        $pernum = 20;
        $where = [];
        $where[] = ['withdrawlog.aid', '=', aid];
        if ($st == 'all') {

        } elseif ($st == '0') {
            $where[] = ['status', '=', 0];
        } elseif ($st == '1') {
            $where[] = ['status', '=', 1];
        } elseif ($st == '2') {
            $where[] = ['status', '=', 2];
        } elseif ($st == '3') {
            $where[] = ['status', '=', 3];
        }

        if (input('param.keyword')) $where[] = ['member.nickname', 'like', '%' . trim(input('param.keyword')) . '%'];
        if (input('?param.status') && input('param.status') !== '') $where[] = ['withdrawlog.status', '=', input('param.status')];
        $datalist = Db::name('member_yuebao_withdrawlog')->alias('withdrawlog')->field('member.nickname,member.headimg,withdrawlog.*')->join('member member', 'member.id=withdrawlog.mid')->where($where)->page($pagenum, $pernum)->order('withdrawlog.id desc')->select()->toArray();
        if ($pagenum == 1) {
            $count = 0 + Db::name('member_yuebao_withdrawlog')->alias('withdrawlog')->field('member.nickname,member.headimg,withdrawlog.*')->join('member member', 'member.id=withdrawlog.mid')->where($where)->count();
        }
        return $this->json(['status' => 1, 'count' => $count, 'data' => $datalist]);
    }

    //余额宝提现明细
    function yuebaowithdrawdetail()
    {
        $id = input('param.id/d');
        $info = Db::name('member_yuebao_withdrawlog')->where(['aid' => aid, 'id' => $id])->find();
        $member = Db::name('member')->where(['id' => $info['mid']])->find();
        $info['nickname'] = $member['nickname'];
        $info['headimg'] = $member['headimg'];
        return $this->json(['status' => 1, 'info' => $info]);
    }

    //余额宝提现审核通过
    function yuebaowithdrawpass()
    {
        $id = input('post.id/d');
        $info = Db::name('member_yuebao_withdrawlog')->where(['aid' => aid, 'id' => $id])->update(['status' => 1, 'reason' => '']);
        return $this->json(['status' => 1, 'msg' => '操作成功']);
    }

    //余额宝提现审核不通过
    function yuebaowithdrawnopass()
    {
        $id = input('post.id/d');
        $reason = input('post.reason');
        Db::name('member_yuebao_withdrawlog')->where(['aid' => aid, 'id' => $id])->update(['status' => 2, 'reason' => $reason]);
        $info = Db::name('member_yuebao_withdrawlog')->where(['aid' => aid, 'id' => $id])->find();
        \app\common\Member::addyuebaomoney(aid, $info['mid'], $info['txmoney'], t('余额宝') . '收益提现返还', 4);
        //提现失败通知
        $tmplcontent = [];
        $tmplcontent['first'] = '您的提现申请被商家驳回，可与商家协商沟通。';
        $tmplcontent['remark'] = $reason . '，请点击查看详情~';
        $tmplcontent['money'] = (string)$info['txmoney'];
        $tmplcontent['time'] = date('Y-m-d H:i', $info['createtime']);
        \app\common\Wechat::sendtmpl(aid, $info['mid'], 'tmpl_tixianerror', $tmplcontent, m_url('/pages/my/usercenter'));
        //订阅消息
        $tmplcontent = [];
        $tmplcontent['amount1'] = $info['txmoney'];
        $tmplcontent['time3'] = date('Y-m-d H:i', $info['createtime']);
        $tmplcontent['thing4'] = $reason;

        $tmplcontentnew = [];
        $tmplcontentnew['thing1'] = '提现失败';
        $tmplcontentnew['amount2'] = $info['txmoney'];
        $tmplcontentnew['date4'] = date('Y-m-d H:i', $info['createtime']);
        $tmplcontentnew['thing12'] = $reason;
        \app\common\Wechat::sendwxtmpl(aid, $info['mid'], 'tmpl_tixianerror', $tmplcontentnew, 'pages/my/usercenter', $tmplcontent);
        //短信通知
        $member = Db::name('member')->where(['id' => $info['mid']])->find();
        if ($member['tel']) {
            \app\common\Sms::send(aid, $member['tel'], 'tmpl_tixianerror', ['reason' => $reason]);
        }
        return $this->json(['status' => 1, 'msg' => '操作成功']);
    }

    //余额宝提现改为打款
    function yuebaowidthdsetydk()
    {
        $id = input('post.id/d');
        Db::name('member_yuebao_withdrawlog')->where(['aid' => aid, 'id' => $id])->update(['status' => 3, 'reason' => '']);
        $info = Db::name('member_yuebao_withdrawlog')->where(['aid' => aid, 'id' => $id])->find();
        //提现成功通知
        $tmplcontent = [];
        $tmplcontent['first'] = '您的提现申请已打款，请留意查收';
        $tmplcontent['remark'] = '请点击查看详情~';
        $tmplcontent['money'] = (string)$info['money'];
        $tmplcontent['timet'] = date('Y-m-d H:i', $info['createtime']);
        \app\common\Wechat::sendtmpl(aid, $info['mid'], 'tmpl_tixiansuccess', $tmplcontent, m_url('pages/my/usercenter'));
        //订阅消息
        $tmplcontent = [];
        $tmplcontent['amount1'] = $info['money'];
        $tmplcontent['thing3'] = $info['paytype'];
        $tmplcontent['time5'] = date('Y-m-d H:i');

        $tmplcontentnew = [];
        $tmplcontentnew['amount3'] = $info['money'];
        $tmplcontentnew['phrase9'] = $info['paytype'];
        $tmplcontentnew['date8'] = date('Y-m-d H:i');
        \app\common\Wechat::sendwxtmpl(aid, $info['mid'], 'tmpl_tixiansuccess', $tmplcontentnew, 'pages/my/usercenter', $tmplcontent);
        //短信通知
        $member = Db::name('member')->where(['id' => $info['mid']])->find();
        if ($member['tel']) {
            \app\common\Sms::send(aid, $member['tel'], 'tmpl_tixiansuccess', ['money' => $info['money']]);
        }
        return $this->json(['status' => 1, 'msg' => '操作成功']);
    }

    //余额宝提现 微信打款
    function yuebaowidthdwxdakuan()
    {
        $id = input('post.id/d');
        $info = Db::name('member_yuebao_withdrawlog')->where(['aid' => aid, 'id' => $id])->find();
        if ($info['status'] != 1) return $this->json(['status' => 0, 'msg' => '已审核状态才能打款']);
        $rs = \app\common\Wxpay::transfers(aid, $info['mid'], $info['money'], $info['ordernum'], $info['platform'], t('余额宝') . '提现');
        if ($rs['status'] == 0) {
            return $this->json(['status' => 0, 'msg' => $rs['msg']]);
        } else {
            Db::name('member_yuebao_withdrawlog')->where(['aid' => aid, 'id' => $id])->update(['status' => 3, 'paytime' => time(), 'paynum' => $rs['resp']['payment_no']]);
            //提现成功通知
            $tmplcontent = [];
            $tmplcontent['first'] = '您的提现申请已打款，请留意查收';
            $tmplcontent['remark'] = '请点击查看详情~';
            $tmplcontent['money'] = (string)$info['money'];
            $tmplcontent['timet'] = date('Y-m-d H:i', $info['createtime']);
            \app\common\Wechat::sendtmpl(aid, $info['mid'], 'tmpl_tixiansuccess', $tmplcontent, m_url('pages/my/usercenter'));
            //订阅消息
            $tmplcontent = [];
            $tmplcontent['amount1'] = $info['money'];
            $tmplcontent['thing3'] = $info['paytype'];
            $tmplcontent['time5'] = date('Y-m-d H:i');

            $tmplcontentnew = [];
            $tmplcontentnew['amount3'] = $info['money'];
            $tmplcontentnew['phrase9'] = $info['paytype'];
            $tmplcontentnew['date8'] = date('Y-m-d H:i');
            \app\common\Wechat::sendwxtmpl(aid, $info['mid'], 'tmpl_tixiansuccess', $tmplcontentnew, 'pages/my/usercenter', $tmplcontent);
            //短信通知
            $member = Db::name('member')->where(['id' => $info['mid']])->find();
            if ($member['tel']) {
                $tel = $member['tel'];
                \app\common\Sms::send(aid, $tel, 'tmpl_tixiansuccess', ['money' => $info['money']]);
            }
            return $this->json(['status' => 1, 'msg' => $rs['msg']]);
        }
    }
}