<?php


//表单提交记录
namespace app\controller;

use think\facade\Db;

class ApiAdminForm extends ApiAdmin
{

    public function formlog()
    {
        $pagenum = input('post.pagenum');
        $st = input('post.st');
        if (!$pagenum) $pagenum = 1;
        $pernum = 20;
        $where = [];
        $where[] = ['aid', '=', aid];
        $where[] = ['bid', '=', bid];

        if (!input('?param.st') || $st === '') {
            $st = 'all';
        }
        if ($st == 'all') {

        } elseif ($st == '0') {
            $where[] = ['status', '=', 0];
            $where[] = Db::raw('payorderid is null or paystatus=1');
        } elseif ($st == '1') {
            $where[] = ['status', '=', 1];
        } elseif ($st == '2') {
            $where[] = ['status', '=', 2];
        } elseif ($st == '10') {
            $where[] = ['status', '=', 0];
            $where[] = ['paystatus', '=', 0];
            $where[] = ['payorderid', '<>', ''];
        }

        //$where['status'] = 1;
        $datalist = Db::name('form_order')->field('*,from_unixtime(createtime)createtime,from_unixtime(paytime)paytime')->where($where)->page($pagenum, $pernum)->order('id desc')->select()->toArray();
        if (!$datalist) $datalist = [];
        if (request()->isPost()) {
            return $this->json(['status' => 1, 'data' => $datalist]);
        }
        $count = Db::name('form_order')->where($where)->count();
        $rdata = [];
        $rdata['count'] = $count;
        $rdata['datalist'] = $datalist;
        $rdata['pernum'] = $pernum;
        $rdata['st'] = $st;
        return $this->json($rdata);
    }

    //表单提交记录
    public function formdetail()
    {
        $id = input('param.id/d');
        $detail = Db::name('form_order')->where('aid', aid)->where('bid', bid)->where('id', $id)->find();
        if (!$detail) return json(['status' => -4, 'msg' => '记录不存在']);
        $detail['paytime'] = date('Y-m-d H:i:s', $detail['paytime']);
        $detail['createtime'] = date('Y-m-d H:i:s', $detail['createtime']);
        $member = Db::name('member')->where('id', $detail['mid'])->find();
        $detail['headimg'] = $member['headimg'];
        $detail['nickname'] = $member['nickname'];
        $form = Db::name('form')->where('aid', aid)->where('bid', bid)->where('id', $detail['formid'])->find();
        $formcontent = json_decode($form['content'], true);
        $rdata = [];
        $rdata['form'] = $form;
        $rdata['formcontent'] = $formcontent;
        $rdata['detail'] = $detail;
        return $this->json($rdata);
    }

    //改状态
    public function formsetst()
    {
        $id = input('param.id/d');
        $st = input('param.st/d');
        $istuikuan = input('post.istuikuan/d');
        $istuikuan = 1;

        $order = Db::name('form_order')->where('aid', aid)->where('bid', bid)->where('id', $id)->find();
        if (!$order) return json(['status' => 1, 'msg' => '操作失败']);

        if ($st == 2 && $istuikuan == 1) {
            $order['totalprice'] = $order['money'];
            $rs = \app\common\Order::refund($order, $order['money'], input('post.reason'));
            if ($rs['status'] == 0) {
                return json(['status' => 0, 'msg' => $rs['msg']]);
            }
            Db::name('form_order')->where('aid', aid)->where('bid', bid)->where('id', $order['id'])->update(['isrefund' => 1]);
        }
        if ($st == 2) {
            $reason = input('post.reason');
            Db::name('form_order')->where('aid', aid)->where('bid', bid)->where('id', $order['id'])->update(['status' => $st, 'reason' => $reason]);
        } else {
            Db::name('form_order')->where('aid', aid)->where('bid', bid)->where('id', $order['id'])->update(['status' => $st]);
        }
        //审核结果通知
        $tmplcontent = [];
        $tmplcontent['first'] = ($st == 1 ? '恭喜您的提交审核通过' : '抱歉您的提交未审核通过');
        $tmplcontent['remark'] = ($st == 1 ? '' : ($reason . '，')) . '请点击查看详情~';
        $tmplcontent['keyword1'] = $order['title'];
        $tmplcontent['keyword2'] = ($st == 1 ? '已通过' : '未通过');
        $tmplcontent['keyword3'] = date('Y年m月d日 H:i');
        \app\common\Wechat::sendtmpl(aid, $order['mid'], 'tmpl_shenhe', $tmplcontent, m_url('pages/form/formlog'));
        //订阅消息
        $tmplcontent = [];
        $tmplcontent['thing8'] = $order['title'];
        $tmplcontent['phrase2'] = ($st == 1 ? '已通过' : '未通过');
        $tmplcontent['thing4'] = $reason;

        $tmplcontentnew = [];
        $tmplcontentnew['thing2'] = $order['title'];
        $tmplcontentnew['phrase1'] = ($st == 1 ? '已通过' : '未通过');
        $tmplcontentnew['thing5'] = $reason;
        \app\common\Wechat::sendwxtmpl(aid, $order['mid'], 'tmpl_shenhe', $tmplcontentnew, 'pages/form/formlog', $tmplcontent);

    }

    //删除
    public function formdel()
    {
        $id = input('param.id/d');
        Db::name('form_order')->where('aid', aid)->where('bid', bid)->where('id', $id)->delete();
        return json(['status' => 1, 'msg' => '删除成功']);
    }
}