<?php


//管理员中心 - 会员列表
namespace app\controller;

use think\facade\Db;

class ApiAdminMember extends ApiAdmin
{
    public function initialize()
    {
        parent::initialize();
        if (bid != 0) die(json_encode(['status' => -4, 'msg' => '无权限操作']));
    }

    public function index()
    {
        $pagenum = input('post.pagenum');
        if (!$pagenum) $pagenum = 1;
        $pernum = 20;
        $where = [];
        $where[] = ['aid', '=', aid];
        if (input('param.keyword')) {
            $where[] = ['nickname|realname|tel', 'like', '%' . input('param.keyword') . '%'];
        }
        $datalist = Db::name('member')->where($where)->page($pagenum, $pernum)->order('id desc')->select()->toArray();
        if (!$datalist) $datalist = [];
        foreach ($datalist as $k => $v) {
            $datalist[$k]['createtime'] = date('Y-m-d H:i', $v['createtime']);
        }
        if ($pagenum == 1) {
            $count = Db::name('member')->where($where)->count();
        }
        $rdata = [];
        $rdata['status'] = 1;
        $rdata['count'] = $count;
        $rdata['datalist'] = $datalist;
        $rdata['auth_data'] = $this->auth_data;
        return $this->json($rdata);
    }

    //会员详情
    public function detail()
    {
        $mid = input('param.mid/d');
        $member = db('member')->where(['aid' => aid, 'id' => $mid])->find();
        $member['levelname'] = db('member_level')->where(['id' => $member['levelid']])->value('name');
        $member['createtime'] = date('Y-m-d H:i', $member['createtime']);
        $rdata = [];
        $rdata['member'] = $member;

        $default_cid = Db::name('member_level_category')->where('aid', aid)->where('isdefault', 1)->value('id');
        $default_cid = $default_cid ? $default_cid : 0;

        $rdata['levelList'] = Db::name('member_level')->field('id,name')->where('aid', aid)->where('cid', $default_cid)->order('sort,id')->select()->toArray();
        return $this->json($rdata);
    }

    //充值
    public function recharge()
    {
        $mid = input('post.rechargemid/d');
        $money = floatval(input('post.rechargemoney'));
        if ($money == 0) {
            return $this->json(['status' => 0, 'msg' => '请输入充值金额']);
        }
        $info = db('member')->where('aid', aid)->where('id', $mid)->find();
        if (!$info) return $this->json(['status' => 0, 'msg' => '未找到该会员' . t('会员')]);
        \app\common\Member::addmoney(aid, $mid, $money, '商家充值，操作员：' . $this->user['un']);
        return $this->json(['status' => 1, 'msg' => '充值成功']);
    }

    //改积分
    public function addscore()
    {
        $mid = input('post.rechargemid/d');
        $score = floatval(input('post.rechargescore'));
        if ($score == 0) {
            return $this->json(['status' => 0, 'msg' => '请输入' . t('积分') . '数量']);
        }
        $info = db('member')->where('aid', aid)->where('id', $mid)->find();
        if (!$info) return $this->json(['status' => 0, 'msg' => '未找到该' . t('会员')]);
        \app\common\Member::addscore(aid, $mid, $score, '商家增加' . t('积分'));
        return $this->json(['status' => 1, 'msg' => '增加成功']);
    }

    //备注
    public function remark()
    {
        $mid = input('post.remarkmid/d');
        $remark = input('post.remark');
        db('member')->where('aid', aid)->where('id', $mid)->update(['remark' => $remark]);
        return $this->json(['status' => 1, 'msg' => '操作成功']);
    }

    //修改等级
    public function changelv()
    {
        $mid = input('post.changemid/d');
        $levelid = input('post.levelid');
        db('member')->where('aid', aid)->where('id', $mid)->update(['levelid' => $levelid]);
        \app\common\Wechat::updatemembercard(aid, $mid);
        return $this->json(['status' => 1, 'msg' => '操作成功']);
    }
}