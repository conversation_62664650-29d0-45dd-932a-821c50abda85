<?php

namespace app\controller;

use think\facade\Db;

class ApiRestaurantShop extends ApiCommon
{
    public function initialize()
    {
        parent::initialize();
        //$this->checklogin();
    }

    //点餐页面
    public function index()
    {
        $bid = input('param.bid/d', 0);
        $tableId = input('param.tableId/d', 0);
        if (!$tableId) {
            return $this->json(['status' => 0, 'msg' => '桌号错误']);
        }
        if (!$bid) $bid = 0;

        // 获取商家信息和系统设置
        $businessAndSettings = $this->getBusinessAndSettings($bid);
        if ($businessAndSettings['error']) {
            return $this->json($businessAndSettings['error']);
        }

        $business = $businessAndSettings['business'];
        $shop_set = $businessAndSettings['shop_set'];

        // 检查营业状态
        $businessCheck = $this->checkBusinessStatus($shop_set);
        if ($businessCheck['error']) {
            return $this->json($businessCheck['error']);
        }

        // 获取分类和商品数据
        $cid = input('param.cid');
        if (!$cid) $cid = 0;
        $clist = $this->getCategoriesWithProducts($bid, $cid);

        // 获取购物车数据
        $cartList = $this->getCartData($bid, $tableId);
        // 计算商品和分类数量统计
        $statistics = $this->calculateStatistics($clist, $cartList);

        $rdata = [
            'status' => 1,
            'data' => $clist,
            'cartList' => $cartList,
            'numtotal' => $statistics['numtotal'],
            'numCat' => $statistics['numCat'],
            'business' => $business,
            'sysset' => $shop_set,
            'table' => ['name' => $tableName ?? '']
        ];
        return $this->json($rdata);
    }

    /**
     * 获取商家信息和系统设置（带缓存）
     */
    private function getBusinessAndSettings($bid)
    {
        $cacheKey = "restaurant_business_settings_" . aid . "_" . $bid;
        $cached = cache($cacheKey);

        if ($cached) {
            return $cached;
        }

        if ($bid != 0) {
            $business = Db::name('business')->where('aid', aid)->where('id', $bid)
                ->field('id,name,logo,content,pics,desc,tel,address,sales,start_hours,end_hours,zhengming')
                ->find();
            if ($business) {
                $business['pic'] = explode(',', $business['pics'])[0];
                $business['zhengming'] = $business['zhengming'] ? explode(',', $business['zhengming']) : [];
            }
        } else {
            $business = Db::name('admin_set')->where('aid', aid)
                ->field('id,name,logo,desc,tel,address')
                ->find();
        }

        $shop_set = Db::name('restaurant_shop_sysset')->where('aid', aid)->where('bid', $bid)->find();
        if ($shop_set['banner']) {
            $business['pic'] = $shop_set['banner'];
        }

        $result = [
            'business' => $business,
            'shop_set' => $shop_set,
            'error' => null
        ];

        // 缓存5分钟
        cache($cacheKey, $result, 300);

        return $result;
    }

    /**
     * 检查商家营业状态
     */
    private function checkBusinessStatus($shop_set)
    {
        if ($shop_set['status'] == 0) {
            return ['error' => ['status' => 0, 'msg' => '该商家未开启点餐']];
        }

        if ($shop_set['start_hours'] != $shop_set['end_hours']) {
            $start_time = strtotime(date('Y-m-d ' . $shop_set['start_hours']));
            $end_time = strtotime(date('Y-m-d ' . $shop_set['end_hours']));
            if (($start_time < $end_time && ($start_time > time() || $end_time < time()))
                || ($start_time >= $end_time && ($start_time > time() && $end_time < time()))) {
                return ['error' => ['status' => 0, 'msg' => '该商家不在营业时间']];
            }
        }

        return ['error' => null];
    }

    /**
     * 获取分类和商品数据（优化版本）
     */
    private function getCategoriesWithProducts($bid, $cid)
    {
        // 获取分类列表
        $clist = Db::name('restaurant_product_category')
            ->where('aid', aid)
            ->where('bid', $bid)
            ->where('pid', $cid)
            ->where('status', 1)
            ->where('is_shop', 1)
            ->order('sort desc,id')
            ->select()
            ->toArray();

        if (empty($clist)) {
            return [];
        }

        // 构建商品查询条件
        $categoryIds = array_column($clist, 'id');
        $week = date("w");
        if ($week == 0) $week = 7;
        $nowtime = time();
        $nowhm = date('H:i');

        // 批量查询所有商品（优化查询条件）
        $products = Db::name('restaurant_product')
            ->field("pic,id,name,stock,sales,market_price,sell_price,lvprice,lvprice_data,guigedata,limit_start,limit_per,stock_daily,sales_daily,cid")
            ->where([
                ['aid', '=', aid],
                ['bid', '=', $bid],
                ['ischecked', '=', 1]
            ])
            ->where(function($query) use ($week, $nowtime, $nowhm) {
                $query->whereRaw("find_in_set($week, status_week)");
                $query->whereRaw("`status`=1 or (`status`=2 and unix_timestamp(start_time)<=$nowtime and unix_timestamp(end_time)>=$nowtime) or (`status`=3 and ((start_hours<end_hours and start_hours<='$nowhm' and end_hours>='$nowhm') or (start_hours>=end_hours and (start_hours<='$nowhm' or end_hours>='$nowhm'))))");
            })
            ->orderRaw('if(stock_daily>sales_daily,1,0) desc,sort desc,id desc')
            ->select()
            ->toArray();

        if (empty($products)) {
            return [];
        }

        // 格式化商品数据
        $products = $this->formatprolist($products);

        // 批量查询所有商品规格
        $productIds = array_column($products, 'id');
        $allSpecs = $this->batchGetProductSpecs($productIds);

        // 将商品按分类分组
        $productsByCategory = [];
        foreach ($products as $product) {
            if (!$product['cid']) continue;

            $cids = explode(',', $product['cid']);
            foreach ($cids as $categoryId) {
                if (in_array($categoryId, $categoryIds)) {
                    if (!isset($productsByCategory[$categoryId])) {
                        $productsByCategory[$categoryId] = [];
                    }

                    // 添加规格信息
                    $product['gglist'] = $allSpecs[$product['id']] ?? [];
                    $product['ggcount'] = count($product['gglist']);

                    // 设置默认限制值
                    if ($product['limit_start'] == 0) $product['limit_start'] = 1;
                    if ($product['limit_per'] == 0) $product['limit_per'] = 999999;

                    $productsByCategory[$categoryId][] = $product;
                }
            }
        }

        // 为分类添加商品列表
        foreach ($clist as $k => $category) {
            if (isset($productsByCategory[$category['id']])) {
                $clist[$k]['prolist'] = $productsByCategory[$category['id']];
            } else {
                unset($clist[$k]);
            }
        }

        return array_values($clist);
    }

    /**
     * 批量获取商品规格（带缓存）
     */
    private function batchGetProductSpecs($productIds)
    {
        if (empty($productIds)) {
            return [];
        }

        $specsByProduct = [];
        $uncachedIds = [];

        // 检查缓存
        foreach ($productIds as $productId) {
            $cacheKey = "product_specs_" . $productId;
            $cached = cache($cacheKey);
            if ($cached !== false) {
                $specsByProduct[$productId] = $cached;
            } else {
                $uncachedIds[] = $productId;
            }
        }

        // 查询未缓存的规格
        if (!empty($uncachedIds)) {
            $specs = Db::name('restaurant_product_guige')
                ->whereIn('product_id', $uncachedIds)
                ->select()
                ->toArray();

            foreach ($specs as $spec) {
                if (!isset($specsByProduct[$spec['product_id']])) {
                    $specsByProduct[$spec['product_id']] = [];
                }
                $specsByProduct[$spec['product_id']][] = $spec;
            }

            // 缓存新查询的数据（缓存10分钟）
            foreach ($uncachedIds as $productId) {
                $cacheKey = "product_specs_" . $productId;
                $specs = $specsByProduct[$productId] ?? [];
                cache($cacheKey, $specs, 600);
            }
        }

        return $specsByProduct;
    }

    /**
     * 获取购物车数据（优化版本）
     */
    private function getCartData($bid, $tableId)
    {
        // 获取购物车列表
        $cartItems = Db::name('restaurant_shop_cart')
            ->where('aid', aid)
            ->where('bid', $bid)
            ->where('tid', $tableId)
            ->order('createtime desc')
            ->select()
            ->toArray();

        if (empty($cartItems)) {
            return ['list' => [], 'total' => 0, 'totalprice' => '0.00'];
        }

        // 批量获取商品和规格信息
        $productIds = array_unique(array_column($cartItems, 'proid'));
        $specIds = array_unique(array_column($cartItems, 'ggid'));

        // 批量查询商品
        $productList = Db::name('restaurant_product')
            ->field('cid,pic,id,name,stock,sales,market_price,sell_price,lvprice,lvprice_data,sellpoint,guigedata')
            ->whereIn('id', $productIds)
            ->select()
            ->toArray();

        // 转换为以ID为键的数组
        $products = $this->arrayIndexBy($productList, 'id');

        // 批量查询规格
        $specList = Db::name('restaurant_product_guige')
            ->whereIn('id', $specIds)
            ->select()
            ->toArray();

        // 转换为以ID为键的数组
        $specs = $this->arrayIndexBy($specList, 'id');

        $total = 0;
        $totalprice = 0;
        $validItems = [];
        $itemsToDelete = [];

        foreach ($cartItems as $item) {
            $product = $products[$item['proid']] ?? null;
            if (!$product) {
                $itemsToDelete[] = $item['id'];
                continue;
            }

            $spec = $specs[$item['ggid']] ?? null;
            if (!$spec) {
                $itemsToDelete[] = $item['id'];
                continue;
            }

            // 处理会员价格
            if ($product['lvprice'] == 1) {
                $spec = $this->formatguige($spec);
            }

            $item['product'] = $product;
            $item['guige'] = $spec;
            $total += $item['num'];
            $totalprice += $spec['sell_price'] * $item['num'];

            $validItems[] = $item;
        }

        // 批量删除无效的购物车项目
        if (!empty($itemsToDelete)) {
            Db::name('restaurant_shop_cart')->whereIn('id', $itemsToDelete)->delete();
        }

        $totalprice = number_format($totalprice, 2, '.', '');

        return [
            'list' => $validItems,
            'total' => $total,
            'totalprice' => $totalprice
        ];
    }

    /**
     * 计算商品和分类数量统计
     */
    private function calculateStatistics($clist, $cartList)
    {
        $numtotal = [];
        $numCat = [];

        // 初始化商品和分类计数
        foreach ($clist as $category) {
            $numCat[$category['id']] = 0;
            if (isset($category['prolist'])) {
                foreach ($category['prolist'] as $product) {
                    $numtotal[$product['id']] = 0;
                }
            }
        }

        // 计算购物车中的数量
        foreach ($cartList['list'] as $item) {
            $numtotal[$item['proid']] = ($numtotal[$item['proid']] ?? 0) + $item['num'];

            // 计算分类数量
            if (!empty($item['product']['cid'])) {
                $cids = explode(',', $item['product']['cid']);
                foreach ($cids as $cid) {
                    if (isset($numCat[$cid])) {
                        $numCat[$cid] += $item['num'];
                    }
                }
            }
        }

        return [
            'numtotal' => $numtotal,
            'numCat' => $numCat
        ];
    }

    /**
     * 清理相关缓存
     */
    public function clearCache($bid = null, $productIds = [])
    {
        // 清理商家设置缓存
        if ($bid !== null) {
            $cacheKey = "restaurant_business_settings_" . aid . "_" . $bid;
            cache($cacheKey, null);
        }

        // 清理商品规格缓存
        foreach ($productIds as $productId) {
            $cacheKey = "product_specs_" . $productId;
            cache($cacheKey, null);
        }
    }

    /**
     * 将数组转换为以指定字段为键的关联数组
     */
    private function arrayIndexBy($array, $key)
    {
        $result = [];
        foreach ($array as $item) {
            if (isset($item[$key])) {
                $result[$item[$key]] = $item;
            }
        }
        return $result;
    }

 public function index2()
    {
        $bid = input('param.bid/d', 0);
        $tableId = input('param.tableId/d', 0);
        if (!$tableId) {
            return $this->json(['status' => 0, 'msg' => '桌号错误']);
        }
        if (!$bid) $bid = 0;
        if ($bid != 0) {
            $business = Db::name('business')->where('aid', aid)->where('id', $bid)->field('id,name,logo,content,pics,desc,tel,address,sales,start_hours,end_hours,zhengming')->find();
            $business['pic'] = explode(',', $business['pics'])[0];
            $business['zhengming'] = $business['zhengming'] ? explode(',', $business['zhengming']) : [];
        } else {
            $business = Db::name('admin_set')->where('aid', aid)->field('id,name,logo,desc,tel,address')->find();
        }
        $shop_set = Db::name('restaurant_shop_sysset')->where('aid', aid)->where('bid', $bid)->find();
        if ($shop_set['banner']) $business['pic'] = $shop_set['banner'];

        if ($shop_set['status'] == 0) {
            return $this->json(['status' => 0, 'msg' => '该商家未开启点餐']);
        }
        if ($shop_set['start_hours'] != $shop_set['end_hours']) {
            $start_time = strtotime(date('Y-m-d ' . $shop_set['start_hours']));
            $end_time = strtotime(date('Y-m-d ' . $shop_set['end_hours']));
            if (($start_time < $end_time && ($start_time > time() || $end_time < time()))
                || ($start_time >= $end_time && ($start_time > time() && $end_time < time()))) {
                return $this->json(['status' => 0, 'msg' => '该商家不在营业时间']);
            }
        }

        $cid = input('param.cid');
        if (!$cid) $cid = 0;
        $clist = Db::name('restaurant_product_category')->where('aid', aid)->where('bid', $bid)->where('pid', $cid)->where('status', 1)->where('is_shop', 1)->order('sort desc,id')->select()->toArray();
        foreach ($clist as $k => $v) {
            $where = [];
            $where[] = ['aid', '=', aid];
            $where[] = ['bid', '=', $bid];
            //$where[] = ['status','=',1];
            $where[] = ['ischecked', '=', 1];
//			$where[] = ['stock','>',0];
//			$where[] = Db::raw('stock_daily-sales_daily>0');
            $week = date("w");
            if ($week == 0) $week = 7;
            $where[] = Db::raw('find_in_set(' . $week . ',status_week)');
            $nowtime = time();
            $nowhm = date('H:i');
            $where[] = Db::raw("`status`=1 or (`status`=2 and unix_timestamp(start_time)<=$nowtime and unix_timestamp(end_time)>=$nowtime) or (`status`=3 and ((start_hours<end_hours and start_hours<='$nowhm' and end_hours>='$nowhm') or (start_hours>=end_hours and (start_hours<='$nowhm' or end_hours>='$nowhm'))) )");

            $where[] = Db::raw("find_in_set(" . $v['id'] . ",cid)");

            $prolist = Db::name('restaurant_product')->field("pic,id,name,stock,sales,market_price,sell_price,lvprice,lvprice_data,guigedata,limit_start,limit_per,stock_daily,sales_daily")->where($where)->orderRaw('if(stock_daily>sales_daily,1,0) desc,sort desc,id desc')->select()->toArray();
            if (!$prolist) $prolist = [];
            $prolist = $this->formatprolist($prolist);
            if (!$prolist) {
                unset($clist[$k]);
            } else {
                foreach ($prolist as $k2 => $v2) {
                    $gglist = Db::name('restaurant_product_guige')->where('product_id', $v2['id'])->select()->toArray();
                    $prolist[$k2]['gglist'] = $gglist;
                    $prolist[$k2]['ggcount'] = count($gglist);
                    if ($v2['limit_start'] == 0) $v2['limit_start'] = 1;
                    if ($v2['limit_per'] == 0) $v2['limit_per'] = 999999;
                }
                $clist[$k]['prolist'] = $prolist;
            }
        }
        $clist = array_values($clist);

        $list = Db::name('restaurant_shop_cart')->where('aid', aid)->where('bid', $bid)->where('tid', $tableId)->order('createtime desc')->select()->toArray();
        $total = 0;
        $totalprice = 0;
        foreach ($list as $k => $v) {
            $product = Db::name('restaurant_product')->field('cid,pic,id,name,stock,sales,market_price,sell_price,lvprice,lvprice_data,sellpoint,guigedata')->where('id', $v['proid'])->find();
            if (!$product) {
                unset($list[$k]);
                Db::name('restaurant_shop_cart')->where('id', $v['id'])->delete();
                continue;
            }
            //$product = $this->formatproduct($product);
            $guige = Db::name('restaurant_product_guige')->where('id', $v['ggid'])->find();
            if (!$guige) {
                unset($list[$k]);
                Db::name('restaurant_shop_cart')->where('id', $v['id'])->delete();
                continue;
            }
            if ($product['lvprice'] == 1) $guige = $this->formatguige($guige);
            $list[$k]['product'] = $product;
            $list[$k]['guige'] = $guige;
            $total += $v['num'];
            $totalprice += $guige['sell_price'] * $v['num'];
        }
        $totalprice = number_format($totalprice, 2, '.', '');

        $cartList = ['list' => $list, 'total' => $total, 'totalprice' => $totalprice];
        $numtotal = [];
        $numCat = [];
        foreach ($clist as $i => $v) {
            foreach ($v['prolist'] as $j => $pro) {
                $numtotal[$pro['id']] = 0;
                $numCat[$v['id']] = 0;
            }
        }
        foreach ($cartList['list'] as $i => $v) {
            $numtotal[$v['proid']] += $v['num'];
            //分类数量
            if ($v['product']['cid']) {
                $cids = explode(',', $v['product']['cid']);
                if ($cids) {
                    foreach ($cids as $cid)
                        $numCat[$cid] += $v['num'];
                }
            }
        }
        $tableName = '';
        if ($tableId) {
            $tableName = \db('restaurant_table')->where('aid', aid)->where('bid', $bid)->value('name');
        }

        $rdata = [];
        $rdata['status'] = 1;
        $rdata['data'] = $clist;
        $rdata['cartList'] = $cartList;
        $rdata['numtotal'] = $numtotal;
        $rdata['numCat'] = $numCat;
        $rdata['business'] = $business;
        $rdata['sysset'] = $shop_set;
        $rdata['table'] = ['name' => $tableName];
        return $this->json($rdata);
    }

    //菜品列表
    public function getprolist()
    {
        $where = [];
        $where[] = ['aid', '=', aid];
        $where[] = ['ischecked', '=', 1];
        //$where[] = ['status','=',1];
        $week = date("w");
        if ($week == 0) $week = 7;
        $where[] = Db::raw('find_in_set(' . $week . ',status_week)');
        $nowtime = time();
        $nowhm = date('H:i');
        $where[] = Db::raw("`status`=1 or (`status`=2 and unix_timestamp(start_time)<=$nowtime and unix_timestamp(end_time)>=$nowtime) or (`status`=3 and ((start_hours<end_hours and start_hours<='$nowhm' and end_hours>='$nowhm') or (start_hours>=end_hours and (start_hours<='$nowhm' or end_hours>='$nowhm'))) )");

        if (input('param.bid')) {
            $bid = input('param.bid/d');
            $where[] = ['bid', '=', input('param.bid/d')];
        } else {
            $business_sysset = Db::name('business_sysset')->where('aid', aid)->find();
            if (!$business_sysset || $business_sysset['status'] == 0 || $business_sysset['product_isshow'] == 0) {
                $where[] = ['bid', '=', 0];
            }
            $bid = 0;
        }

        if (input('param.field') && input('param.order')) {
            $order = input('param.field') . ' ' . input('param.order') . ',sort desc,id desc';
        } else {
            $order = 'sort desc,id desc';
        }
        //分类
        if (input('param.cid')) {
            $cid = input('post.cid') ? input('post.cid/d') : input('param.cid/d');
            //子分类
            $clist = Db::name('restaurant_product_category')->where('aid', aid)->where('bid', $bid)->where('pid', $cid)->column('id');
            if ($clist) {
                $clist2 = Db::name('restaurant_product_category')->where('aid', aid)->where('bid', $bid)->where('pid', 'in', $clist)->column('id');
                $cCate = array_merge($clist, $clist2, [$cid]);
                if ($cCate) {
                    $whereCid = [];
                    foreach ($cCate as $k => $c2) {
                        $whereCid[] = "find_in_set({$c2},cid)";
                    }
                    $where[] = Db::raw(implode(' or ', $whereCid));
                }
            } else {
                $where[] = Db::raw("find_in_set(" . $cid . ",cid)");
            }
        } else {
            $clist = Db::name('restaurant_product_category')->where('aid', aid)->where('bid', $bid)->where('status', 1)->where('is_shop', 1)->order('sort desc,id')->select()->toArray();
            $cidwhere = '';
            foreach ($clist as $k => $v) {
                if ($k == count($clist) - 1) {
                    $cidwhere .= "find_in_set(" . $v['id'] . ",cid)";
                } else
                    $cidwhere .= " find_in_set(" . $v['id'] . ",cid) or ";
            }
            $where[] = Db::raw($cidwhere);
        }

        if (input('param.keyword')) {
            $where[] = ['name', 'like', '%' . input('param.keyword') . '%'];
        } else {
            return $this->json(['status' => 1, 'data' => []]);
        }

        $pernum = 10;
        $pagenum = input('post.pagenum');
        if (!$pagenum) $pagenum = 1;
//        $datalist = Db::name('restaurant_product')->field("id,pic,name,sales,market_price,sell_price,lvprice,lvprice_data,sellpoint")->where($where)->page($pagenum,$pernum)->order($order)->select()->toArray();
        $prolist = Db::name('restaurant_product')->field("pic,id,name,stock,sales,market_price,sell_price,lvprice,lvprice_data,guigedata,limit_start,limit_per,stock_daily,sales_daily")
            ->where($where)->page($pagenum, $pernum)->orderRaw('if(stock_daily>sales_daily,1,0) desc,' . $order)->select()->toArray();
        if (!$prolist) $prolist = [];
        $prolist = $this->formatprolist($prolist);
        if (!$prolist) {
            unset($clist[$k]);
        } else {
            foreach ($prolist as $k2 => $v2) {
                $gglist = Db::name('restaurant_product_guige')->where('product_id', $v2['id'])->select()->toArray();
                $prolist[$k2]['gglist'] = $gglist;
                $prolist[$k2]['ggcount'] = count($gglist);
                if ($v2['limit_start'] == 0) $v2['limit_start'] = 1;
                if ($v2['limit_per'] == 0) $v2['limit_per'] = 999999;
            }
            $clist[$k]['prolist'] = $prolist;
        }
        if (!$prolist) $prolist = [];
//        $datalist = $this->formatprolist($datalist);
        return $this->json(['status' => 1, 'data' => $prolist]);
    }

    //获取商品列表 评价列表
    public function getdatalist()
    {
        $id = input('param.id/d');
        $st = input('param.st/d');
        $pagenum = input('param.pagenum');
        if (!$pagenum) $pagenum = 1;
        if ($st == 0) {//商品
            $pernum = 20;
            $where = [];
            $where[] = ['aid', '=', aid];
            $where[] = ['bid', '=', $id];
            //$where[] = ['status','=',1];
            $nowtime = time();
            $nowhm = date('H:i');
            $where[] = Db::raw("`status`=1 or (`status`=2 and unix_timestamp(start_time)<=$nowtime and unix_timestamp(end_time)>=$nowtime) or (`status`=3 and ((start_hours<end_hours and start_hours<='$nowhm' and end_hours>='$nowhm') or (start_hours>=end_hours and (start_hours<='$nowhm' or end_hours>='$nowhm'))) )");

            $prolist = Db::name('restaurant_product')->where($where)->page($pagenum, $pernum)->order('id desc')->select()->toArray();
            if (!$prolist) $prolist = [];
            if (request()->isPost()) {
                return $this->json(['status' => 1, 'data' => $prolist]);
            }
        } else {//评价
            $pernum = 10;
            $where = [];
            $where[] = ['aid', '=', aid];
            $where[] = ['bid', '=', $id];
            $where[] = ['status', '=', 1];
            $commentlist = Db::name('business_comment')->where($where)->page($pagenum, $pernum)->order('id desc')->select()->toArray();
            if (!$commentlist) $commentlist = [];
            foreach ($commentlist as $k => $pl) {
                $commentlist[$k]['createtime'] = date('Y-m-d H:i', $pl['createtime']);
                if ($commentlist[$k]['content_pic']) $commentlist[$k]['content_pic'] = explode(',', $commentlist[$k]['content_pic']);
            }
            if (request()->isPost()) {
                return $this->json(['status' => 1, 'data' => $commentlist]);
            }
        }
    }

    //商品
    public function product()
    {
        $proid = input('param.id/d');
        $product = Db::name('restaurant_product')->where('id', $proid)->where('aid', aid)->find();
        if (!$product) return $this->json(['status' => 0, 'msg' => '商品不存在']);
        if ($product['status'] == 0) return $this->json(['status' => 0, 'msg' => '商品未上架']);
        if ($product['ischecked'] != 1) return $this->json(['status' => 0, 'msg' => '商品未审核']);

        if ($product['status'] == 2 && (strtotime($product['start_time']) > time() || strtotime($product['end_time']) < time())) {
            return $this->json(['status' => 0, 'msg' => '商品未上架']);
        }
        if ($product['status'] == 3) {
            $start_time = strtotime(date('Y-m-d ' . $product['start_hours']));
            $end_time = strtotime(date('Y-m-d ' . $product['end_hours']));
            if (($start_time < $end_time && ($start_time > time() || $end_time < time())) || ($start_time >= $end_time && ($start_time > time() && $end_time < time()))) {
                return $this->json(['status' => 0, 'msg' => '商品未上架']);
            }
        }
        if ($product['status'] == 2 || $product['status'] == 3) $product['status'] = 1;

        if (!$product['pics']) $product['pics'] = $product['pic'];
        $product['pics'] = explode(',', $product['pics']);
        $product = $this->formatproduct($product);

        //是否收藏
        $rs = Db::name('member_favorite')->where('aid', aid)->where('mid', mid)->where('proid', $proid)->where('type', 'restaurant_shop')->find();
        if ($rs) {
            $isfavorite = true;
        } else {
            $isfavorite = false;
        }
        //获取评论
        $commentlist = Db::name('restaurant_shop_comment')->where('aid', aid)->where('proid', $proid)->where('status', 1)->order('id desc')->limit(10)->select()->toArray();
        if (!$commentlist) $commentlist = [];
        foreach ($commentlist as $k => $pl) {
            $commentlist[$k]['createtime'] = date('Y-m-d H:i', $pl['createtime']);
            if ($commentlist[$k]['content_pic']) $commentlist[$k]['content_pic'] = explode(',', $commentlist[$k]['content_pic']);
        }
        $commentcount = Db::name('restaurant_shop_comment')->where('aid', aid)->where('proid', $proid)->where('status', 1)->count();
        //添加浏览历史
        if (mid) {
            $rs = Db::name('member_history')->where('aid', aid)->where('mid', mid)->where('proid', $proid)->where('type', 'restaurant_shop')->find();
            if ($rs) {
                Db::name('member_history')->where('id', $rs['id'])->update(['createtime' => time()]);
            } else {
                Db::name('member_history')->insert(['aid' => aid, 'mid' => mid, 'proid' => $proid, 'type' => 'restaurant_shop', 'createtime' => time()]);
            }
        }

        $shopset = Db::name('restaurant_admin_set')->where('aid', aid)->field('shop_comment,shop_showcommission showcommission')->find();
        $sysset = Db::name('admin_set')->where('aid', aid)->field('name,logo,desc,fxjiesuantype,tel,kfurl,gzts,ddbb')->find();

        //预计佣金
        $commission = 0;
        $product['commission_desc'] = '元';
        if ($this->member && $shopset['showcommission'] == 1 && $product['commissionset'] != -1) {
            $userlevel = Db::name('member_level')->where('aid', aid)->where('id', $this->member['levelid'])->find();
            if ($userlevel['can_agent'] != 0) {
                if ($product['commissionset'] == 1) {//按比例
                    $commissiondata = json_decode($product['commissiondata1'], true);
                    if ($commissiondata) {
                        $commission = $commissiondata[$userlevel['id']]['commission1'] * ($product['sell_price'] - ($sysset['fxjiesuantype'] == 2 ? $product['cost_price'] : 0)) * 0.01;
                    }
                } elseif ($product['commissionset'] == 2) {//按固定金额
                    $commissiondata = json_decode($product['commissiondata2'], true);
                    if ($commissiondata) {
                        $commission = $commissiondata[$userlevel['id']]['commission1'];
                    }
                } elseif ($product['commissionset'] == 3) {//提成是积分
                    $commissiondata = json_decode($product['commissiondata3'], true);
                    if ($commissiondata) {
                        $commission = $commissiondata[$userlevel['id']]['commission1'];
                    }
                    $product['commission_desc'] = t('积分');
                } elseif ($product['commissionset'] == 0) {//按会员等级
                    if ($userlevel['commissiontype'] == 1) { //固定金额按单
                        $commission = $userlevel['commission1'];
                    } else {
                        $commission = $userlevel['commission1'] * ($product['sell_price'] - ($sysset['fxjiesuantype'] == 2 ? $product['cost_price'] : 0)) * 0.01;
                    }
                }
            }
        }
        $product['commission'] = round($commission * 100) / 100;
        unset($product['cost_price']);

        if ($product['bid'] != 0) {
            $business = Db::name('business')->where('aid', aid)->where('id', $product['bid'])->field('id,name,logo,desc,tel,address,sales')->find();
        } else {
            $business = $sysset;
        }
        $product['detail'] = \app\common\System::initpagecontent($product['detail'], aid, mid, platform);
        $product['comment_starnum'] = floor($product['comment_score']);

        //促销活动
        $cuxiaolist = Db::name('restaurant_cuxiao')
            ->where('aid', aid)
            ->where('bid', $product['bid'])
            ->where('starttime', '<', time())
            ->where('endtime', '>', time())
            ->order('sort desc')->select()->toArray();
        $newcxlist = [];
        foreach ($cuxiaolist as $k => $v) {
            $gettj = explode(',', $v['gettj']);
            if (!in_array('-1', $gettj) && !in_array($this->member['levelid'], $gettj)) { //不是所有人
                continue;
            }
            if ($v['fwtype'] == 2) {//指定商品可用
                $productids = explode(',', $v['productids']);
                if (!in_array($product['id'], $productids)) {
                    continue;
                }
            }
            if ($v['fwtype'] == 1) {//指定类目可用
                $categoryids = explode(',', $v['categoryids']);
                $cids = explode(',', $product['cid']);
                $clist = Db::name('restaurant_product_category')->where('pid', 'in', $categoryids)->select()->toArray();
                foreach ($clist as $kc => $vc) {
                    $categoryids[] = $vc['id'];
                    $cate2 = Db::name('restaurant_product_category')->where('pid', $vc['id'])->find();
                    $categoryids[] = $cate2['id'];
                }
                if (!array_intersect($cids, $categoryids)) {
                    continue;
                }
            }
            $newcxlist[] = $v;
        }
        //优惠券
        $couponlist = Db::name('coupon')->where('aid', aid)->where('bid', $product['bid'])->where('tolist', 1)->where('type', '5')->where("unix_timestamp(starttime)<=" . time() . " and unix_timestamp(endtime)>=" . time())->order('sort desc')->select()->toArray();
        $newcplist = [];
        foreach ($couponlist as $k => $v) {
            $gettj = explode(',', $v['gettj']);
            if (!in_array('-1', $gettj) && !in_array($this->member['levelid'], $gettj)) { //不是所有人
                continue;
            }
            if ($v['fwtype'] == 2) {//指定商品可用
                $productids = explode(',', $v['productids']);
                if (!in_array($product['id'], $productids)) {
                    continue;
                }
            }
            if ($v['fwtype'] == 1) {//指定类目可用
                $categoryids = explode(',', $v['categoryids']);
                $cids = explode(',', $product['cid']);
                $clist = Db::name('restaurant_product_category')->where('id', 'in', $cids)->select()->toArray();
                foreach ($clist as $kc => $vc) {
                    if ($vc['pid']) {
                        $cids[] = $vc['pid'];
                        $cate2 = Db::name('restaurant_product_category')->where('id', $vc['pid'])->find();
                        if ($cate2 && $cate2['pid']) {
                            $cids[] = $cate2['pid'];
                        }
                    }
                }
                if (!array_intersect($cids, $categoryids)) {
                    continue;
                }
            }
            $haveget = Db::name('coupon_record')->where('aid', aid)->where('mid', mid)->where('couponid', $v['id'])->count();
            $v['haveget'] = $haveget;
            $v['starttime'] = date('m-d H:i', strtotime($v['starttime']));
            $v['endtime'] = date('m-d H:i', strtotime($v['endtime']));
            if ($v['yxqtype'] == 1) {
                $yxqtime = explode(' ~ ', $v['yxqtime']);
                $v['yxqdate'] = strtotime($yxqtime[1]);
            } elseif ($v['yxqtype'] == 2) {
                $v['yxqdate'] = time() + 86400 * $v['yxqdate'];
            } elseif ($v['yxqtype'] == 3) {
                //次日起计算有效期
                $v['yxqdate'] = strtotime(date('Y-m-d')) + 86400 * ($v['yxqdate'] + 1) - 1;
            }
            if ($v['bid'] > 0) {
                $v['bname'] = Db::name('business')->where('aid', aid)->where('id', $v['bid'])->value('name');
            }
            $newcplist[] = $v;
        }

        $rdata = [];
        $rdata['status'] = 1;
        $rdata['title'] = $product['name'];
        $rdata['shopset'] = $shopset;
        $rdata['isfavorite'] = $isfavorite;
        $rdata['product'] = $product;
        $rdata['business'] = $business;
        $rdata['commentlist'] = $commentlist;
        $rdata['commentcount'] = $commentcount;
        $rdata['cartnum'] = Db::name('restaurant_shop_cart')->where('aid', aid)->where('mid', mid)->sum('num');
        $rdata['cuxiaolist'] = $newcxlist;
        $rdata['couponlist'] = $newcplist;
        return $this->json($rdata);
    }

    //获取商品详情
    public function getproductdetail()
    {
        $proid = input('param.id/d');
        $where = [];
        $where[] = ['aid', '=', aid];
        $where[] = ['id', '=', $proid];
        $product = Db::name('restaurant_product')->field("id,pic,name,sales,market_price,sell_price,lvprice,lvprice_data,guigedata,status,ischecked,freighttype,start_time,end_time,start_hours,end_hours,commissionset,commissiondata1,commissiondata2,commissiondata3")->where($where)->find();
        if (!$product) {
            return $this->json(['status' => 0, 'msg' => '商品不存在']);
        }
        $product = $this->formatproduct($product);
        if ($product['status'] == 0) return $this->json(['status' => 0, 'msg' => '商品已下架']);
        if ($product['ischecked'] != 1) return $this->json(['status' => 0, 'msg' => '商品未审核']);
        if ($product['status'] == 2 && (strtotime($product['start_time']) > time() || strtotime($product['end_time']) < time())) {
            return $this->json(['status' => 0, 'msg' => '商品未上架']);
        }
        if ($product['status'] == 3) {
            $start_time = strtotime(date('Y-m-d ' . $product['start_hours']));
            $end_time = strtotime(date('Y-m-d ' . $product['end_hours']));
            if (($start_time < $end_time && ($start_time > time() || $end_time < time())) || ($start_time >= $end_time && ($start_time > time() && $end_time < time()))) {
                return $this->json(['status' => 0, 'msg' => '商品未上架']);
            }
        }
        if ($product['status'] == 2 || $product['status'] == 3) $product['status'] = 1;

        $gglist = Db::name('restaurant_product_guige')->where('product_id', $product['id'])->select()->toArray();
        if ($product['lvprice'] == 1) $gglist = $this->formatgglist($gglist);

        $sysset = Db::name('admin_set')->where('aid', aid)->field('name,logo,desc,fxjiesuantype,tel,kfurl,gzts,ddbb')->find();
        $shopset = Db::name('shop_sysset')->where('aid', aid)->field('showjd,comment,showcommission,hide_sales')->find();

        $guigelist = array();
        foreach ($gglist as $k => $v) {
            //预计佣金
            $commission = 0;
            $v['commission_desc'] = '元';
            //计算佣金
            if ($this->member && $shopset['showcommission'] == 1 && $product['commissionset'] != -1) {
                $userlevel = Db::name('member_level')->where('aid', aid)->where('id', $this->member['levelid'])->find();
                if ($userlevel['can_agent'] != 0) {
                    if ($product['commissionset'] == 1) {//按比例
                        $commissiondata = json_decode($product['commissiondata1'], true);
                        if ($commissiondata) {
                            $commission = $commissiondata[$userlevel['id']]['commission1'] * ($v['sell_price'] - ($sysset['fxjiesuantype'] == 2 ? $v['cost_price'] : 0)) * 0.01;
                        }
                    } elseif ($product['commissionset'] == 2) {//按固定金额
                        $commissiondata = json_decode($product['commissiondata2'], true);
                        if ($commissiondata) {
                            $commission = $commissiondata[$userlevel['id']]['commission1'];
                        }
                    } elseif ($product['commissionset'] == 3) {//提成是积分
                        $commissiondata = json_decode($product['commissiondata3'], true);
                        if ($commissiondata) {
                            $commission = $commissiondata[$userlevel['id']]['commission1'];
                        }
                        $v['commission_desc'] = t('积分');
                    } elseif ($product['commissionset'] == 4 && $product['lvprice'] == 1) {//按价格差
                        $lvprice_data = json_decode($v['lvprice_data'], true);
                        $commission = array_shift($lvprice_data) - $v['sell_price'];
                        if ($commission < 0) $commission = 0;
                    } elseif ($product['commissionset'] == 0) {//按会员等级
                        //fxjiesuantype 0按商品价格,1按成交价格,2按销售利润
                        if ($userlevel['commissiontype'] == 1) { //固定金额按单
                            $commission = $userlevel['commission1'];
                        } else {
                            $commission = $userlevel['commission1'] * ($v['sell_price'] - ($sysset['fxjiesuantype'] == 2 ? $v['cost_price'] : 0)) * 0.01;
                        }
                    }
                }
            }
            $v['commission'] = round($commission * 100) / 100;
            $guigelist[$v['ks']] = $v;
        }
        $guigedata = json_decode($product['guigedata'], true);
        $ggselected = [];
        foreach ($guigedata as $v) {
            $ggselected[] = 0;
        }
        $ks = implode(',', $ggselected);
        return $this->json(['status' => 1, 'product' => $product, 'guigelist' => $guigelist, 'guigedata' => $guigedata, 'ggselected' => $ggselected, 'ks' => $ks, 'shopset' => $shopset]);
    }

    //商品评价
    public function commentlist()
    {
        $proid = input('param.proid/d');
        $pagenum = input('post.pagenum');
        if (!$pagenum) $pagenum = 1;
        $pernum = 20;
        $where = [];
        $where[] = ['aid', '=', aid];
        $where[] = ['proid', '=', $proid];
        $where[] = ['status', '=', 1];
        $datalist = Db::name('restaurant_shop_comment')->where($where)->page($pagenum, $pernum)->order('id desc')->select()->toArray();
        if (!$datalist) $datalist = [];
        foreach ($datalist as $k => $pl) {
            $datalist[$k]['createtime'] = date('Y-m-d H:i', $pl['createtime']);
            if ($datalist[$k]['content_pic']) $datalist[$k]['content_pic'] = explode(',', $datalist[$k]['content_pic']);
        }
        if (request()->isPost()) {
            return $this->json(['status' => 1, 'data' => $datalist]);
        }
        $rdata = [];
        $rdata['datalist'] = $datalist;
        return $this->json($rdata);
    }

    //商品海报
    function getposter()
    {
        $this->checklogin();
        $post = input('post.');
        $platform = platform;
        $page = '/restaurant/shop/product';
        $scene = 'id_' . $post['proid'] . '-pid_' . $this->member['id'];
        //if($platform == 'mp' || $platform == 'h5' || $platform == 'app'){
        //	$page = PRE_URL .'/h5/'.aid.'.html#'. $page;
        //}
        $posterset = Db::name('admin_set_poster')->where('aid', aid)->where('type', 'product')->where('platform', $platform)->order('id')->find();

        $posterdata = Db::name('member_poster')->where('aid', aid)->where('mid', mid)->where('scene', $scene)->where('type', 'restaurant_shop')->where('posterid', $posterset['id'])->find();
        if (true || !$posterdata) {
            $product = Db::name('restaurant_product')->where('id', $post['proid'])->find();
            $product = $this->formatproduct($product);
            $sysset = Db::name('admin_set')->where('aid', aid)->find();
            $textReplaceArr = [
                '[头像]' => $this->member['headimg'],
                '[昵称]' => $this->member['nickname'],
                '[姓名]' => $this->member['realname'],
                '[手机号]' => $this->member['mobile'],
                '[商城名称]' => $sysset['name'],
                '[商品名称]' => $product['name'],
                '[商品销售价]' => $product['sell_price'],
                '[商品市场价]' => $product['market_price'],
                '[商品图片]' => $product['pic'],
            ];

            $poster = $this->_getposter(aid, $platform, $posterset['content'], $page, $scene, $textReplaceArr);
            $posterdata = [];
            $posterdata['aid'] = aid;
            $posterdata['mid'] = $this->member['id'];
            $posterdata['scene'] = $scene;
            $posterdata['page'] = $page;
            $posterdata['type'] = 'product';
            $posterdata['poster'] = $poster;
            $posterdata['createtime'] = time();
            Db::name('member_poster')->insert($posterdata);
        }
        return $this->json(['status' => 1, 'poster' => $posterdata['poster']]);
    }

    //购物车
    public function cart()
    {
        //$this->checklogin();
        $gwcdata = [];
        $cartlist = Db::name('restaurant_shop_cart')->field('id,bid,proid,ggid,num')->where('aid', aid)->where('mid', mid)->order('createtime desc')->select()->toArray();
        if (!$cartlist) $cartlist = [];
        $newcartlist = [];
        foreach ($cartlist as $k => $gwc) {
            if ($newcartlist[$gwc['bid']]) {
                $newcartlist[$gwc['bid']][] = $gwc;
            } else {
                $newcartlist[$gwc['bid']] = [$gwc];
            }
        }
        foreach ($newcartlist as $bid => $gwclist) {
            if ($bid == 0) {
                $business = Db::name('admin_set')->where('aid', aid)->field('id,name,logo,tel')->find();
            } else {
                $business = Db::name('business')->where('aid', aid)->where('id', $bid)->field('id,name,logo,tel')->find();
            }
            $prolist = [];
            foreach ($gwclist as $gwc) {
                $product = Db::name('restaurant_product')->where('aid', aid)->where('status', '<>', 0)->where('id', $gwc['proid'])->find();
                if (!$product) {
                    Db::name('restaurant_shop_cart')->where('aid', aid)->where('proid', $gwc['proid'])->delete();
                    continue;
                }
                $guige = Db::name('restaurant_product_guige')->where('id', $gwc['ggid'])->find();
                if (!$guige) {
                    Db::name('restaurant_shop_cart')->where('aid', aid)->where('ggid', $gwc['ggid'])->delete();
                    continue;
                }
                if ($product['lvprice'] == 1) {
                    $guige = $this->formatguige($guige);
                }
                $prolist[] = ['id' => $gwc['id'], 'checked' => true, 'product' => $product, 'guige' => $guige, 'num' => $gwc['num']];
            }
            $newcartlist[$bid] = ['bid' => $bid, 'checked' => true, 'business' => $business, 'prolist' => $prolist];
        }

        $rdata = [];
        $rdata['cartlist'] = array_values($newcartlist);
        $rdata['tjdatalist'] = [];
        return $this->json($rdata);
    }

     public function addcart()
{
    $post = input('post.');
    $tableId = $post['tableId'] ?? 0;
    $bid = $post['bid'] ?? 0;

    if (!$tableId) {
        return $this->json(['status' => 0, 'msg' => '桌号错误']);
    }
    if (!$bid) {
        return $this->json(['status' => 0, 'msg' => '商户号错误']);
    }

    $proid = $post['proid'] ?? 0;
    if (!$proid) {
        return $this->json(['status' => 0, 'msg' => '产品参数错误']);
    }

    // 获取产品（60秒缓存）
    $product = cache("product_{$proid}");
    if (!$product) {
        $product = Db::name('restaurant_product')
            ->where([
                ['aid', '=', aid],
                ['id', '=', $proid],
                ['status', '<>', 0],
                ['ischecked', '=', 1],
            ])
            ->find();
        if (!$product) {
            return $this->json(['status' => 0, 'msg' => '产品不存在或已下架']);
        }
        cache("product_{$proid}", $product, 60);
    }

    // 获取 ggid（规格）
    if (empty($post['ggid'])) {
        if ($post['num'] > 0) {
            $post['ggid'] = cache("default_ggid_{$proid}") ?: Db::name('restaurant_product_guige')
                ->where('proid', $proid)
                ->cache("default_ggid_{$proid}", 60)
                ->value('id');
        } else {
            $post['ggid'] = Db::name('restaurant_shop_cart')
                ->where([
                    ['aid', '=', aid],
                    ['tid', '=', $tableId],
                    ['bid', '=', $bid],
                    ['proid', '=', $proid],
                ])
                ->order('id desc')
                ->value('ggid');
        }
    }

    // 查询购物车中该产品所有记录（避免多次查询）
    $cartList = Db::name('restaurant_shop_cart')
        ->where([
            ['aid', '=', aid],
            ['tid', '=', $tableId],
            ['bid', '=', $bid],
            ['proid', '=', $proid],
        ])
        ->select()
        ->toArray();

    $hasnum = array_sum(array_column($cartList, 'num'));

    // 限购判断
    if ($post['num'] > 0 && $product['limit_per'] > 0 && ($hasnum + $post['num'] > $product['limit_per'])) {
        return $this->json(['status' => 0, 'msg' => '每单限购' . $product['limit_per'] . '份']);
    }

    // 起售判断
    $num = $post['num'];
    if ($product['limit_start'] > 0) {
        $num = $num > 0 ? max($num, $product['limit_start'] - $hasnum) : max($num, -$hasnum);
    }

    // 查找当前规格对应的购物车项
    $gwc = null;
    foreach ($cartList as $item) {
        if ($item['ggid'] == $post['ggid']) {
            $gwc = $item;
            break;
        }
    }

    // 更新购物车逻辑（可加事务）
    if ($gwc) {
        $newNum = $post['method'] == 'input' ? $num : $gwc['num'] + $num;
        if ($newNum == $gwc['num']) {
            // 不做更新
        } elseif ($newNum <= 0) {
            Db::name('restaurant_shop_cart')->where('id', $gwc['id'])->delete();
        } else {
            Db::name('restaurant_shop_cart')->where('id', $gwc['id'])->update(['num' => $newNum]);
        }
    } elseif ($num > 0) {
        $data = [
            'aid' => aid,
            'bid' => $product['bid'],
            'tid' => $tableId,
            'ggid' => $post['ggid'],
            'createtime' => time(),
            'proid' => $proid,
            'num' => $num
        ];
        Db::name('restaurant_shop_cart')->insert($data);
    }

    // 缓存购物车数量（5秒）
    $cartnumCacheKey = "cartnum_" . aid . "_{$tableId}_{$bid}";
    $cartnum = cache($cartnumCacheKey);
    if ($cartnum === false) {
        $cartnum = Db::name('restaurant_shop_cart')
            ->where([
                ['aid', '=', aid],
                ['tid', '=', $tableId],
                ['bid', '=', $bid],
            ])
            ->sum('num');
        cache($cartnumCacheKey, $cartnum, 5);
    }

    return $this->json([
        'status' => 1,
        'msg' => $gwc && ($gwc['num'] + $num <= 0) ? '移除成功' : '加入购物车成功',
        'cartnum' => $cartnum
    ]);
}

public function addcart2()
    {
       // $this->checklogin();
        $post = input('post.');
        $oldnum = 0;
        $num = $post['num'];
        $tableId = $post['tableId'];
        $bid = $post['bid'];
        if (!$tableId) {
            return $this->json(['status' => 0, 'msg' => '桌号错误']);
        }
        if (!$bid) {
            return $this->json(['status' => 0, 'msg' => '商户号错误']);
        }
        $product = Db::name('restaurant_product')->where('aid', aid)->where('status', '<>', 0)->where('ischecked', 1)->where('id', $post['proid'])->find();
        if (!$product) return $this->json(['status' => 0, 'msg' => '产品不存在或已下架']);
        if (!$post['ggid']) {
            if ($num > 0) {
                $post['ggid'] = Db::name('restaurant_product_guige')->where('proid', $post['proid'])->value('id');
            } else {
                $post['ggid'] = Db::name('restaurant_shop_cart')->where('aid', aid)->where('tid', $tableId)->where('bid',$bid)->where('proid', $post['proid'])->order('id desc')->value('ggid');
            }
        }
        if ($num > 0 && $product['limit_per'] > 0) { //每单限购
            $hasnum = Db::name('restaurant_shop_cart')->where('aid', aid)->where('tid', $tableId)->where('bid',$bid)->where('proid', $post['proid'])->sum('num');
            if ($hasnum + $num > $product['limit_per']) {
                return $this->json(['status' => 0, 'msg' => '每单限购' . $product['limit_per'] . '份']);
            }
        }
        if ($product['limit_start'] > 0) { //有起售数量
            $hasnum = Db::name('restaurant_shop_cart')->where('aid', aid)->where('tid', $tableId)->where('bid',$bid)->where('proid', $post['proid'])->sum('num');
            if ($num > 0) { // +
                if ($hasnum + $num < $product['limit_start']) $num = $product['limit_start'] - $hasnum;
            } else { // -
                if ($hasnum + $num < $product['limit_start']) $num = -$hasnum;
            }
        }

        $gwc = Db::name('restaurant_shop_cart')->where('aid', aid)->where('tid', $tableId)->where('bid',$bid)->where('proid', $post['proid'])->where('ggid', $post['ggid'])->find();
        if ($gwc) $oldnum = $gwc['num'];

        if ($oldnum + $num <= 0) {
            Db::name('restaurant_shop_cart')->where('aid', aid)->where('tid', $tableId)->where('bid',$bid)->where('proid', $post['proid'])->where('ggid', $post['ggid'])->delete();
            $cartnum = Db::name('restaurant_shop_cart')->where('aid', aid)->where('tid', $tableId)->where('bid',$bid)->sum('num');
            return $this->json(['status' => 1, 'msg' => '移除成功', 'cartnum' => $cartnum]);
        }
        if ($gwc) {
            Db::name('restaurant_shop_cart')->where('aid', aid)->where('tid', $tableId)->where('bid',$bid)->where('proid', $post['proid'])->where('ggid', $post['ggid'])->inc('num', $num)->update();
        } else {
            $data = [];
            $data['aid'] = aid;
            $data['bid'] = $product['bid'];
            $data['tid'] = $tableId;
            $data['ggid'] = $post['ggid'];
            $data['createtime'] = time();
            $data['proid'] = $post['proid'];
            $data['num'] = $num;
            Db::name('restaurant_shop_cart')->insert($data);
        }
        $cartnum = Db::name('restaurant_shop_cart')->where('aid', aid)->where('tid', $tableId)->where('bid',$bid)->sum('num');
        return $this->json(['status' => 1, 'msg' => '加入购物车成功', 'cartnum' => $cartnum]);
    }
    public function cartChangenum()
    {
        //$this->checklogin();
        $id = input('post.id/d');
        $num = input('post.num/d');
        if ($num < 1) $num = 1;
        Db::name('restaurant_shop_cart')->where('id', $id)->where('mid', mid)->update(['num' => $num]);
        return $this->json(['status' => 1, 'msg' => '修改成功']);
    }

    public function cartdelete()
    {
        $id = input('post.id/d');
        if (!$id) {
            $bid = input('post.bid/d');
            Db::name('restaurant_shop_cart')->where('bid', $bid)->delete();
            return $this->json(['status' => 1, 'msg' => '删除成功']);
        }
        Db::name('restaurant_shop_cart')->where('id', $id)->delete();
        return $this->json(['status' => 1, 'msg' => '删除成功']);
    }

    public function cartclear()
    {
        $bid = input('param.bid', 0);
        $tableId = input('param.tableId');
        if (empty($tableId)) {
            return $this->json(['status' => 0, 'msg' => '桌号错误']);
        }
        Db::name('restaurant_shop_cart')
            ->where('aid', aid)
            ->where('bid', $bid)
            ->where('tid', $tableId)
            ->delete();
        return $this->json(['status' => 1, 'msg' => '删除成功']);
    }

    //获取促销信息
    public function getcuxiaoinfo()
    {
        $id = input('post.id/d');
        $info = Db::name('restaurant_cuxiao')->where('id', $id)->where('aid', aid)->find();
        if (!$info) {
            return $this->json(['status' => 0, 'msg' => '获取失败']);
        }
        $proinfo = false;
        $gginfo = false;
        if (($info['type'] == 2 || $info['type'] == 3) && $info['proid']) {
            $proinfo = Db::name('restaurant_product')->field('id,name,pic,sell_price')->where('aid', aid)->where('id', $info['proid'])->find();
            $gginfo = Db::name('restaurant_product_guige')->where('aid', aid)->where('id', $info['ggid'])->find();
        }
        return $this->json(['status' => 1, 'info' => $info, 'product' => $proinfo, 'guige' => $gginfo]);
    }

    //订单提交页
    public function buy()
    {
        //$this->checklogin();
        $prodata = explode('-', input('param.prodata'));

        $tableid = input('param.tableId');
        $frompage = input('param.frompage');
        if (!$tableid) return $this->json(['status' => 0, 'msg' => '请先扫描桌台二维码']);

        $tableinfo = Db::name('restaurant_table')->where('id', $tableid)->find();

        //桌子有订单号时,并且订单内有菜，为加菜
        if ($tableinfo['orderid']) {
            $order = Db::name('restaurant_shop_order')->where('aid', aid)->where('id', $tableinfo['orderid'])->find();
            $order['goods_count'] = Db::name('restaurant_shop_order_goods')->where('aid', aid)->where('orderid', $order['id'])->count();
        }

        $ordertype = $order['goods_count'] && ($order['status'] == 0 || $frompage == 'admin') ? 'edit_order' : 'create_order';//加菜，点菜

        $userlevel = Db::name('member_level')->where('aid', aid)->where('id', $this->member['levelid'])->find();
        $adminset = Db::name('admin_set')->where('aid', aid)->find();
        $userinfo = [];
        $userinfo['discount'] = $userlevel['discount'];
        $userinfo['score'] = $this->member['score'];
        $userinfo['score2money'] = $adminset['score2money'];
        $userinfo['scoredk_money'] = round($userinfo['score'] * $userinfo['score2money'], 2);
        $userinfo['scoredkmaxpercent'] = $adminset['scoredkmaxpercent'];
        $userinfo['scoremaxtype'] = 0; //0最大百分比 1最大抵扣金额
        $userinfo['realname'] = $this->member['realname'];
        $userinfo['tel'] = $this->member['tel'];

        $scoredkmaxmoney = 0;
        $allbuydata = [];
        $autofahuo = 0;
        foreach ($prodata as $key => $gwc) {
            list($proid, $ggid, $num) = explode(',', $gwc);
            $product = Db::name('restaurant_product')->field("id,aid,bid,cid,pic,name,sales,market_price,sell_price,lvprice,lvprice_data,freightdata,limit_per,scored_set,scored_val,status,start_time,end_time,start_hours,end_hours")->where('aid', aid)->where('ischecked', 1)->where('id', $proid)->find();
            if (!$product) {
                Db::name('restaurant_shop_cart')->where('aid', aid)->where('proid', $proid)->delete();
                return $this->json(['status' => 0, 'msg' => '产品不存在或已下架']);
            }
            if ($product['status'] == 0) {
                return $this->json(['status' => 0, 'msg' => '商品未上架']);
            }
            if ($product['status'] == 2 && (strtotime($product['start_time']) > time() || strtotime($product['end_time']) < time())) {
                return $this->json(['status' => 0, 'msg' => '商品未上架']);
            }
            if ($product['status'] == 3) {
                $start_time = strtotime(date('Y-m-d ' . $product['start_hours']));
                $end_time = strtotime(date('Y-m-d ' . $product['end_hours']));
                if (($start_time < $end_time && ($start_time > time() || $end_time < time())) || ($start_time >= $end_time && ($start_time > time() && $end_time < time()))) {
                    return $this->json(['status' => 0, 'msg' => '商品未上架']);
                }
            }
            if ($product['limit_per'] > 0 && $num > $product['limit_per']) { //每单限购
                return $this->json(['status' => 0, 'msg' => $product['name'] . '每单限购' . $product['limit_per'] . '份']);
            }
            if ($product['limit_start'] > 0 && $num < $product['limit_start']) { //起售份数
                return $this->json(['status' => 0, 'msg' => $product['name'] . '最低购买' . $product['limit_start'] . '份']);
            }
            if ($product['limit_start'] > 0) { //有起售数量
                $hasnum = Db::name('restaurant_shop_cart')->where('aid', aid)->where('bid', bid)->where('tid',$tableid)->where('proid', $post['proid'])->sum('num');
                if ($num > 0) { // +
                    if ($hasnum + $num < $product['limit_start']) $num = $product['limit_start'] - $hasnum;
                } else { // -
                    if ($hasnum + $num < $product['limit_start']) $num = -$hasnum;
                }
            }

            $guige = Db::name('restaurant_product_guige')->where('id', $ggid)->find();
            if (!$guige) {
                Db::name('restaurant_shop_cart')->where('aid', aid)->where('ggid', $ggid)->delete();
                return $this->json(['status' => 0, 'msg' => '产品该规格不存在或已下架']);
            }
            if ($guige['stock'] < $num || $guige['stock_daily'] - $guige['sales_daily'] < $num) {
                return $this->json(['status' => 0, 'msg' => '库存不足']);
            }
            //$gettj = explode(',',$product['gettj']);
            //if(!in_array('-1',$gettj) && !in_array($this->member['levelid'],$gettj) && (!in_array('0',$gettj) || $this->member['subscribe']!=1)){ //不是所有人
            //	if(!$product['gettjtip']) $product['gettjtip'] = '没有权限购买该商品';
            //	return $this->json(['status'=>0,'msg'=>$product['gettjtip'],'url'=>$product['gettjurl']]);
            //}
            if ($product['limit_per'] > 0) {
                if ($num > $product['limit_per']) {
                    return $this->json(['status' => 0, 'msg' => '每单限购' . $product['limit_per'] . '件']);
                }
            }
            if ($product['lvprice'] == 1) $guige = $this->formatguige($guige);
            if ($product['scored_set'] == 0) {
                if ($userinfo['scoredkmaxpercent'] > 0 && $userinfo['scoredkmaxpercent'] < 100) {
                    $scoredkmaxmoney += $userinfo['scoredkmaxpercent'] * 0.01 * $guige['sell_price'] * $num;
                } else {
                    $scoredkmaxmoney += $guige['sell_price'] * $num;
                }
            } elseif ($product['scored_set'] == 1) {
                $userinfo['scoremaxtype'] = 1;
                $scoredkmaxmoney += $product['scored_val'] * 0.01 * $guige['sell_price'] * $num;
            } elseif ($product['scored_set'] == 2) {
                $userinfo['scoremaxtype'] = 1;
                $scoredkmaxmoney += $product['scored_val'] * $num;
            } else {
                $userinfo['scoremaxtype'] = 1;
                $scoredkmaxmoney += 0;
            }

            if (!$allbuydata[$product['bid']]) $allbuydata[$product['bid']] = [];
            if (!$allbuydata[$product['bid']]['prodata']) $allbuydata[$product['bid']]['prodata'] = [];
            $allbuydata[$product['bid']]['prodata'][] = ['product' => $product, 'guige' => $guige, 'num' => $num];
        }
        $userinfo['scoredkmaxmoney'] = round($scoredkmaxmoney, 2);

        $allproduct_price = 0;
        foreach ($allbuydata as $bid => $buydata) {
            if ($bid != 0) {
                $business = Db::name('business')->where('id', $bid)->field('id,aid,cid,name,logo,tel,address,sales,longitude,latitude,start_hours,end_hours')->find();
                if ($business['start_hours'] != $business['end_hours']) {
                    $start_time = strtotime(date('Y-m-d ' . $business['start_hours']));
                    $end_time = strtotime(date('Y-m-d ' . $business['end_hours']));
                    if (($start_time < $end_time && ($start_time > time() || $end_time < time())) || ($start_time >= $end_time && ($start_time > time() && $end_time < time()))) {
                        return $this->json(['status' => 0, 'msg' => '商家不在营业时间']);
                    }
                }
            } else {
                $business = Db::name('admin_set')->where('aid', aid)->field('id,name,logo,desc,tel')->find();
            }

            $shop_set = Db::name('restaurant_shop_sysset')->where('aid', aid)->where('bid', $bid)->find();
            if ($shop_set['status'] == 0) {
                return $this->json(['status' => 0, 'msg' => '商家未开启点餐']);
            }
            if ($shop_set['start_hours'] != $shop_set['end_hours']) {
                $start_time = strtotime(date('Y-m-d ' . $shop_set['start_hours']));
                $end_time = strtotime(date('Y-m-d ' . $shop_set['end_hours']));
                if (($start_time < $end_time && ($start_time > time() || $end_time < time())) || ($start_time >= $end_time && ($start_time > time() && $end_time < time()))) {
                    return $this->json(['status' => 0, 'msg' => '商家不在点餐时间']);
                }
            }

            $product_price = 0;
            $needzkproduct_price = 0;
            $totalweight = 0;
            $totalnum = 0;
            $prodataArr = [];
            $proids = [];
            $cids = [];
            foreach ($buydata['prodata'] as $prodata) {
                $product_price += $prodata['guige']['sell_price'] * $prodata['num'];
                if ($prodata['product']['lvprice'] == 0) { //未开启会员价
                    $needzkproduct_price += $prodata['guige']['sell_price'] * $prodata['num'];
                }
                $totalweight += $prodata['guige']['weight'] * $prodata['num'];
                $totalnum += $prodata['num'];
                $prodataArr[] = $prodata['product']['id'] . ',' . $prodata['guige']['id'] . ',' . $prodata['num'];
                $proids[] = $prodata['product']['id'];
                $cids = array_merge(explode(',', $prodata['product']['cid']), $cids);
            }
            $prodatastr = implode('-', $prodataArr);


            $leveldk_money = 0;
            if ($userlevel && $userlevel['discount'] > 0 && $userlevel['discount'] < 10) {
                $leveldk_money = $needzkproduct_price * (1 - $userlevel['discount'] * 0.1);
            }
            $leveldk_money = round($leveldk_money, 2);
            $price = $product_price - $leveldk_money;

            if ($ordertype == 'create_order') {
                //满减活动
                $mjset = Db::name('manjian_set')->where('aid', aid)->find();
                if ($mjset && $mjset['status'] == 1) {
                    $mjdata = json_decode($mjset['mjdata'], true);
                } else {
                    $mjdata = array();
                }
                $manjian_money = 0;
                $moneyduan = 0;
                if ($mjdata) {
                    foreach ($mjdata as $give) {
                        if (($product_price - $leveldk_money) * 1 >= $give['money'] * 1 && $give['money'] * 1 > $moneyduan) {
                            $moneyduan = $give['money'] * 1;
                            $manjian_money = $give['jian'] * 1;
                        }
                    }
                }
                if ($manjian_money > 0) {
                    $allbuydata[$bid]['manjian_money'] = round($manjian_money, 2);
                } else {
                    $allbuydata[$bid]['manjian_money'] = 0;
                }

                $newcouponlist = [];
                /*$couponList = Db::name('coupon_record')
                    ->where("bid=-1 or bid=" . $bid)->where('aid', aid)->where('mid', mid)->where('type', 'in', '1,4,5')->where('status', 0)->where('minprice', '<=', $price - $manjian_money)->where('starttime', '<=', time())->where('endtime', '>', time())
                    ->order('id desc')->select()->toArray();*/
               // if (!$couponList) $couponList = [];
                $couponList = [];
                foreach ($couponList as $k => $v) {
                    //$couponList[$k]['starttime'] = date('m-d H:i',$v['starttime']);
                    //$couponList[$k]['endtime'] = date('m-d H:i',$v['endtime']);
                    $couponinfo = Db::name('coupon')->where('aid', aid)->where('id', $v['couponid'])->find();
                    if ($couponinfo['fwtype'] == 2) {//指定商品可用
                        $productids = explode(',', $couponinfo['productids']);
                        if (!array_intersect($proids, $productids)) {
                            continue;
                        }
                    }
                    if ($couponinfo['fwtype'] == 1) {//指定类目可用
                        $categoryids = explode(',', $couponinfo['categoryids']);
                        $clist = Db::name('restaurant_product_category')->where('pid', 'in', $categoryids)->select()->toArray();
                        foreach ($clist as $kc => $vc) {
                            $categoryids[] = $vc['id'];
                            $cate2 = Db::name('restaurant_product_category')->where('pid', $vc['id'])->find();
                            $categoryids[] = $cate2['id'];
                        }
                        if (!array_intersect($cids, $categoryids)) {
                            continue;
                        }
                    }
                    $newcouponlist[] = $v;
                }
                $couponList = $newcouponlist;

                //促销活动
                $cuxiaolist = Db::name('restaurant_cuxiao')->where('aid', aid)->where('bid', $bid)->where("(type in (1,2,3,4) and minprice<=" . ($price - $manjian_money) . ") or ((type=5 or type=6) and minnum<=" . $totalnum . ") ")->where('starttime', '<', time())->where('endtime', '>', time())->order('sort desc')->select()->toArray();
                $newcxlist = [];
                foreach ($cuxiaolist as $k => $v) {
                    $gettj = explode(',', $v['gettj']);
                    if (!in_array('-1', $gettj) && !in_array($this->member['levelid'], $gettj)) { //不是所有人
                        continue;
                    }
                    if ($v['fwtype'] == 2) {//指定商品可用
                        $productids = explode(',', $v['productids']);
                        if (!array_intersect($proids, $productids)) {
                            continue;
                        }
                    }
                    if ($v['fwtype'] == 1) {//指定类目可用
                        $categoryids = explode(',', $v['categoryids']);
                        $clist = Db::name('restaurant_product_category')->where('pid', 'in', $categoryids)->select()->toArray();
                        foreach ($clist as $kc => $vc) {
                            $categoryids[] = $vc['id'];
                            $cate2 = Db::name('restaurant_product_category')->where('pid', $vc['id'])->find();
                            $categoryids[] = $cate2['id'];
                        }
                        if (!array_intersect($cids, $categoryids)) {
                            continue;
                        }
                    }
                    $newcxlist[] = $v;
                }

                $allbuydata[$bid]['couponList'] = $couponList;
                $allbuydata[$bid]['couponCount'] = count($couponList);
                $allbuydata[$bid]['tea_fee'] = $shop_set['tea_fee_status'] == 1 ? round($shop_set['tea_fee'], 2) : 0;
                $allbuydata[$bid]['tea_fee_text'] = $shop_set['tea_fee_text'];
                $allbuydata[$bid]['coupon_money'] = 0;
                $allbuydata[$bid]['coupontype'] = 1;
                $allbuydata[$bid]['couponrid'] = 0;
                $allbuydata[$bid]['cuxiaolist'] = $newcxlist;
                $allbuydata[$bid]['cuxiaoCount'] = count($newcxlist);
                $allbuydata[$bid]['cuxiao_money'] = 0;
                $allbuydata[$bid]['cuxiaotype'] = 0;
                $allbuydata[$bid]['cuxiaoid'] = 0;
                $allbuydata[$bid]['renshu'] = 1;
            } elseif ($ordertype == 'edit_order') {
                $allbuydata[$bid]['coupon_money'] = 0;
                $allbuydata[$bid]['cuxiao_money'] = 0;
                $allbuydata[$bid]['tea_fee'] = 0;
                $allbuydata[$bid]['manjian_money'] = 0;
            }
            
            $shop_sysset = Db::name('restaurant_shop_sysset')->where('aid',aid)->where('bid',$bid)->find();
        
            $taxes_fee = 0;
            $money = 0;
            $num = $order['renshu']?$order['renshu']:1;
            if($shop_sysset){
                $money = $shop_sysset['server_fee_status']?$shop_sysset['person_num']*$num:(round($product_price,2)*($shop_sysset['order_rado']*0.01));
                $taxes_fee = $shop_sysset['taxes_fee'];
            }
            $taxes = round($product_price,2)*($taxes_fee*0.01);
            $taxes = round($taxes,2);
            $money = round($money,2);
            
            $allbuydata[$bid]['bid'] = $bid;
            $allbuydata[$bid]['business'] = $business;
            $allbuydata[$bid]['prodatastr'] = $prodatastr;
            $allbuydata[$bid]['product_price'] = round($product_price, 2);
            $allbuydata[$bid]['leveldk_money'] = $leveldk_money;
            $allbuydata[$bid]['message'] = '';
            $allbuydata[$bid]['field1'] = '';
            $allbuydata[$bid]['field2'] = '';
            $allbuydata[$bid]['field3'] = '';
            $allbuydata[$bid]['field4'] = '';
            $allbuydata[$bid]['field5'] = '';
            
            $allbuydata[$bid]['server_fee'] = $money;
            $allbuydata[$bid]['taxes'] = $taxes;
            $allbuydata[$bid]['totalprice'] = round($product_price, 2)+$money+$taxes+$allbuydata[$bid]['tea_fee'];


            $allproduct_price += $product_price;
        }

        $rdata = [];
        $rdata['status'] = 1;
        $rdata['linkman'] = $userinfo['realname'] ? strval($userinfo['realname']) : strval($userinfo['nickname']);
        $rdata['tel'] = strval($userinfo['tel']);
        $rdata['userinfo'] = $userinfo;
        $rdata['allbuydata'] = $allbuydata;
        $rdata['needLocation'] = $needLocation;
        $rdata['scorebdkyf'] = Db::name('admin_set')->where('aid', aid)->value('scorebdkyf');
        $rdata['order'] = $order ? $order : [];
        $rdata['ordertype'] = $ordertype;
        $rdata['tableinfo'] = $tableinfo;
        return $this->json($rdata);
    }

    public function editOrder()
    {
        $post = input('post.');
        if ($post['frompage'] != 'admin') {
            //非管理员点餐验证登录
            //$this->checklogin();
        }
        if (empty($post['tableid'])) {
            return $this->json(['status' => 0, 'msg' => '请先扫描桌台二维码或选择桌台']);
        }

        //todo 一个桌子同时只能点一单
        $table = Db::name('restaurant_table')->where('aid', aid)->where('id', $post['tableid'])->find();
        //桌子有订单号时,并且订单内有菜，为加菜
        if ($table['orderid']) {
            $order = Db::name('restaurant_shop_order')->where('aid', aid)->where('id', $table['orderid'])->find();
            $order['goods_count'] = Db::name('restaurant_shop_order_goods')->where('aid', aid)->where('orderid', $order['id'])->count();
        }
        $mid = $order['mid'] ? $order['mid'] : 0;
        //$member = Db::name('member')->where('aid', aid)->where('id', $mid)->find();
        $shop_set = Db::name('restaurant_shop_sysset')->where('aid', aid)->where('bid', $order['bid'])->find();

        $ordertype = $order['goods_count'] ? 'edit_order' : 'create_order';//加菜，点菜

        $sysset = Db::name('admin_set')->where('aid', aid)->find();
        //$levelid = Db::name('member')->where('aid', aid)->where('id', $mid)->value('levelid');
        //$userlevel = Db::name('member_level')->where('aid', aid)->where('id', $levelid)->find();

        $buydata = $post['buydata'];
        $couponridArr = [];
        $userinfo = [];
        $userinfo['discount'] = 0;
        //$userinfo['discount'] = $userlevel['discount'];
        //$userinfo['score'] = $member['score'];
        $userinfo['score'] = 0;
        $userinfo['score2money'] = $sysset['score2money'];
        $userinfo['scoredk_money'] = round($userinfo['score'] * $userinfo['score2money'], 2);
        $userinfo['scoredkmaxpercent'] = $sysset['scoredkmaxpercent'];
        $userinfo['scoremaxtype'] = 0; //0最大百分比 1最大抵扣金额
        $userinfo['realname'] = '';
        $userinfo['tel'] = '';
        /*$userinfo['realname'] = $member['realname'];
        $userinfo['tel'] = $member['tel'];*/

        $ordernum = date('ymdHis') . rand(100000, 999999);
        $scoredkmaxmoney = 0;
        $scoremaxtype = 0;
        $i = 0;
        $alltotalprice = 0;
        $alltotalscore = 0;
        foreach ($buydata as $data) {
            $i++;
            $bid = $data['bid'];
            if ($data['prodata']) {
                $prodata = explode('-', $data['prodata']);
            } else {
                return $this->json(['status' => 0, 'msg' => '产品数据错误']);
            }
            if ($bid != 0) {
                $business = Db::name('business')->where('id', $bid)->field('id,aid,cid,name,logo,tel,address,sales,longitude,latitude,start_hours,end_hours')->find();
                if ($business['start_hours'] != $business['end_hours']) {
                    $start_time = strtotime(date('Y-m-d ' . $business['start_hours']));
                    $end_time = strtotime(date('Y-m-d ' . $business['end_hours']));
                    if (($start_time < $end_time && ($start_time > time() || $end_time < time())) || ($start_time >= $end_time && ($start_time > time() && $end_time < time()))) {
                        return $this->json(['status' => 0, 'msg' => '商家不在营业时间']);
                    }
                }
            }
            $shop_set = Db::name('restaurant_shop_sysset')->where('aid', aid)->where('bid', $bid)->find();
            if ($shop_set['status'] == 0) {
                return $this->json(['status' => 0, 'msg' => '该商家未开启点餐']);
            }
            if ($shop_set['start_hours'] != $shop_set['end_hours']) {
                $start_time = strtotime(date('Y-m-d ' . $shop_set['start_hours']));
                $end_time = strtotime(date('Y-m-d ' . $shop_set['end_hours']));
                if (($start_time < $end_time && ($start_time > time() || $end_time < time())) || ($start_time >= $end_time && ($start_time > time() && $end_time < time()))) {
                    return $this->json(['status' => 0, 'msg' => '该商家不在点餐时间']);
                }
            }

            $product_price = 0;
            $needzkproduct_price = 0;
            $givescore = 0; //奖励积分
            $totalweight = 0;//重量
            $totalnum = 0;
            $prolist = [];
            $proids = [];
            $cids = [];

            foreach ($prodata as $key => $pro) {
                $sdata = explode(',', $pro);
                // $sdata[2] = intval($sdata[2]);
                if ($sdata[2] <= 0) return $this->json(['status' => 0, 'msg' => '购买数量有误']);
                $product = Db::name('restaurant_product')->where('aid', aid)->where('ischecked', 1)->where('bid', $bid)->where('id', $sdata[0])->find();
                if (!$product) return $this->json(['status' => 0, 'msg' => '产品不存在或已下架']);

                if ($product['status'] == 0) {
                    return $this->json(['status' => 0, 'msg' => '商品未上架']);
                }
                if ($product['status'] == 2 && (strtotime($product['start_time']) > time() || strtotime($product['end_time']) < time())) {
                    return $this->json(['status' => 0, 'msg' => '商品未上架']);
                }
                if ($product['status'] == 3) {
                    $start_time = strtotime(date('Y-m-d ' . $product['start_hours']));
                    $end_time = strtotime(date('Y-m-d ' . $product['end_hours']));
                    if (($start_time < $end_time && ($start_time > time() || $end_time < time())) || ($start_time >= $end_time && ($start_time > time() && $end_time < time()))) {
                        return $this->json(['status' => 0, 'msg' => '商品未上架']);
                    }
                }

                if ($key == 0) $title = $product['name'];

                $guige = Db::name('restaurant_product_guige')->where('aid', aid)->where('id', $sdata[1])->find();
                if (!$guige) return $this->json(['status' => 0, 'msg' => '产品规格不存在或已下架']);
                if ($guige['stock'] < $sdata[2] || $guige['stock_daily'] - $guige['sales_daily'] < $sdata[2]) {
                    return $this->json(['status' => 0, 'msg' => '库存不足']);
                }

                if ($product['limit_per'] > 0 && $sdata[2] > $product['limit_per']) { //每单限购
                    return $this->json(['status' => 0, 'msg' => $product['name'] . '每单限购' . $product['limit_per'] . '份']);
                }
                if ($product['limit_start'] > 0 && $sdata[2] < $product['limit_start']) { //起售份数
                    return $this->json(['status' => 0, 'msg' => $product['name'] . '最低购买' . $product['limit_start'] . '份']);
                }

                if ($product['lvprice'] == 1) $guige = $this->formatguige($guige);
                $product_price += $guige['sell_price'] * $sdata[2];
                if ($product['lvprice'] == 0) { //未开启会员价
                    $needzkproduct_price += $guige['sell_price'] * $sdata[2];
                }
                $totalweight += $guige['weight'] * $sdata[2];
                $totalnum += $sdata[2];

                if ($product['scored_set'] == 0) {
                    if ($userinfo['scoredkmaxpercent'] > 0 && $userinfo['scoredkmaxpercent'] < 100) {
                        $scoredkmaxmoney += $userinfo['scoredkmaxpercent'] * 0.01 * $guige['sell_price'] * $sdata[2];
                    } else {
                        $scoredkmaxmoney += $guige['sell_price'] * $sdata[2];
                    }
                } elseif ($product['scored_set'] == 1) {
                    $scoremaxtype = 1;
                    $scoredkmaxmoney += $product['scored_val'] * 0.01 * $guige['sell_price'] * $sdata[2];
                } elseif ($product['scored_set'] == 2) {
                    $scoremaxtype = 1;
                    $scoredkmaxmoney += $product['scored_val'] * $sdata[2];
                } else {
                    $scoremaxtype = 1;
                    $scoredkmaxmoney += 0;
                }

                $prolist[] = ['product' => $product, 'guige' => $guige, 'num' => $sdata[2], 'skrs' => $skrs, 'isSeckill' => $isSeckill];

                $proids[] = $product['id'];
                $cids = array_merge($cids, explode(',', $product['cid']));
                $givescore += $guige['givescore'] * $sdata[2];
            }

//            dd($prolist);
            //会员折扣
            $leveldk_money = 0;
            if ($userlevel && $userlevel['discount'] > 0 && $userlevel['discount'] < 10) {
                $leveldk_money = $needzkproduct_price * (1 - $userlevel['discount'] * 0.1);
            }
            $totalprice = $product_price - $leveldk_money;

            //积分抵扣
            $scoredkscore = 0;
            $scoredk_money = 0;
            
            $shop_sysset = Db::name('restaurant_shop_sysset')->where('aid',aid)->where('bid',$order['bid'])->find();
        
            $taxes_fee = 0;
            $money = 0;
            if($shop_sysset){
                $money = $shop_sysset['server_fee_status']?$shop_sysset['person_num']*$order['renshu']:(round($order['totalprice'],2)*($shop_sysset['order_rado']*0.01));
                $taxes_fee = $shop_sysset['taxes_fee'];
            }
            $taxes = round($order['totalprice'],2)*($taxes_fee*0.01);
            $taxes = round($taxes,2);
            $money = round($money,2);
            
            $orderdata = [];
            $orderdata['totalprice'] = $totalprice + $order['totalprice'];
            $orderdata['product_price'] = $product_price + $order['product_price'];
            $orderdata['leveldk_money'] = $leveldk_money + $order['leveldk_money'];  //会员折扣
            $orderdata['givescore'] = $givescore + $order['givescore'];
            $orderdata['message'] = $data['message'] . ' ' . $order['message'];
            $orderdata['server_fee'] = $money;
            $orderdata['taxes'] = $taxes;
            
            

            $orderid = $order['id'];
            Db::name('restaurant_shop_order')->where('id', $table['orderid'])->update($orderdata);
            $orderdata = Db::name('restaurant_shop_order')->where('id', $table['orderid'])->find();

            $payorderid = \app\model\Payorder::createorder(aid, $orderdata['bid'], $orderdata['mid'], 'restaurant_shop', $orderid, $orderdata['ordernum'], $orderdata['title'], $orderdata['totalprice'], $orderdata['scoredkscore']);

            $alltotalprice += $orderdata['totalprice'];
            $alltotalscore += $orderdata['scoredkscore'];

            $istc1 = 0; //设置了按单固定提成时 只将佣金计算到第一个商品里
            $istc2 = 0;
            $istc3 = 0;
            foreach ($prolist as $key => $v) {
                $product = $v['product'];
                $guige = $v['guige'];
                $num = $v['num'];
                $ogdata = [];
                $ogdata['aid'] = aid;
                $ogdata['bid'] = $product['bid'];
                $ogdata['mid'] = $mid;
                $ogdata['orderid'] = $orderid;
                $ogdata['ordernum'] = $orderdata['ordernum'];
                $ogdata['proid'] = $product['id'];
                $ogdata['name'] = $product['name'];
                $ogdata['pic'] = $product['pic'];
                $ogdata['procode'] = $product['procode'];
                $ogdata['ggid'] = $guige['id'];
                $ogdata['ggname'] = $guige['name'];
                //$ogdata['cid'] = $product['cid'];
                $ogdata['num'] = $num;
                $ogdata['cost_price'] = $guige['cost_price'];
                $ogdata['sell_price'] = $guige['sell_price'];
                $ogdata['totalprice'] = $num * $guige['sell_price'];
                $ogdata['status'] = 0;
                $ogdata['createtime'] = time();

                $og_totalprice = $ogdata['totalprice'];

                //计算商品实际金额  商品金额 - 会员折扣 - 积分抵扣 - 满减抵扣 - 优惠券抵扣
                if ($sysset['fxjiesuantype'] == 1 || $sysset['fxjiesuantype'] == 2) {
                    $allproduct_price = $product_price;
                    $og_leveldk_money = 0;
                    $og_coupon_money = 0;
                    $og_scoredk_money = 0;
                    $og_manjian_money = 0;
                    if ($allproduct_price > 0 && $og_totalprice > 0) {
                        if ($leveldk_money) {
                            $og_leveldk_money = $og_totalprice / $allproduct_price * $leveldk_money;
                        }
                        if ($coupon_money) {
                            $og_coupon_money = $og_totalprice / $allproduct_price * $coupon_money;
                        }
                        if ($scoredk_money) {
                            $og_scoredk_money = $og_totalprice / $allproduct_price * $scoredk_money;
                        }
                        if ($manjian_money) {
                            $og_manjian_money = $og_totalprice / $allproduct_price * $manjian_money;
                        }
                    }
                    $og_totalprice = round($og_totalprice - $og_coupon_money - $og_scoredk_money - $og_manjian_money, 2);
                    if ($og_totalprice < 0) $og_totalprice = 0;
                }
                $ogdata['real_totalprice'] = $og_totalprice; //实际商品销售金额

                //计算佣金的商品金额
                $commission_totalprice = $ogdata['totalprice'];
                if ($sysset['fxjiesuantype'] == 1) { //按成交价格
                    $commission_totalprice = $ogdata['real_totalprice'];
                }
                if ($sysset['fxjiesuantype'] == 2) { //按利润提成
                    $commission_totalprice = $ogdata['real_totalprice'] - $guige['cost_price'] * $num;
                }
                if ($commission_totalprice < 0) $commission_totalprice = 0;

                $pid = $member['pid'];
                $agleveldata = Db::name('member_level')->where('aid', aid)->where('id', $levelid)->find();
                if ($agleveldata['can_agent'] > 0 && $agleveldata['commission1own'] == 1) {
                    $pid = $mid;
                }
                if ($product['commissionset'] != -1) {
                    if ($pid) {
                        $parent1 = Db::name('member')->where('aid', aid)->where('id', $pid)->find();
                        if ($parent1) {
                            $agleveldata1 = Db::name('member_level')->where('aid', aid)->where('id', $parent1['levelid'])->find();
                            if ($agleveldata1['can_agent'] != 0) {
                                $ogdata['parent1'] = $parent1['id'];
                            }
                        }
                    }
                    if ($parent1['pid']) {
                        $parent2 = Db::name('member')->where('aid', aid)->where('id', $parent1['pid'])->find();
                        if ($parent2) {
                            $agleveldata2 = Db::name('member_level')->where('aid', aid)->where('id', $parent2['levelid'])->find();
                            if ($agleveldata2['can_agent'] > 1) {
                                $ogdata['parent2'] = $parent2['id'];
                            }
                        }
                    }
                    if ($parent2['pid']) {
                        $parent3 = Db::name('member')->where('aid', aid)->where('id', $parent2['pid'])->find();
                        if ($parent3) {
                            $agleveldata3 = Db::name('member_level')->where('aid', aid)->where('id', $parent3['levelid'])->find();
                            if ($agleveldata3['can_agent'] > 2) {
                                $ogdata['parent3'] = $parent3['id'];
                            }
                        }
                    }
                    if ($product['commissionset'] == 1) {//按商品设置的分销比例
                        $commissiondata = json_decode($product['commissiondata1'], true);
                        if ($commissiondata) {
                            if ($agleveldata1) $ogdata['parent1commission'] = $commissiondata[$agleveldata1['id']]['commission1'] * $commission_totalprice * 0.01;
                            if ($agleveldata2) $ogdata['parent2commission'] = $commissiondata[$agleveldata2['id']]['commission2'] * $commission_totalprice * 0.01;
                            if ($agleveldata3) $ogdata['parent3commission'] = $commissiondata[$agleveldata3['id']]['commission3'] * $commission_totalprice * 0.01;
                        }
                    } elseif ($product['commissionset'] == 2) {//按固定金额
                        $commissiondata = json_decode($product['commissiondata2'], true);
                        if ($commissiondata) {
                            if ($agleveldata1) $ogdata['parent1commission'] = $commissiondata[$agleveldata1['id']]['commission1'] * $num;
                            if ($agleveldata2) $ogdata['parent2commission'] = $commissiondata[$agleveldata2['id']]['commission2'] * $num;
                            if ($agleveldata3) $ogdata['parent3commission'] = $commissiondata[$agleveldata3['id']]['commission3'] * $num;
                        }
                    } elseif ($product['commissionset'] == 3) {//提成是积分
                        $commissiondata = json_decode($product['commissiondata3'], true);
                        if ($commissiondata) {
                            if ($agleveldata1) $ogdata['parent1score'] = $commissiondata[$agleveldata1['id']]['commission1'] * $num;
                            if ($agleveldata2) $ogdata['parent2score'] = $commissiondata[$agleveldata2['id']]['commission2'] * $num;
                            if ($agleveldata3) $ogdata['parent3score'] = $commissiondata[$agleveldata3['id']]['commission3'] * $num;
                        }
                    } else { //按会员等级设置的分销比例
                        if ($agleveldata1) {
                            if ($agleveldata1['commissiontype'] == 1) { //固定金额按单
                                if ($istc1 == 0) {
                                    $ogdata['parent1commission'] = $agleveldata1['commission1'];
                                    $istc1 = 1;
                                }
                            } else {
                                $ogdata['parent1commission'] = $agleveldata1['commission1'] * $commission_totalprice * 0.01;
                            }
                        }
                        if ($agleveldata2) {
                            if ($agleveldata2['commissiontype'] == 1) {
                                if ($istc2 == 0) {
                                    $ogdata['parent2commission'] = $agleveldata2['commission2'];
                                    $istc2 = 1;
                                }
                            } else {
                                $ogdata['parent2commission'] = $agleveldata2['commission2'] * $commission_totalprice * 0.01;
                            }
                        }
                        if ($agleveldata3) {
                            if ($agleveldata3['commissiontype'] == 1) {
                                if ($istc3 == 0) {
                                    $ogdata['parent3commission'] = $agleveldata3['commission3'];
                                    $istc3 = 1;
                                }
                            } else {
                                $ogdata['parent3commission'] = $agleveldata3['commission3'] * $commission_totalprice * 0.01;
                            }
                        }
                    }
                }


                $ogid = Db::name('restaurant_shop_order_goods')->insertGetId($ogdata);
                $ogids[] = $ogid;
                if ($ogdata['parent1'] && ($ogdata['parent1commission'] > 0 || $ogdata['parent1score'] > 0)) {
                    Db::name('member_commission_record')->insert(['aid' => aid, 'mid' => $ogdata['parent1'], 'frommid' => $mid, 'orderid' => $orderid, 'ogid' => $ogid, 'type' => 'restaurant_shop', 'commission' => $ogdata['parent1commission'], 'score' => $ogdata['parent1score'], 'remark' => '下级购买菜品奖励', 'createtime' => time()]);
                }
                if ($ogdata['parent2'] && ($ogdata['parent2commission'] || $ogdata['parent2score'])) {
                    Db::name('member_commission_record')->insert(['aid' => aid, 'mid' => $ogdata['parent2'], 'frommid' => $mid, 'orderid' => $orderid, 'ogid' => $ogid, 'type' => 'restaurant_shop', 'commission' => $ogdata['parent2commission'], 'score' => $ogdata['parent2score'], 'remark' => '下二级购买菜品奖励', 'createtime' => time()]);
                }
                if ($ogdata['parent3'] && ($ogdata['parent3commission'] || $ogdata['parent3score'])) {
                    Db::name('member_commission_record')->insert(['aid' => aid, 'mid' => $ogdata['parent3'], 'frommid' => $mid, 'orderid' => $orderid, 'ogid' => $ogid, 'type' => 'restaurant_shop', 'commission' => $ogdata['parent3commission'], 'score' => $ogdata['parent3score'], 'remark' => '下三级购买菜品奖励', 'createtime' => time()]);
                }

                //删除购物车
                Db::name('restaurant_shop_cart')->where('aid', aid)->where('mid', mid)->where('ggid', $guige['id'])->where('proid', $product['id'])->delete();
                Db::name('restaurant_product_guige')->where('aid', aid)->where('id', $guige['id'])->update(['stock' => Db::raw("stock-$num"), 'sales' => Db::raw("sales+$num"), 'sales_daily' => Db::raw("sales_daily+$num")]);
                Db::name('restaurant_product')->where('aid', aid)->where('id', $product['id'])->update(['stock' => Db::raw("stock-$num"), 'sales' => Db::raw("sales+$num"), 'sales_daily' => Db::raw("sales_daily+$num")]);
            }

            //根据餐后付款设置，开启时下单后打印小票，关闭时付款后打印小票
            $restaurant_shop_sysset = Db::name('restaurant_shop_sysset')->where('aid', aid)->where('bid', $data['bid'])->find();
            if ($restaurant_shop_sysset['pay_after'] == 1) {
                //仅打印加菜
                $orderGoods = Db::name('restaurant_shop_order_goods')->alias('og')->where('orderid', $orderid)->where('og.id', 'in', $ogids)->leftJoin('restaurant_product p', 'p.id=og.proid')
                    ->fieldRaw('og.*,p.area_id')->select()->toArray();
                \app\custom\Restaurant::print('restaurant_shop', $orderdata, $orderGoods);
            }

        }
        if (count($buydata) > 1) { //创建合并支付单
            $payorderid = \app\model\Payorder::createorder(aid, 0, $mid, 'restaurant_shop_hb', $orderid, $ordernum, $orderdata['title'], $alltotalprice, $alltotalscore);
        }

        return $this->json(['status' => 1, 'payorderid' => $payorderid, 'msg' => '提交成功', 'pay_after' => $shop_set['pay_after']]);
    }

    //编辑

    public function createOrder()
    {
        $post = input('post.');
        if ($post['frompage'] != 'admin') {
            //非管理员点餐验证登录
            //$this->checklogin();
        }
        if (empty($post['tableid'])) {
            return $this->json(['status' => 0, 'msg' => '请先扫描桌台二维码或选择桌台']);
        }

        //todo 一个桌子同时只能点一单
        $table = Db::name('restaurant_table')->where('aid', aid)->where('id', $post['tableid'])->find();
        if ($table) {
//            if($table['orderid']) {
//                //todo 修改菜品
//                return $this->json(['status'=>0,'msg'=>'当前桌台已存在订单，请联系服务员']);
//            }
            $order = Db::name('restaurant_shop_order')->where('aid', aid)->where('id', $table['orderid'])->find();
//            if($table['status'] != 0 && $order['status'] == 0 && $post['frompage'] != 'admin') {
//                return $this->json(['status'=>0,'msg'=>'当前桌台状态不可下单，请联系服务员']);
//            }
        }

        $sysset = Db::name('admin_set')->where('aid', aid)->find();
        $userlevel = Db::name('member_level')->where('aid', aid)->where('id', $this->member['levelid'])->find();

        $buydata = $post['buydata'];

        $shop_sysset = Db::name('restaurant_shop_sysset')->where('aid', aid)->where('bid', $buydata[0]['bid'])->find();

        $couponridArr = [];
        foreach ($buydata as $data) { //判断有没有重复选择的优惠券
            if ($data['couponrid'] && in_array($data['couponrid'], $couponridArr)) {
                return $this->json(['status' => 0, 'msg' => t('优惠券') . '不可重复使用']);
            } elseif ($data['couponrid']) {
                $couponridArr[] = $data['couponrid'];
            }
        }

        $ordernum = date('ymdHis') . rand(100000, 999999);
        $scoredkmaxmoney = 0;
        $scoremaxtype = 0;
        $i = 0;
        $alltotalprice = 0;
        $alltotalscore = 0;
        foreach ($buydata as $data) {
            $i++;
            $bid = $data['bid'];
            if ($data['prodata']) {
                $prodata = explode('-', $data['prodata']);
            } else {
                return $this->json(['status' => 0, 'msg' => '产品数据错误']);
            }
            if ($bid != 0) {
                $business = Db::name('business')->where('id', $bid)->field('id,aid,cid,name,logo,tel,address,sales,longitude,latitude,start_hours,end_hours')->find();
                if ($business['start_hours'] != $business['end_hours']) {
                    $start_time = strtotime(date('Y-m-d ' . $business['start_hours']));
                    $end_time = strtotime(date('Y-m-d ' . $business['end_hours']));
                    if (($start_time < $end_time && ($start_time > time() || $end_time < time())) || ($start_time >= $end_time && ($start_time > time() && $end_time < time()))) {
                        return $this->json(['status' => 0, 'msg' => '商家不在营业时间']);
                    }
                }
            }
            $shop_set = Db::name('restaurant_shop_sysset')->where('aid', aid)->where('bid', $bid)->find();
            if ($shop_set['status'] == 0) {
                return $this->json(['status' => 0, 'msg' => '该商家未开启点餐']);
            }
            if ($shop_set['start_hours'] != $shop_set['end_hours']) {
                $start_time = strtotime(date('Y-m-d ' . $shop_set['start_hours']));
                $end_time = strtotime(date('Y-m-d ' . $shop_set['end_hours']));
                if (($start_time < $end_time && ($start_time > time() || $end_time < time())) || ($start_time >= $end_time && ($start_time > time() && $end_time < time()))) {
                    return $this->json(['status' => 0, 'msg' => '该商家不在点餐时间']);
                }
            }

            $product_price = 0;
            $needzkproduct_price = 0;
            $givescore = 0; //奖励积分
            $totalweight = 0;//重量
            $totalnum = 0;
            $prolist = [];
            $proids = [];
            $cids = [];

            foreach ($prodata as $key => $pro) {
                $sdata = explode(',', $pro);
                // $sdata[2] = intval($sdata[2]);
                if ($sdata[2] <= 0) return $this->json(['status' => 0, 'msg' => '购买数量有误']);
                $product = Db::name('restaurant_product')->where('aid', aid)->where('ischecked', 1)->where('bid', $bid)->where('id', $sdata[0])->find();
                if (!$product) return $this->json(['status' => 0, 'msg' => '产品不存在或已下架']);

                if ($product['status'] == 0) {
                    return $this->json(['status' => 0, 'msg' => '商品未上架']);
                }
                if ($product['status'] == 2 && (strtotime($product['start_time']) > time() || strtotime($product['end_time']) < time())) {
                    return $this->json(['status' => 0, 'msg' => '商品未上架']);
                }
                if ($product['status'] == 3) {
                    $start_time = strtotime(date('Y-m-d ' . $product['start_hours']));
                    $end_time = strtotime(date('Y-m-d ' . $product['end_hours']));
                    if (($start_time < $end_time && ($start_time > time() || $end_time < time())) || ($start_time >= $end_time && ($start_time > time() && $end_time < time()))) {
                        return $this->json(['status' => 0, 'msg' => '商品未上架']);
                    }
                }

                if ($key == 0) $title = $product['name'];

                $guige = Db::name('restaurant_product_guige')->where('aid', aid)->where('id', $sdata[1])->find();
                if (!$guige) return $this->json(['status' => 0, 'msg' => '产品规格不存在或已下架']);
                if ($guige['stock'] < $sdata[2] || $guige['stock_daily'] - $guige['sales_daily'] < $sdata[2]) {
                    return $this->json(['status' => 0, 'msg' => '库存不足']);
                }
                //$gettj = explode(',',$product['gettj']);
                //if(!in_array('-1',$gettj) && !in_array($this->member['levelid'],$gettj) && (!in_array('0',$gettj) || $this->member['subscribe']!=1)){ //不是所有人
                //	if(!$product['gettjtip']) $product['gettjtip'] = '没有权限购买该商品';
                //	return $this->json(['status'=>0,'msg'=>$product['gettjtip'],'url'=>$product['gettjurl']]);
                //}

                if ($product['limit_per'] > 0 && $sdata[2] > $product['limit_per']) { //每单限购
                    return $this->json(['status' => 0, 'msg' => $product['name'] . '每单限购' . $product['limit_per'] . '份']);
                }
                if ($product['limit_start'] > 0 && $sdata[2] < $product['limit_start']) { //起售份数
                    return $this->json(['status' => 0, 'msg' => $product['name'] . '最低购买' . $product['limit_start'] . '份']);
                }

                if ($product['lvprice'] == 1) $guige = $this->formatguige($guige);
                $product_price += $guige['sell_price'] * $sdata[2];
                if ($product['lvprice'] == 0) { //未开启会员价
                    $needzkproduct_price += $guige['sell_price'] * $sdata[2];
                }
                $totalweight += $guige['weight'] * $sdata[2];
                $totalnum += $sdata[2];

                if ($product['scored_set'] == 0) {
                    if ($userinfo['scoredkmaxpercent'] > 0 && $userinfo['scoredkmaxpercent'] < 100) {
                        $scoredkmaxmoney += $userinfo['scoredkmaxpercent'] * 0.01 * $guige['sell_price'] * $sdata[2];
                    } else {
                        $scoredkmaxmoney += $guige['sell_price'] * $sdata[2];
                    }
                } elseif ($product['scored_set'] == 1) {
                    $scoremaxtype = 1;
                    $scoredkmaxmoney += $product['scored_val'] * 0.01 * $guige['sell_price'] * $sdata[2];
                } elseif ($product['scored_set'] == 2) {
                    $scoremaxtype = 1;
                    $scoredkmaxmoney += $product['scored_val'] * $sdata[2];
                } else {
                    $scoremaxtype = 1;
                    $scoredkmaxmoney += 0;
                }

                $prolist[] = ['product' => $product, 'guige' => $guige, 'num' => $sdata[2], 'skrs' => $skrs, 'isSeckill' => $isSeckill];

                $proids[] = $product['id'];
                $cids = array_merge($cids, explode(',', $product['cid']));
                $givescore += $guige['givescore'] * $sdata[2];
            }
            //会员折扣
            $leveldk_money = 0;
            if ($userlevel && $userlevel['discount'] > 0 && $userlevel['discount'] < 10) {
                $leveldk_money = $needzkproduct_price * (1 - $userlevel['discount'] * 0.1);
            }
            $totalprice = $product_price - $leveldk_money;

            //满减活动
            $mjset = Db::name('manjian_set')->where('aid', aid)->find();
            if ($mjset && $mjset['status'] == 1) {
                $mjdata = json_decode($mjset['mjdata'], true);
            } else {
                $mjdata = array();
            }
            $manjian_money = 0;
            $moneyduan = 0;
            if ($mjdata) {
                foreach ($mjdata as $give) {
                    if ($totalprice * 1 >= $give['money'] * 1 && $give['money'] * 1 > $moneyduan) {
                        $moneyduan = $give['money'] * 1;
                        $manjian_money = $give['jian'] * 1;
                    }
                }
            }
            if ($manjian_money <= 0) $manjian_money = 0;
            $totalprice = $totalprice - $manjian_money;
            if ($totalprice < 0) $totalprice = 0;

            //优惠券
            if ($data['couponrid'] > 0) {
                $couponrid = $data['couponrid'];
                $couponrecord = Db::name('coupon_record')->where("bid=-1 or bid=" . $data['bid'])->where('aid', aid)->where('mid', mid)->where('id', $couponrid)->find();
                if (!$couponrecord) {
                    return $this->json(['status' => 0, 'msg' => '该' . t('优惠券') . '不存在']);
                } elseif ($couponrecord['status'] != 0) {
                    return $this->json(['status' => 0, 'msg' => '该' . t('优惠券') . '已使用过了']);
                } elseif ($couponrecord['starttime'] > time()) {
                    return $this->json(['status' => 0, 'msg' => '该' . t('优惠券') . '尚未开始使用']);
                } elseif ($couponrecord['endtime'] < time()) {
                    return $this->json(['status' => 0, 'msg' => '该' . t('优惠券') . '已过期']);
                } elseif ($couponrecord['minprice'] > $totalprice) {
                    return $this->json(['status' => 0, 'msg' => '该' . t('优惠券') . '不符合条件']);
                } elseif ($couponrecord['type'] != 1 && $couponrecord['type'] != 4 && $couponrecord['type'] != 5) {
                    return $this->json(['status' => 0, 'msg' => '该' . t('优惠券') . '不符合条件']);
                }

                $couponinfo = Db::name('coupon')->where('aid', aid)->where('id', $couponrecord['couponid'])->find();
                if ($couponinfo['fwtype'] == 2) {//指定商品可用
                    $productids = explode(',', $couponinfo['productids']);
                    if (!array_intersect($proids, $productids)) {
                        return $this->json(['status' => 0, 'msg' => '该' . t('优惠券') . '指定商品可用']);
                    }
                }
                if ($couponinfo['fwtype'] == 1) {//指定类目可用
                    $categoryids = explode(',', $couponinfo['categoryids']);
                    $clist = Db::name('restaurant_product_category')->where('pid', 'in', $categoryids)->select()->toArray();
                    foreach ($clist as $kc => $vc) {
                        $categoryids[] = $vc['id'];
                        $cate2 = Db::name('restaurant_product_category')->where('pid', $vc['id'])->find();
                        $categoryids[] = $cate2['id'];
                    }
                    if (!array_intersect($cids, $categoryids)) {
                        return $this->json(['status' => 0, 'msg' => '该' . t('优惠券') . '指定分类可用']);
                    }
                }

                Db::name('coupon_record')->where('id', $couponrid)->update(['status' => 1, 'usetime' => time()]);
                if ($couponrecord['type'] == 4) {//运费抵扣券
                    $coupon_money = 0;
                } else {
                    $coupon_money = $couponrecord['money'];
                    if ($coupon_money > $totalprice) $coupon_money = $totalprice;
                }
            } else {
                $coupon_money = 0;
            }
            //促销活动
            if ($data['cuxiaoid'] > 0) {
                $cuxiaoid = $data['cuxiaoid'];
                $cuxiaoinfo = Db::name('restaurant_cuxiao')->where("bid=-1 or bid=" . $data['bid'])->where('aid', aid)->where('id', $cuxiaoid)->find();
                if (!$cuxiaoinfo) {
                    return $this->json(['status' => 0, 'msg' => '该促销活动不存在']);
                } elseif ($cuxiaoinfo['starttime'] > time()) {
                    return $this->json(['status' => 0, 'msg' => '该促销活动尚未开始']);
                } elseif ($cuxiaoinfo['endtime'] < time()) {
                    return $this->json(['status' => 0, 'msg' => '该促销活动已结束']);
                } elseif ($cuxiaoinfo['type'] != 5 && $cuxiaoinfo['type'] != 6 && $cuxiaoinfo['minprice'] > $totalprice) {
                    return $this->json(['status' => 0, 'msg' => '该促销活动不符合条件']);
                } elseif (($cuxiaoinfo['type'] == 5 || $cuxiaoinfo['type'] == 6) && $cuxiaoinfo['minnum'] > $totalnum) {
                    return $this->json(['status' => 0, 'msg' => '该促销活动不符合条件']);
                }
                if ($cuxiaoinfo['fwtype'] == 2) {//指定商品可用
                    $productids = explode(',', $cuxiaoinfo['productids']);
                    if (!array_intersect($proids, $productids)) {
                        return $this->json(['status' => 0, 'msg' => '该促销活动指定商品可用']);
                    }
                }
                if ($cuxiaoinfo['fwtype'] == 1) {//指定类目可用
                    $categoryids = explode(',', $cuxiaoinfo['categoryids']);
                    $clist = Db::name('restaurant_product_category')->where('pid', 'in', $categoryids)->select()->toArray();
                    foreach ($clist as $kc => $vc) {
                        $categoryids[] = $vc['id'];
                        $cate2 = Db::name('restaurant_product_category')->where('pid', $vc['id'])->find();
                        $categoryids[] = $cate2['id'];
                    }
                    if (!array_intersect($cids, $categoryids)) {
                        return $this->json(['status' => 0, 'msg' => '该促销活动指定分类可用']);
                    }
                }
                if ($cuxiaoinfo['type'] == 1 || $cuxiaoinfo['type'] == 6) {//满额立减 满件立减
                    $manjian_money = $manjian_money + $cuxiaoinfo['money'];
                    $cuxiaomoney = $cuxiaoinfo['money'] * -1;
                } elseif ($cuxiaoinfo['type'] == 2) {//满额赠送
                    $cuxiaomoney = 0;
                    $product = Db::name('restaurant_product')->where('aid', aid)->where('id', $cuxiaoinfo['proid'])->find();
                    $guige = Db::name('restaurant_product_guige')->where('aid', aid)->where('id', $cuxiaoinfo['ggid'])->find();
                    if (!$product) return $this->json(['status' => 0, 'msg' => '赠送产品不存在']);
                    if (!$guige) return $this->json(['status' => 0, 'msg' => '赠送产品规格不存在']);
                    if ($guige['stock'] < 1) {
                        return $this->json(['status' => 0, 'msg' => '赠送产品库存不足']);
                    }
                    $prolist[] = ['product' => $product, 'guige' => $guige, 'num' => 1, 'isSeckill' => 0];
                } elseif ($cuxiaoinfo['type'] == 3) {//加价换购
                    $cuxiaomoney = $cuxiaoinfo['money'];
                    $product = Db::name('restaurant_product')->where('aid', aid)->where('id', $cuxiaoinfo['proid'])->find();
                    $guige = Db::name('restaurant_product_guige')->where('aid', aid)->where('id', $cuxiaoinfo['ggid'])->find();
                    if (!$product) return $this->json(['status' => 0, 'msg' => '换购产品不存在']);
                    if (!$guige) return $this->json(['status' => 0, 'msg' => '换购产品规格不存在']);
                    if ($guige['stock'] < 1) {
                        return $this->json(['status' => 0, 'msg' => '换购产品库存不足']);
                    }
                    $prolist[] = ['product' => $product, 'guige' => $guige, 'num' => 1, 'isSeckill' => 0];
                } elseif ($cuxiaoinfo['type'] == 4 || $cuxiaoinfo['type'] == 5) {//满额打折 满件打折
                    $cuxiaomoney = $totalprice * (1 - $cuxiaoinfo['zhekou'] * 0.1);
                    $manjian_money = $manjian_money + $cuxiaomoney;
                    $cuxiaomoney = $cuxiaomoney * -1;
                } else {
                    $cuxiaomoney = 0;
                }
            } else {
                $cuxiaomoney = 0;
            }
            $totalprice = $totalprice - $coupon_money + $cuxiaomoney;
            $tea_fee = ($shop_set['tea_fee_status'] == 1 && $shop_set['tea_fee'] > 0 ? $shop_set['tea_fee'] * $data['renshu'] : 0);
            //$totalprice = $totalprice + $tea_fee;

            //积分抵扣
            $scoredkscore = 0;
            $scoredk_money = 0;
            if ($post['usescore'] == 1) {
                $adminset = Db::name('admin_set')->where('aid', aid)->find();
                $score2money = $adminset['score2money'];
                $scoredkmaxpercent = $adminset['scoredkmaxpercent'];
                $scorebdkyf = $adminset['scorebdkyf'];
                $scoredk_money = $this->member['score'] * $score2money;
                if ($scorebdkyf == 1) {//积分不抵扣运费
                    if ($scoredk_money > $totalprice - $freight_price) $scoredk_money = $totalprice - $freight_price;
                } else {
                    if ($scoredk_money > $totalprice) $scoredk_money = $totalprice;
                }
                if ($scoremaxtype == 0) {
                    if ($scoredkmaxpercent > 0 && $scoredkmaxpercent < 100 && $scoredk_money > 0 && $scoredk_money > $totalprice * $scoredkmaxpercent * 0.01) {
                        $scoredk_money = $totalprice * $scoredkmaxpercent * 0.01;
                    }
                } else {
                    if ($scoredk_money > $scoredkmaxmoney) $scoredk_money = $scoredkmaxmoney;
                }
                $totalprice = $totalprice - $scoredk_money;
                $totalprice = round($totalprice * 100) / 100;
                if ($scoredk_money > 0) {
                    $scoredkscore = intval($scoredk_money / $score2money);
                }
            }

            $orderdata = [];
//			dump($table);
//            if(!$table['orderid']) {
            $orderdata['aid'] = aid;
            $orderdata['mid'] = mid;
            $orderdata['bid'] = $data['bid'];
            $orderdata['tableid'] = input('param.tableid');
//            }
            if (count($buydata) > 1) {
                $orderdata['ordernum'] = $ordernum . '_' . $i;
            } else {
                $orderdata['ordernum'] = $ordernum;
            }
            $orderdata['title'] = $title . (count($prodata) > 1 ? '等' : '');

//			$orderdata['linkman'] = $address['name'];
//			$orderdata['tel'] = $address['tel'];
//			$orderdata['area'] = $address['area'];
//			$orderdata['address'] = $address['address'];
//			$orderdata['longitude'] = $address['longitude'];
//			$orderdata['latitude'] = $address['latitude'];
//			$orderdata['area2'] = $address['province'].','.$address['city'].','.$address['district'];
            $orderdata['totalprice'] = $totalprice;
            $orderdata['product_price'] = $product_price;
            $orderdata['renshu'] = $data['renshu'];
            $orderdata['tea_fee'] = $tea_fee;
            $orderdata['leveldk_money'] = $leveldk_money;  //会员折扣
            $orderdata['manjian_money'] = $manjian_money;    //满减活动
            $orderdata['scoredk_money'] = $scoredk_money;    //积分抵扣
            $orderdata['coupon_money'] = $coupon_money;        //优惠券抵扣
            $orderdata['scoredkscore'] = $scoredkscore;    //抵扣掉的积分
            $orderdata['coupon_rid'] = $couponrid;
            $orderdata['freight_price'] = $freight_price; //运费
            $orderdata['givescore'] = $givescore;
            $orderdata['message'] = $data['message'];

            $orderdata['createtime'] = time();
            $orderdata['platform'] = platform;
            $orderdata['hexiao_code'] = random(16);
            $orderdata['hexiao_qr'] = createqrcode(m_url('admin/hexiao/hexiao?type=restaurant_shop&co=' . $orderdata['hexiao_code']));
            $orderdata['field1'] = $data['field1'];
            $orderdata['field2'] = $data['field2'];
            $orderdata['field3'] = $data['field3'];
            $orderdata['field4'] = $data['field4'];
            $orderdata['field5'] = $data['field5'];
            
            $shop_sysset = Db::name('restaurant_shop_sysset')->where('aid',aid)->where('bid',$data['bid'])->find();
            
            $taxes_fee = 0;
            $money = 0;
            if($shop_sysset){
                $money = $shop_sysset['server_fee_status']?$shop_sysset['person_num']*$data['renshu']:(round($totalprice,2)*($shop_sysset['order_rado']*0.01));
                $taxes_fee = $shop_sysset['taxes_fee'];
            }
            $taxes = round($totalprice,2)*($taxes_fee*0.01);
            $taxes = round($taxes,2);
            $money = round($money,2);
            
            $orderdata['server_fee'] = $money;
            $orderdata['taxes'] = $taxes;
           // $orderdata['totalprice'] = $totalprice+$money+$taxes;
			
            if ($table['orderid'] && ($order['status'] == 0 || $post['frompage'] == 'admin')) {
                Db::name('restaurant_shop_order')->where('id', $table['orderid'])->update($orderdata);
                $orderid = $table['orderid'];
            } else {
                $orderid = Db::name('restaurant_shop_order')->insertGetId($orderdata);
            }
            $orderdata = Db::name('restaurant_shop_order')->where('id', $orderid)->find();

            $payorderid = \app\model\Payorder::createorder(aid, $orderdata['bid'], $orderdata['mid'], 'restaurant_shop', $orderid, $orderdata['ordernum'], $orderdata['title'], $orderdata['totalprice'], $orderdata['scoredkscore']);
            //更新餐桌状态
            Db::name('restaurant_table')->where('aid', aid)->where('id', $table['id'])->update(['status' => 2, 'orderid' => $orderid]);

            $alltotalprice += $orderdata['totalprice'];
            $alltotalscore += $orderdata['scoredkscore'];

            $istc1 = 0; //设置了按单固定提成时 只将佣金计算到第一个商品里
            $istc2 = 0;
            $istc3 = 0;
            foreach ($prolist as $key => $v) {
                $product = $v['product'];
                $guige = $v['guige'];
                $num = $v['num'];
                $ogdata = [];
                $ogdata['aid'] = aid;
                $ogdata['bid'] = $product['bid'];
                $ogdata['mid'] = mid;
                $ogdata['orderid'] = $orderid;
                $ogdata['ordernum'] = $orderdata['ordernum'];
                $ogdata['proid'] = $product['id'];
                $ogdata['name'] = $product['name'];
                $ogdata['pic'] = $product['pic'];
                $ogdata['procode'] = $product['procode'];
                $ogdata['ggid'] = $guige['id'];
                $ogdata['ggname'] = $guige['name'];
                //$ogdata['cid'] = $product['cid'];
                $ogdata['num'] = $num;
                $ogdata['cost_price'] = $guige['cost_price'];
                $ogdata['sell_price'] = $guige['sell_price'];
                $ogdata['totalprice'] = $num * $guige['sell_price'];
                $ogdata['status'] = 0;
                $ogdata['createtime'] = time();

                $og_totalprice = $ogdata['totalprice'];

                //计算商品实际金额  商品金额 - 会员折扣 - 积分抵扣 - 满减抵扣 - 优惠券抵扣
                if ($sysset['fxjiesuantype'] == 1 || $sysset['fxjiesuantype'] == 2) {
                    $allproduct_price = $product_price;
                    $og_leveldk_money = 0;
                    $og_coupon_money = 0;
                    $og_scoredk_money = 0;
                    $og_manjian_money = 0;
                    if ($allproduct_price > 0 && $og_totalprice > 0) {
                        if ($leveldk_money) {
                            $og_leveldk_money = $og_totalprice / $allproduct_price * $leveldk_money;
                        }
                        if ($coupon_money) {
                            $og_coupon_money = $og_totalprice / $allproduct_price * $coupon_money;
                        }
                        if ($scoredk_money) {
                            $og_scoredk_money = $og_totalprice / $allproduct_price * $scoredk_money;
                        }
                        if ($manjian_money) {
                            $og_manjian_money = $og_totalprice / $allproduct_price * $manjian_money;
                        }
                    }
                    $og_totalprice = round($og_totalprice - $og_leveldk_money - $og_coupon_money - $og_scoredk_money - $og_manjian_money, 2);
                    if ($og_totalprice < 0) $og_totalprice = 0;
                }
                $ogdata['real_totalprice'] = $og_totalprice; //实际商品销售金额

                //计算佣金的商品金额
                $commission_totalprice = $ogdata['totalprice'];
                if ($sysset['fxjiesuantype'] == 1) { //按成交价格
                    $commission_totalprice = $ogdata['real_totalprice'];
                    if ($commission_totalprice < 0) $commission_totalprice = 0;
                }
                if ($sysset['fxjiesuantype'] == 2) { //按利润提成
                    $commission_totalprice = $ogdata['real_totalprice'] - $guige['cost_price'] * $num;
                    if ($commission_totalprice < 0) $commission_totalprice = 0;
                }
                if ($commission_totalprice < 0) $commission_totalprice = 0;

                $agleveldata = Db::name('member_level')->where('aid', aid)->where('id', $this->member['levelid'])->find();
                if ($agleveldata['can_agent'] > 0 && $agleveldata['commission1own'] == 1) {
                    $this->member['pid'] = mid;
                }
                if ($product['commissionset'] != -1) {
                    if ($this->member['pid']) {
                        $parent1 = \app\custom\Restaurant::getParentWithLevel(aid, $this->member['pid']);
                        if ($parent1 && $parent1['levelData']['can_agent'] != 0) {
                            $ogdata['parent1'] = $parent1['id'];
                        }
                    }
                    if ($parent1['pid']) {
                        $parent2 = \app\custom\Restaurant::getParentWithLevel(aid, $parent1['pid']);
                        if ($parent2 && $parent2['levelData']['can_agent'] > 1) {
                            $ogdata['parent2'] = $parent2['id'];
                        }
                    }
                    if ($parent2['pid']) {
                        $parent3 = \app\custom\Restaurant::getParentWithLevel(aid, $parent2['pid']);
                        if ($parent3 && $parent3['levelData']['can_agent'] > 2) {
                            $ogdata['parent3'] = $parent3['id'];
                        }
                    }
                    if ($product['commissionset'] == 1) {//按商品设置的分销比例
                        $commissiondata = json_decode($product['commissiondata1'], true);
                        if ($commissiondata) {
                            if ($ogdata['parent1']) $ogdata['parent1commission'] = $commissiondata[$parent1['levelData']['id']]['commission1'] * $commission_totalprice * 0.01;
                            if ($ogdata['parent2']) $ogdata['parent2commission'] = $commissiondata[$parent2['levelData']['id']]['commission2'] * $commission_totalprice * 0.01;
                            if ($ogdata['parent3']) $ogdata['parent3commission'] = $commissiondata[$parent3['levelData']['id']]['commission3'] * $commission_totalprice * 0.01;
                        }
                    } elseif ($product['commissionset'] == 2) {//按固定金额
                        $commissiondata = json_decode($product['commissiondata2'], true);
                        if ($commissiondata) {
                            if ($ogdata['parent1']) $ogdata['parent1commission'] = $commissiondata[$parent1['levelData']['id']]['commission1'] * $num;
                            if ($ogdata['parent2']) $ogdata['parent2commission'] = $commissiondata[$parent2['levelData']['id']]['commission2'] * $num;
                            if ($ogdata['parent3']) $ogdata['parent3commission'] = $commissiondata[$parent3['levelData']['id']]['commission3'] * $num;
                        }
                    } elseif ($product['commissionset'] == 3) {//提成是积分
                        $commissiondata = json_decode($product['commissiondata3'], true);
                        if ($commissiondata) {
                            if ($ogdata['parent1']) $ogdata['parent1score'] = $commissiondata[$parent1['levelData']['id']]['commission1'] * $num;
                            if ($ogdata['parent2']) $ogdata['parent2score'] = $commissiondata[$parent2['levelData']['id']]['commission2'] * $num;
                            if ($ogdata['parent3']) $ogdata['parent3score'] = $commissiondata[$parent3['levelData']['id']]['commission3'] * $num;
                        }
                    } else { //按会员等级设置的分销比例
                        if ($ogdata['parent1']) {
                            if ($parent1['levelData']['commissiontype'] == 1) { //固定金额按单
                                if ($istc1 == 0) {
                                    $ogdata['parent1commission'] = $parent1['levelData']['commission1'];
                                    $istc1 = 1;
                                }
                            } else {
                                $ogdata['parent1commission'] = $parent1['levelData']['commission1'] * $commission_totalprice * 0.01;
                            }
                        }
                        if ($ogdata['parent2']) {
                            if ($parent2['levelData']['commissiontype'] == 1) {
                                if ($istc2 == 0) {
                                    $ogdata['parent2commission'] = $parent2['levelData']['commission2'];
                                    $istc2 = 1;
                                }
                            } else {
                                $ogdata['parent2commission'] = $parent2['levelData']['commission2'] * $commission_totalprice * 0.01;
                            }
                        }
                        if ($ogdata['parent3']) {
                            if ($parent3['levelData']['commissiontype'] == 1) {
                                if ($istc3 == 0) {
                                    $ogdata['parent3commission'] = $parent3['levelData']['commission3'];
                                    $istc3 = 1;
                                }
                            } else {
                                $ogdata['parent3commission'] = $parent3['levelData']['commission3'] * $commission_totalprice * 0.01;
                            }
                        }
                    }
                }

                $ogid = Db::name('restaurant_shop_order_goods')->insertGetId($ogdata);
                $ogids[] = $ogid;
                if ($ogdata['parent1'] && ($ogdata['parent1commission'] > 0 || $ogdata['parent1score'] > 0)) {
                    Db::name('member_commission_record')->insert(['aid' => aid, 'mid' => $ogdata['parent1'], 'frommid' => mid, 'orderid' => $orderid, 'ogid' => $ogid, 'type' => 'restaurant_shop', 'commission' => $ogdata['parent1commission'], 'score' => $ogdata['parent1score'], 'remark' => '下级购买菜品奖励', 'createtime' => time()]);
                }
                if ($ogdata['parent2'] && ($ogdata['parent2commission'] || $ogdata['parent2score'])) {
                    Db::name('member_commission_record')->insert(['aid' => aid, 'mid' => $ogdata['parent2'], 'frommid' => mid, 'orderid' => $orderid, 'ogid' => $ogid, 'type' => 'restaurant_shop', 'commission' => $ogdata['parent2commission'], 'score' => $ogdata['parent2score'], 'remark' => '下二级购买菜品奖励', 'createtime' => time()]);
                }
                if ($ogdata['parent3'] && ($ogdata['parent3commission'] || $ogdata['parent3score'])) {
                    Db::name('member_commission_record')->insert(['aid' => aid, 'mid' => $ogdata['parent3'], 'frommid' => mid, 'orderid' => $orderid, 'ogid' => $ogid, 'type' => 'restaurant_shop', 'commission' => $ogdata['parent3commission'], 'score' => $ogdata['parent3score'], 'remark' => '下三级购买菜品奖励', 'createtime' => time()]);
                }

                //删除购物车
                Db::name('restaurant_shop_cart')->where('aid', aid)->where('mid', mid)->where('ggid', $guige['id'])->where('proid', $product['id'])->delete();
                Db::name('restaurant_product_guige')->where('aid', aid)->where('id', $guige['id'])->update(['stock' => Db::raw("stock-$num"), 'sales' => Db::raw("sales+$num"), 'sales_daily' => Db::raw("sales_daily+$num")]);
                Db::name('restaurant_product')->where('aid', aid)->where('id', $product['id'])->update(['stock' => Db::raw("stock-$num"), 'sales' => Db::raw("sales+$num"), 'sales_daily' => Db::raw("sales_daily+$num")]);
            }

            //根据餐后付款设置，开启时下单后打印小票，关闭时付款后打印小票
            $restaurant_shop_sysset = Db::name('restaurant_shop_sysset')->where('aid', aid)->where('bid', $data['bid'])->find();
            if ($restaurant_shop_sysset['pay_after'] == 1) {
                \app\custom\Restaurant::print('restaurant_shop', $orderdata);
            }

            //公众号通知 订单提交成功
            $tmplcontent = [];
            $tmplcontent['first'] = '有新点餐订单提交成功';
            $tmplcontent['remark'] = '点击进入查看~';
            $tmplcontent['keyword1'] = Db::name('admin_set')->where('aid', aid)->value('name'); //店铺
            $tmplcontent['keyword2'] = date('Y-m-d H:i:s', $orderdata['createtime']);//下单时间
            $tmplcontent['keyword3'] = $orderdata['title'];//商品
            $tmplcontent['keyword4'] = $orderdata['totalprice'] . '元';//金额
            \app\common\Wechat::sendhttmpl(aid, $orderdata['bid'], 'tmpl_orderconfirm', $tmplcontent, m_url('admin/restaurant/shoporder'), $orderdata['mdid']);
        }
        if (count($buydata) > 1) { //创建合并支付单
            $payorderid = \app\model\Payorder::createorder(aid, 0, mid, 'restaurant_shop_hb', $orderid, $ordernum, $orderdata['title'], $alltotalprice, $alltotalscore);
        }

        return $this->json(['status' => 1, 'payorderid' => $payorderid, 'msg' => '提交成功', 'pay_after' => $shop_sysset['pay_after']]);
    }

    public function orderlist()
    {
        $st = input('param.st');
        if (!input('?param.st') || $st === '') {
            $st = 'all';
        }
        $tableId = input('param.tableId');
        // if (!$tableId) {
        //     return $this->json(['status' => 0, 'msg' => '桌号错误']);
        // }
        $where = [];
        $where[] = ['aid', '=', aid];
        /*$where[] = ['bid', '=', bid];
        $where[] = ['tableid', '=', $tableId];*/
        $where[] = ['mid', '=', mid];
        $where[] = ['delete', '=', 0];
        if ($st == 'all') {

        } elseif ($st == '0') {
            $where[] = ['status', '=', 0];
        } elseif ($st == '1') {
            $where[] = ['status', 'in', '1,12'];
        } elseif ($st == '2') {
            $where[] = ['status', '=', 2];
        } elseif ($st == '3') {
            $where[] = ['status', '=', 3];
        } elseif ($st == '10') {
            $where[] = ['refund_status', '>', 0];
        }
        $pernum = 10;
        $pagenum = input('post.pagenum');
        if (!$pagenum) $pagenum = 1;
        $datalist = Db::name('restaurant_shop_order')->where($where)->page($pagenum, $pernum)->order('id desc')->select()->toArray();
        if (!$datalist) $datalist = array();
        foreach ($datalist as $key => $v) {
            $datalist[$key]['prolist'] = Db::name('restaurant_shop_order_goods')->where('orderid', $v['id'])->select()->toArray();
            if (!$datalist[$key]['prolist']) $datalist[$key]['prolist'] = [];
            $datalist[$key]['procount'] = Db::name('restaurant_shop_order_goods')->where('orderid', $v['id'])->sum('num');
            if ($v['bid'] != 0) {
                $datalist[$key]['binfo'] = Db::name('business')->where('aid', aid)->where('id', $v['bid'])->field('id,name,logo')->find();
            } else {
                $datalist[$key]['binfo'] = Db::name('admin_set')->where('aid', aid)->field('id,name,logo')->find();
            }
            $commentdp = Db::name('business_comment')->where('orderid', $v['id'])->where('aid', aid)->where('mid', mid)->find();
            if ($commentdp) {
                $datalist[$key]['iscommentdp'] = 1;
            } else {
                $datalist[$key]['iscommentdp'] = 0;
            }
        }
        $rdata = [];
        $rdata['datalist'] = $datalist;
        $rdata['codtxt'] = Db::name('shop_sysset')->where('aid', aid)->value('codtxt');
        $rdata['st'] = $st;
        return $this->json($rdata);
    }

    public function orderdetail()
    {
        $detail = Db::name('restaurant_shop_order')->where('id', input('param.id/d'))->where('aid', aid)->find();
        if (empty($detail)) $this->json(['status' => 0, 'msg' => '订单不存在']);
        //可在6小时内查看非本人订单
        if (mid != $detail['mid'] && time() > ($detail['createtime'] + 3600 * 6)) {
            $this->json(['status' => 0, 'msg' => '订单不存在']);
        }

        $detail['createtime'] = $detail['createtime'] ? date('Y-m-d H:i:s', $detail['createtime']) : '';
        $detail['collect_time'] = $detail['collect_time'] ? date('Y-m-d H:i:s', $detail['collect_time']) : '';
        $detail['paytime'] = $detail['paytime'] ? date('Y-m-d H:i:s', $detail['paytime']) : '';
        $detail['refund_time'] = $detail['refund_time'] ? date('Y-m-d H:i:s', $detail['refund_time']) : '';
        $detail['send_time'] = $detail['send_time'] ? date('Y-m-d H:i:s', $detail['send_time']) : '';

        $storeinfo = [];
        if ($detail['freight_type'] == 1) {
            if ($detail['bid'] == 0) {
                $storeinfo = Db::name('mendian')->where('id', $detail['mdid'])->field('name,address,longitude,latitude')->find();
            } else {
                $storeinfo = Db::name('business')->where('id', $detail['bid'])->field('name,address,longitude,latitude')->find();
            }
        }

        if ($detail['bid'] > 0) {
            $detail['binfo'] = Db::name('business')->where('aid', aid)->where('id', $detail['bid'])->field('id,name,logo')->find();
        } else {
            $detail['binfo'] = Db::name('admin_set')->where('aid', aid)->field('id,name,logo')->find();
        }
        $iscommentdp = 0;
        $commentdp = Db::name('business_comment')->where('orderid', $detail['id'])->where('aid', aid)->where('mid', mid)->find();
        if ($commentdp) $iscommentdp = 1;

        $prolist = Db::name('restaurant_shop_order_goods')->where('orderid', $detail['id'])->select()->toArray();

        $shopset = Db::name('shop_sysset')->where('aid', aid)->field('comment,autoclose')->find();
        $shop_set = Db::name('restaurant_shop_sysset')->where('aid', aid)->where('bid', $detail['bid'])->find();
        $shopset = array_merge($shopset, $shop_set);

        if ($detail['status'] == 0 && $shopset['autoclose'] > 0) {
            $lefttime = strtotime($detail['createtime']) + $shopset['autoclose'] * 60 - time();
            if ($lefttime < 0) $lefttime = 0;
        } else {
            $lefttime = 0;
        }

        if ($detail['field1']) {
            $detail['field1data'] = explode('^_^', $detail['field1']);
        }
        if ($detail['field2']) {
            $detail['field2data'] = explode('^_^', $detail['field2']);
        }
        if ($detail['field3']) {
            $detail['field3data'] = explode('^_^', $detail['field3']);
        }
        if ($detail['field4']) {
            $detail['field4data'] = explode('^_^', $detail['field4']);
        }
        if ($detail['field5']) {
            $detail['field5data'] = explode('^_^', $detail['field5']);
        }

        $detail['tableName'] = Db::name('restaurant_table')->where('id', $detail['tableid'])->value('name');

        $rdata = [];
        $rdata['detail'] = $detail;
        $rdata['iscommentdp'] = $iscommentdp;
        $rdata['prolist'] = $prolist;
        $rdata['shopset'] = $shopset;
        $rdata['storeinfo'] = $storeinfo;
        $rdata['lefttime'] = $lefttime;
        return $this->json($rdata);
    }

    //取消订单
    function quxiao()
    {
//		$post = input('post.');
//		$orderid = intval($post['orderid']);
//		$order = Db::name('restaurant_shop_order')->where('id',$orderid)->where('aid',aid)->where('mid',mid)->find();
//		if(!$order || $order['status']!=1){
//			return $this->json(['status'=>0,'msg'=>'取消失败,订单状态错误']);
//		}
//		$refund_money = $order['totalprice'];
//		$reason = '用户取消订单';
//		if($refund_money > 0) {
//            $rs = \app\common\Order::refund($order,$refund_money,$reason);
//            if($rs['status']==0){
//                return json(['status'=>0,'msg'=>$rs['msg']]);
//            }
//        }
//
//		Db::name('restaurant_shop_order')->where('id',$orderid)->where('aid',aid)->where('bid',bid)->update(['status'=>4,'refund_status'=>2,'refund_money'=>$refund_money,'refund_reason'=>$reason]);
//		Db::name('restaurant_shop_order_goods')->where('orderid',$orderid)->where('aid',aid)->where('bid',bid)->update(['status'=>4]);
//
//		//积分抵扣的返还
//		if($order['scoredkscore'] > 0){
//			\app\common\Member::addscore(aid,$order['mid'],$order['scoredkscore'],'订单退款返还');
//		}
//		//优惠券抵扣的返还
//		if($order['coupon_rid'] > 0){
//			Db::name('coupon_record')->where('aid',aid)->where(['mid'=>$order['mid'],'id'=>$order['coupon_rid']])->update(['status'=>0,'usetime'=>'']);
//		}
//
//		//退款成功通知
//		$tmplcontent = [];
//		$tmplcontent['first'] = '您的订单已经完成退款，¥'.$refund_money.'已经退回您的付款账户，请留意查收。';
//		$tmplcontent['remark'] = $reason.'，请点击查看详情~';
//		$tmplcontent['orderProductPrice'] = $refund_money;
//		$tmplcontent['orderProductName'] = $order['title'];
//		$tmplcontent['orderName'] = $order['ordernum'];
//		\app\common\Wechat::sendtmpl(aid,$order['mid'],'tmpl_tuisuccess',$tmplcontent,m_url('restaurant/shop/orderlist'));
//		//订阅消息
//		$tmplcontent = [];
//		$tmplcontent['amount6'] = $refund_money;
//		$tmplcontent['thing3'] = $order['title'];
//		$tmplcontent['character_string2'] = $order['ordernum'];
//
//		$tmplcontentnew = [];
//		$tmplcontentnew['amount3'] = $refund_money;
//		$tmplcontentnew['thing6'] = $order['title'];
//		$tmplcontentnew['character_string4'] = $order['ordernum'];
//		\app\common\Wechat::sendwxtmpl(aid,$order['mid'],'tmpl_tuisuccess',$tmplcontentnew,'restaurant/shop/orderlist',$tmplcontent);
//
//		//短信通知
//		$member = Db::name('member')->where('id',$order['mid'])->find();
//		if($member['tel']){
//			$tel = $member['tel'];
//		}else{
//			$tel = $order['tel'];
//		}
//		$rs = \app\common\Sms::send(aid,$tel,'tmpl_tuisuccess',['ordernum'=>$order['ordernum'],'money'=>$refund_money]);
//
//		return json(['status'=>1,'msg'=>'已退款成功']);
    }

    function closeOrder()
    {
        $post = input('post.');
        $orderid = intval($post['orderid']);
        $order = Db::name('restaurant_shop_order')->where('id', $orderid)->where('aid', aid)->where('mid', mid)->find();
        if (!$order || $order['status'] != 0) {
            return $this->json(['status' => 0, 'msg' => '关闭失败,订单状态错误']);
        }
        //加库存
        $oglist = Db::name('restaurant_shop_order_goods')->where('aid', aid)->where('orderid', $orderid)->select()->toArray();
        foreach ($oglist as $og) {
            Db::name('restaurant_product_guige')->where('aid', aid)->where('id', $og['ggid'])->update(['stock' => Db::raw("stock+" . $og['num']), 'sales' => Db::raw("sales-" . $og['num'])]);
            Db::name('restaurant_product')->where('aid', aid)->where('id', $og['proid'])->update(['stock' => Db::raw("stock+" . $og['num']), 'sales' => Db::raw("sales-" . $og['num'])]);
            if ($og['seckill_starttime']) {
                Db::name('seckill_prodata')->where('aid', aid)->where('proid', $og['proid'])->where('ggid', $og['ggid'])->where('starttime', $og['seckill_starttime'])->dec('sales', $og['num'])->update();
            }
        }
        //优惠券抵扣的返还
        if ($order['coupon_rid'] > 0) {
            Db::name('coupon_record')->where('aid', aid)->where('mid', mid)->where('id', $order['coupon_rid'])->update(['status' => 0, 'usetime' => '']);
        }

        $rs = Db::name('restaurant_shop_order')->where('id', $orderid)->where('aid', aid)->where('mid', mid)->update(['status' => 4]);
        Db::name('restaurant_shop_order_goods')->where('orderid', $orderid)->where('aid', aid)->where('mid', mid)->update(['status' => 4]);
        return $this->json(['status' => 1, 'msg' => '操作成功']);
    }

    function delOrder()
    {
        $post = input('post.');
        $orderid = intval($post['orderid']);
        $order = Db::name('restaurant_shop_order')->where('id', $orderid)->where('aid', aid)->where('mid', mid)->find();
        if (!$order || ($order['status'] != 4 && $order['status'] != 3)) {
            return $this->json(['status' => 0, 'msg' => '删除失败,订单状态错误']);
        }
//		if($order['status']==3){
        $rs = Db::name('restaurant_shop_order')->where('id', $orderid)->where('aid', aid)->where('mid', mid)->update(['delete' => 1]);
//		}else{
//			$rs = Db::name('restaurant_shop_order')->where('id',$orderid)->where('aid',aid)->where('mid',mid)->delete();
//			$rs = Db::name('restaurant_shop_order_goods')->where('orderid',$orderid)->where('aid',aid)->where('mid',mid)->delete();
//		}
        return $this->json(['status' => 1, 'msg' => '删除成功']);
    }

    function orderCollect()
    { //确认收货
        $post = input('post.');
        $orderid = intval($post['orderid']);
        $order = Db::name('restaurant_shop_order')->where('aid', aid)->where('mid', mid)->where('id', $orderid)->find();
        if (!$order || ($order['status'] != 2) || $order['paytype'] == '货到付款') {
            return $this->json(['status' => 0, 'msg' => '订单状态不符合收货要求']);
        }
        $rs = \app\custom\Restaurant::shop_orderconfirm($orderid);
        if ($rs['status'] == 0) return $this->json($rs);

//		Db::name('restaurant_shop_order')->where('aid',aid)->where('mid',mid)->where('id',$orderid)->update(['status'=>3,'collect_time'=>time()]);
//		Db::name('restaurant_shop_order_goods')->where('aid',aid)->where('orderid',$orderid)->update(['status'=>3,'endtime'=>time()]);
        \app\common\Member::uplv(aid, mid);
        $return = ['status' => 1, 'msg' => '确认收货成功', 'url' => true];

        $tmplcontent = [];
        $tmplcontent['first'] = '有订单客户已确认收货';
        $tmplcontent['remark'] = '点击进入查看~';
        $tmplcontent['keyword1'] = $this->member['nickname'];
        $tmplcontent['keyword2'] = $order['ordernum'];
        $tmplcontent['keyword3'] = $order['totalprice'] . '元';
        $tmplcontent['keyword4'] = date('Y-m-d H:i', $order['paytime']);
        \app\common\Wechat::sendhttmpl(aid, $order['bid'], 'tmpl_ordershouhuo', $tmplcontent, m_url('admin/order/shoporder'), $order['mdid']);
        return $this->json($return);
    }

    public function refundinit()
    {
        //订阅消息
        $wx_tmplset = Db::name('wx_tmplset')->where('aid', aid)->find();
        $tmplids = [];
        if ($wx_tmplset['tmpl_tuisuccess_new']) {
            $tmplids[] = $wx_tmplset['tmpl_tuisuccess_new'];
        } elseif ($wx_tmplset['tmpl_tuisuccess']) {
            $tmplids[] = $wx_tmplset['tmpl_tuisuccess'];
        }
        if ($wx_tmplset['tmpl_tuierror_new']) {
            $tmplids[] = $wx_tmplset['tmpl_tuierror_new'];
        } elseif ($wx_tmplset['tmpl_tuierror']) {
            $tmplids[] = $wx_tmplset['tmpl_tuierror'];
        }
        $rdata = [];
        $rdata['tmplids'] = $tmplids;
        return $this->json($rdata);
    }

    function refund()
    {//申请退款
        if (request()->isPost()) {
            $post = input('post.');
            $orderid = intval($post['orderid']);
            $money = floatval($post['money']);
            $order = Db::name('restaurant_shop_order')->where('aid', aid)->where('mid', mid)->where('id', $orderid)->find();
            if (!$order || ($order['status'] != 1 && $order['status'] != 2) || $order['refund_status'] == 2) {
                return $this->json(['status' => 0, 'msg' => '订单状态不符合退款要求']);
            }
            if ($money <= 0 || $money > $order['totalprice']) {
                return $this->json(['status' => 0, 'msg' => '退款金额有误']);
            }
            Db::name('restaurant_shop_order')->where('aid', aid)->where('mid', mid)->where('id', $orderid)->update(['refund_time' => time(), 'refund_status' => 1, 'refund_reason' => $post['reason'], 'refund_money' => $money]);

            $tmplcontent = [];
            $tmplcontent['first'] = '有订单客户申请退款';
            $tmplcontent['remark'] = '点击进入查看~';
            $tmplcontent['keyword1'] = $order['ordernum'];
            $tmplcontent['keyword2'] = $money . '元';
            $tmplcontent['keyword3'] = $post['reason'];
            \app\common\Wechat::sendhttmpl(aid, $order['bid'], 'tmpl_ordertui', $tmplcontent, m_url('admin/order/shoporder'), $order['mdid']);
            return $this->json(['status' => 1, 'msg' => '提交成功,请等待商家审核']);
        }
        $orderid = input('param.orderid/d');
        $price = input('param.price/f');
        $order = Db::name('restaurant_shop_order')->where('aid', aid)->where('mid', mid)->where('id', $orderid)->find();
        $price = $order['totalprice'];
        $this->assign('orderid', $orderid);
        $this->assign('price', $price);
        return $this->fetch();
    }

    //评价商品
    public function comment()
    {
        $ogid = input('param.ogid/d');
        $og = Db::name('restaurant_shop_order_goods')->where('id', $ogid)->where('mid', mid)->find();
        if (!$og) {
            return $this->json(['status' => 0, 'msg' => '未查找到相关记录']);
        }
        $comment = Db::name('restaurant_shop_comment')->where('ogid', $ogid)->where('aid', aid)->where('mid', mid)->find();
        if (request()->isPost()) {
            $shopset = Db::name('shop_sysset')->where('aid', aid)->find();
            if ($shopset['comment'] == 0) return $this->json(['status' => 0, 'msg' => '评价功能未开启']);
            if ($comment) {
                return $this->json(['status' => 0, 'msg' => '您已经评价过了']);
            }
            $order_good = Db::name('restaurant_shop_order_goods')->where('aid', aid)->where('mid', mid)->where('id', $ogid)->find();
            $order = Db::name('restaurant_shop_order')->where('id', $order_good['orderid'])->find();
            $content = input('post.content');
            $content_pic = input('post.content_pic');
            $score = input('post.score/d');
            if ($score < 1) {
                return $this->json(['status' => 0, 'msg' => '请打分']);
            }
            $data['aid'] = aid;
            $data['mid'] = mid;
            $data['ogid'] = $order_good['id'];
            $data['proid'] = $order_good['proid'];
            $data['proname'] = $order_good['name'];
            $data['propic'] = $order_good['pic'];
            $data['orderid'] = $order['id'];
            $data['ordernum'] = $order['ordernum'];
            $data['score'] = $score;
            $data['content'] = $content;
            $data['openid'] = $this->member['openid'];
            $data['nickname'] = $this->member['nickname'];
            $data['headimg'] = $this->member['headimg'];
            $data['createtime'] = time();
            $data['content_pic'] = $content_pic;
            $data['ggid'] = $order_good['ggid'];
            $data['ggname'] = $order_good['ggname'];
            $data['status'] = ($shopset['comment_check'] == 1 ? 0 : 1);
            Db::name('restaurant_shop_comment')->insert($data);
            Db::name('restaurant_shop_order_goods')->where('aid', aid)->where('mid', mid)->where('id', $ogid)->update(['iscomment' => 1]);
            //Db::name('restaurant_shop_order')->where('id',$order['id'])->update(['iscomment'=>1]);

            //如果不需要审核 增加产品评论数及评分
            if ($shopset['comment_check'] == 0) {
                $countnum = Db::name('restaurant_shop_comment')->where('proid', $order_good['proid'])->where('status', 1)->count();
                $score = Db::name('restaurant_shop_comment')->where('proid', $order_good['proid'])->where('status', 1)->avg('score'); //平均评分
                $haonum = Db::name('restaurant_shop_comment')->where('proid', $order_good['proid'])->where('status', 1)->where('score', '>', 3)->count(); //好评数
                if ($countnum > 0) {
                    $haopercent = $haonum / $countnum * 100;
                } else {
                    $haopercent = 100;
                }
                Db::name('restaurant_product')->where('id', $order_good['proid'])->update(['comment_num' => $countnum, 'comment_score' => $score, 'comment_haopercent' => $haopercent]);
            }
            return $this->json(['status' => 1, 'msg' => '评价成功']);
        }
        $rdata = [];
        $rdata['og'] = $og;
        $rdata['comment'] = $comment;
        return $this->json($rdata);
    }

    //评价店铺
    public function commentdp()
    {
        $orderid = input('param.orderid/d');
        $order = Db::name('restaurant_shop_order')->where('id', $orderid)->where('mid', mid)->find();
        if (!$order) {
            return $this->json(['status' => 0, 'msg' => '未查找到相关记录']);
        }
        $comment = Db::name('business_comment')->where('orderid', $orderid)->where('aid', aid)->where('mid', mid)->find();
        if (request()->isPost()) {
            if ($comment) {
                return $this->json(['status' => 0, 'msg' => '您已经评价过了']);
            }
            $content = input('post.content');
            $content_pic = input('post.content_pic');
            $score = input('post.score/d');
            if ($score < 1) {
                return $this->json(['status' => 0, 'msg' => '请打分']);
            }
            $data['aid'] = aid;
            $data['mid'] = mid;
            $data['bid'] = $order['bid'];
            $data['bname'] = Db::name('business')->where('id', $order['bid'])->value('name');
            $data['orderid'] = $order['id'];
            $data['ordernum'] = $order['ordernum'];
            $data['score'] = $score;
            $data['content'] = $content;
            $data['content_pic'] = $content_pic;
            $data['openid'] = $this->member['openid'];
            $data['nickname'] = $this->member['nickname'];
            $data['headimg'] = $this->member['headimg'];
            $data['createtime'] = time();
            $data['status'] = 1;
            Db::name('business_comment')->insert($data);

            //如果不需要审核 增加店铺评论数及评分
            $countnum = Db::name('business_comment')->where('bid', $order['bid'])->where('status', 1)->count();
            $score = Db::name('business_comment')->where('bid', $order['bid'])->where('status', 1)->avg('score');
            Db::name('business')->where('id', $order['bid'])->update(['comment_num' => $countnum, 'comment_score' => $score]);
            return $this->json(['status' => 1, 'msg' => '评价成功']);
        }
        $rdata = [];
        $rdata['order'] = $order;
        $rdata['comment'] = $comment;
        return $this->json($rdata);
    }

    //评价配送员
    public function commentps()
    {
        $id = input('param.id/d');
        $psorder = Db::name('peisong_order')->where('id', $id)->where('mid', mid)->find();
        if (!$psorder) return $this->json(['status' => 0, 'msg' => '未找到相关记录']);
        $comment = Db::name('peisong_order_comment')->where('orderid', $id)->where('aid', aid)->where('mid', mid)->find();
        if (request()->isPost()) {
            if ($comment) {
                return $this->json(['status' => 0, 'msg' => '您已经评价过了']);
            }
            $content = input('post.content');
            $content_pic = input('post.content_pic');
            $score = input('post.score/d');
            if ($score < 1) {
                return $this->json(['status' => 0, 'msg' => '请打分']);
            }
            $data['aid'] = aid;
            $data['mid'] = mid;
            $data['bid'] = $psorder['bid'];
            $data['psid'] = $psorder['psid'];
            $data['orderid'] = $psorder['id'];
            $data['ordernum'] = $psorder['ordernum'];
            $data['score'] = $score;
            $data['content'] = $content;
            $data['content_pic'] = $content_pic;
            $data['nickname'] = $this->member['nickname'];
            $data['headimg'] = $this->member['headimg'];
            $data['createtime'] = time();
            $data['status'] = 1;
            Db::name('peisong_order_comment')->insert($data);

            //如果不需要审核 增加配送员评论数及评分
            $countnum = Db::name('peisong_order_comment')->where('psid', $psorder['psid'])->where('status', 1)->count();
            $score = Db::name('peisong_order_comment')->where('psid', $psorder['psid'])->where('status', 1)->avg('score'); //平均评分
            $haonum = Db::name('peisong_order_comment')->where('psid', $psorder['psid'])->where('status', 1)->where('score', '>', 3)->count(); //好评数
            if ($countnum > 0) {
                $haopercent = $haonum / $countnum * 100;
            } else {
                $haopercent = 100;
            }
            Db::name('peisong_user')->where('id', $psorder['psid'])->update(['comment_num' => $countnum, 'comment_score' => $score, 'comment_haopercent' => $haopercent]);

            return $this->json(['status' => 1, 'msg' => '评价成功']);
        }
        $rdata = [];
        $rdata['psorder'] = $psorder;
        $rdata['comment'] = $comment;
        return $this->json($rdata);
    }
}