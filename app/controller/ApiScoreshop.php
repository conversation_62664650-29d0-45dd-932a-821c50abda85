<?php

namespace app\controller;

use think\facade\Db;

class ApiScoreshop extends ApiCommon
{
    public function initialize()
    {
        parent::initialize();
        $set = Db::name('scoreshop_sysset')->where('aid', aid)->find();
        $gettj = explode(',', $set['gettj']);
        if (!in_array('-1', $gettj)) { //不是所有人
            $this->checklogin();
            if (!in_array($this->member['levelid'], $gettj)) {
                echojson(['status' => -4, 'msg' => $set['gettjtip'], 'url' => $set['gettjurl']]);
            }
        }
    }

    public function index()
    {
        $clist = Db::name('scoreshop_category')->where('aid', aid)->where('status', 1)->where('pid', 0)->order('sort desc,id')->select()->toArray();
        $rdata['clist'] = $clist;
        $rdata['score'] = $this->member ? $this->member['score'] : 0;
        return $this->json($rdata);
    }

    public function category()
    {
        $datalist = Db::name('scoreshop_category')->where('aid', aid)->where('status', 1)->order('sort desc,id')->select()->toArray();
        $rdata = [];
        $rdata['datalist'] = $datalist;
        return $this->json($rdata);
    }

    public function prolist()
    {
        //分类
        if (input('param.cid')) {
            $clist = Db::name('scoreshop_category')->where('aid', aid)->where('pid', input('param.cid/d'))->where('status', 1)->order('sort desc,id')->select()->toArray();
            if (!$clist) $clist = [];
        } else {
            $clist = Db::name('scoreshop_category')->where('aid', aid)->where('pid', 0)->where('status', 1)->order('sort desc,id')->select()->toArray();
            if (!$clist) $clist = [];
        }
        return $this->json(['clist' => $clist]);
    }

    public function getprolist()
    {
        $where = [];
        $where[] = ['aid', '=', aid];
        $where[] = ['status', '=', 1];
        //分类
        $searchcid = input('param.cid');
        if (input('param.cid')) {
            $cid = input('param.cid/d');
            //子分类
            $clist = Db::name('scoreshop_category')->where('aid', aid)->where('pid', $cid)->select()->toArray();
            if ($clist) {
                $cateArr = [$cid];
                foreach ($clist as $c) {
                    $cateArr[] = $c['id'];
                }
                $where[] = ['cid', 'in', $cateArr];
            } else {
                $where[] = ['cid', '=', $cid];
                $pid = Db::name('scoreshop_category')->where('aid', aid)->where('id', $cid)->value('pid');
                if ($pid) {
                    $searchcid = $pid;
                    $clist = Db::name('scoreshop_category')->where('aid', aid)->where('pid', $pid)->select()->toArray();
                }
            }
        }
        if (input('param.keyword')) {
            $where[] = ['name', 'like', '%' . input('param.keyword') . '%'];
        }

        if (input('param.field') && input('param.order')) {
            $order = input('param.field') . ' ' . input('param.order') . ',sort,id desc';
        } else {
            $order = 'sort desc,id desc';
        }

        $pernum = 20;
        $pagenum = input('post.pagenum');
        if (!$pagenum) $pagenum = 1;
        $datalist = Db::name('scoreshop_product')->field("id,pic,name,sales,score_price,money_price,sell_price,sellpoint,fuwupoint,sales,lvprice,lvprice_data")->where($where)->page($pagenum, $pernum)->order($order)->select()->toArray();
        if (!$datalist) $datalist = array();
        else {
            foreach ($datalist as &$product) {
                $product = $this->formatScoreProduct($product);
            }
        }
        return $this->json(['status' => 1, 'data' => $datalist]);
        $count = Db::name('scoreshop_product')->where($where)->count();

        $rdata = [];
        $rdata['clist'] = $clist;
        $rdata['searchcid'] = $searchcid;
        $rdata['pernum'] = $pernum;
        $rdata['count'] = $count;
        $rdata['datalist'] = $datalist;
        return $this->json($rdata);
    }

    public function product()
    {
        //if(!$this->member){
        //	return $this->json(['status'=>-1,'msg'=>'请先登录']);
        //}
        $proid = input('param.id/d');
        $where = [];
        $where[] = ['id', '=', $proid];
        $where[] = ['aid', '=', aid];
        $product = Db::name('scoreshop_product')->where($where)->find();
        if (!$product) $this->json(['status' => 0, 'msg' => '商品不存在']);
        if ($product['status'] == 0) $this->json(['status' => 0, 'msg' => '商品已下架']);
        if (!$product['pics']) $product['pics'] = $product['pic'];
        $product['pics'] = explode(',', $product['pics']);
        if ($product['fuwupoint']) {
            $product['fuwupoint'] = explode(' ', preg_replace("/\s+/", ' ', str_replace('　', ' ', trim($product['fuwupoint']))));
        }
        $product = $this->formatScoreProduct($product);

        //是否收藏
        $rs = Db::name('member_favorite')->where('aid', aid)->where('mid', mid)->where('proid', $proid)->where('type', 'scoreshop')->find();
        if ($rs) {
            $isfavorite = true;
        } else {
            $isfavorite = false;
        }
        if ($this->member) {
            //添加浏览历史
            $rs = Db::name('member_history')->where('aid', aid)->where('mid', mid)->where('proid', $proid)->where('type', 'scoreshop')->find();
            if ($rs) {
                Db::name('member_history')->where('id', $rs['id'])->update(['createtime' => time()]);
            } else {
                Db::name('member_history')->insert(['aid' => aid, 'mid' => mid, 'proid' => $proid, 'type' => 'scoreshop', 'createtime' => time()]);
            }
        }

        $sysset = Db::name('admin_set')->where('aid', aid)->field('name,logo,desc,fxjiesuantype,tel,kfurl,gzts,ddbb')->find();
        $shopset = Db::name('scoreshop_sysset')->where('aid', aid)->field('showjd,showcommission')->find();

        //预计佣金
        $commission = 0;
        $product['commission_desc'] = '元';
        if ($this->member && $shopset['showcommission'] == 1 && $product['commissionset'] != -1) {
            $userlevel = Db::name('member_level')->where('aid', aid)->where('id', $this->member['levelid'])->find();
            if ($userlevel['can_agent'] != 0) {
                if ($product['commissionset'] == 1) {//按比例
                    $commissiondata = json_decode($product['commissiondata1'], true);
                    if ($commissiondata) {
                        $commission = $commissiondata[$userlevel['id']]['commission1'] * ($product['money_price'] - ($sysset['fxjiesuantype'] == 2 ? $product['cost_price'] : 0)) * 0.01;
                    }
                } elseif ($product['commissionset'] == 2) {//按固定金额
                    $commissiondata = json_decode($product['commissiondata2'], true);
                    if ($commissiondata) {
                        $commission = $commissiondata[$userlevel['id']]['commission1'];
                    }
                } elseif ($product['commissionset'] == 3) {//提成是积分
                    $commissiondata = json_decode($product['commissiondata3'], true);
                    if ($commissiondata) {
                        $commission = $commissiondata[$userlevel['id']]['commission1'];
                    }
                    $product['commission_desc'] = t('积分');
                } elseif ($product['commissionset'] == 4 && $product['lvprice'] == 1) {//按价格差
                    $lvprice_data = json_decode($product['lvprice_data'], true);
                    $commission = array_shift($lvprice_data)['money_price'] - $product['money_price'];
                    if ($commission < 0) $commission = 0;
                } elseif ($product['commissionset'] == 5) {//比例+积分
                    $commissiondata = json_decode($product['commissiondata5'], true);
                    if ($commissiondata) {
                        $commission = $commissiondata[$userlevel['id']]['commission1']['money'] * ($product['money_price'] - ($sysset['fxjiesuantype'] == 2 ? $product['cost_price'] : 0)) * 0.01;
                    }
                    $commissiondata = json_decode($product['commissiondata5'], true);
                    if ($commissiondata) {
                        $commission_score = $commissiondata[$userlevel['id']]['commission1']['score'];
                    }
                    $product['commission_score_desc'] = t('积分');
                } elseif ($product['commissionset'] == 0) {//按会员等级
                    //fxjiesuantype 0按商品价格,1按成交价格,2按销售利润
                    if ($userlevel['commissiontype'] == 1) { //固定金额按单
                        $commission = $userlevel['commission1'];
                    } else {
                        $commission = $userlevel['commission1'] * ($product['money_price'] - ($sysset['fxjiesuantype'] == 2 ? $product['cost_price'] : 0)) * 0.01;
                    }
                }
            }
        }
        $product['commission'] = round($commission * 100) / 100;
        $product['commission_score'] = $commission_score ? $commission_score : 0;
        unset($product['cost_price']);

        $product['detail'] = \app\common\System::initpagecontent($product['detail'], aid, mid, platform);

        $rdata = [];
        $rdata['product'] = $product;
        $rdata['myscore'] = $this->member['score'];
        $rdata['sysset'] = $sysset;
        $rdata['shopset'] = $shopset;
        $rdata['isfavorite'] = $isfavorite;
        $rdata['cartnum'] = Db::name('scoreshop_cart')->where('aid', aid)->where('mid', mid)->sum('num');
        return $this->json($rdata);
    }

    //购物车
    public function cart()
    {
        //$this->checklogin();
        $gwcdata = [];
        $cartlist = Db::name('scoreshop_cart')->field('id,proid,num')->where('aid', aid)->where('mid', mid)->order('createtime desc')->select()->toArray();
        if (!$cartlist) $cartlist = [];
        foreach ($cartlist as $k => $gwc) {
            $product = Db::name('scoreshop_product')->where('aid', aid)->where('status', 1)->where('id', $gwc['proid'])->find();
            if (!$product) {
                Db::name('scoreshop_cart')->where('aid', aid)->where('proid', $gwc['proid'])->delete();
                continue;
            }
            $product = $this->formatScoreProduct($product);
            $cartlist[$k]['product'] = $product;
        }
        $rdata = [];
        $rdata['status'] = 1;
        $rdata['cartlist'] = $cartlist;
        return $this->json($rdata);
    }

    public function addcart()
    {
        $this->checklogin();
        $post = input('post.');
        $oldnum = 0;
        $num = intval($post['num']);
        $gwc = Db::name('scoreshop_cart')->where('aid', aid)->where('mid', mid)->where('proid', $post['proid'])->find();
        if ($gwc) $oldnum = $gwc['num'];

        $product = Db::name('scoreshop_product')->where('aid', aid)->where('status', 1)->where('id', $post['proid'])->find();
        if (!$product) return $this->json(['status' => 0, 'msg' => '产品不存在或已下架']);
        if ($product['freighttype'] == 3 || $product['freighttype'] == 4) return $this->json(['status' => 0, 'msg' => '虚拟商品不能加入购物车']);

        if ($oldnum + $num <= 0) {
            Db::name('scoreshop_cart')->where('aid', aid)->where('mid', mid)->where('proid', $post['proid'])->update(['num' => 1]);
            $cartnum = Db::name('scoreshop_cart')->where('aid', aid)->where('mid', mid)->sum('num');
            return $this->json(['status' => 1, 'msg' => '加入购物车成功', 'cartnum' => $cartnum]);
        }
        if ($gwc) {
            Db::name('scoreshop_cart')->where('aid', aid)->where('mid', mid)->where('proid', $post['proid'])->inc('num', $num)->update();
        } else {
            $data = [];
            $data['aid'] = aid;
            $data['mid'] = mid;
            $data['proid'] = $post['proid'];
            $data['num'] = $num;
            $data['createtime'] = time();
            Db::name('scoreshop_cart')->insert($data);
        }
        $cartnum = Db::name('scoreshop_cart')->where('aid', aid)->where('mid', mid)->sum('num');
        return $this->json(['status' => 1, 'msg' => '加入购物车成功', 'cartnum' => $cartnum]);
    }

    public function cartChangenum()
    {
        $this->checklogin();
        $id = input('post.id/d');
        $num = input('post.num/d');
        if ($num < 1) $num = 1;
        Db::name('scoreshop_cart')->where('id', $id)->where('mid', mid)->update(['num' => $num]);
        return $this->json(['status' => 1, 'msg' => '修改成功']);
    }

    public function cartdelete()
    {
        $this->checklogin();
        $id = input('post.id/d');
        Db::name('scoreshop_cart')->where('id', $id)->where('mid', mid)->delete();
        return $this->json(['status' => 1, 'msg' => '删除成功']);
    }

    public function buy()
    {
        $this->checklogin();

        $prodata = explode('-', input('param.prodata'));

        $freightList = \app\model\Freight::getList([['status', '=', 1], ['aid', '=', aid]]);

        $fids = [];
        foreach ($freightList as $v) {
            $fids[] = $v['id'];
        }
        $totalmoney = 0;
        $totalscore = 0;
        $totalweight = 0;
        $totalnum = 0;
        $prolist = [];
        $autofahuo = 0;
        foreach ($prodata as $key => $gwc) {
            list($proid, $num) = explode(',', $gwc);
            $proid = intval($proid);
            $num = intval($num);
            if ($num < 1) $num = 1;
            $product = Db::name('scoreshop_product')->where('aid', aid)->where('status', 1)->where('id', $proid)->find();
            if (!$product) {
                return $this->json(['status' => 0, 'msg' => '产品不存在或已下架']);
            }
            if ($product['stock'] < $num) {
                return $this->json(['status' => 0, 'msg' => '库存不足']);
            }
            //是否达到限制兑换数
            if ($product['buymax'] > 0) {
                $buynum = $num + Db::name('scoreshop_order_goods')->where('aid', aid)->where('mid', mid)->where('proid', $proid)->where('status', 'in', '0,1,2,3')->sum('num');
                if ($buynum > $product['buymax']) {
                    return $this->json(['status' => 0, 'msg' => '每人限兑' . $product['buymax'] . '次']);
                }
            }
            $product = $this->formatScoreProduct($product);
            $totalmoney += $product['money_price'] * $num;
            $totalscore += $product['score_price'] * $num;
            $totalweight += $product['weight'] * $num;
            $totalnum += $num;

            if ($product['freighttype'] == 0) {
                $fids = array_intersect($fids, explode(',', $product['freightdata']));
            } elseif ($product['freighttype'] == 3 || $product['freighttype'] == 4) {
                $autofahuo = $product['freighttype'];
            } else {
                $thisfreightList = \app\model\Freight::getList([['status', '=', 1], ['aid', '=', aid], ['bid', '=', 0]]);
                $thisfids = [];
                foreach ($thisfreightList as $v) {
                    $thisfids[] = $v['id'];
                }
                $fids = array_intersect($fids, $thisfids);
            }
            $product['num'] = $num;
            $prolist[] = $product;
        }
        if ($autofahuo) {
            if (count($prodata) > 1) $this->json(['status' => 0, 'msg' => '虚拟商品请分别下单']);
            $freightList = [['id' => 0, 'name' => ($product['freighttype'] == 3 ? '自动发货' : '在线卡密'), 'pstype' => $product['freighttype']]];
        } else {
            if (!$fids) {
                if (count($prodata) > 1) {
                    return $this->json(['status' => 0, 'msg' => '所选择商品配送方式不同，请分别下单']);
                } else {
                    return $this->json(['status' => 0, 'msg' => '获取配送方式失败']);
                }
            }
            $freightList = \app\model\Freight::getList([['status', '=', 1], ['aid', '=', aid], ['id', 'in', $fids]]);
        }
        $havetongcheng = 0;
        foreach ($freightList as $k => $v) {
            if ($v['pstype'] == 2) { //同城配送
                $havetongcheng = 1;
            }
        }
        if ($havetongcheng) {
            $address = Db::name('member_address')->where('aid', aid)->where('mid', mid)->where('latitude', '>', 0)->order('isdefault desc,id desc')->find();
        } else {
            $address = Db::name('member_address')->where('aid', aid)->where('mid', mid)->order('isdefault desc,id desc')->find();
        }
        if (!$address) $address = array();

        $needLocation = 0;
        $rs = \app\model\Freight::formatFreightList($freightList, $address, $totalmoney, $totalnum, $totalweight);
        $freightList = $rs['freightList'];
        $freightArr = $rs['freightArr'];
        if ($rs['needLocation'] == 1) $needLocation = 1;

        $rdata = [];
        $rdata['linkman'] = $address ? $address['name'] : strval($this->member['realname']);
        $rdata['tel'] = $address ? $address['tel'] : strval($this->member['tel']);
        if (!$rdata['linkman']) {
            $lastorder = Db::name('scoreshop_order')->where('aid', aid)->where('mid', mid)->where('linkman', '<>', '')->find();
            if ($lastorder) {
                $rdata['linkman'] = $lastorder['linkman'];
                $rdata['tel'] = $lastorder['tel'];
            }
        }
        $rdata['totalmoney'] = $totalmoney;
        $rdata['totalscore'] = $totalscore;
        $rdata['totalnum'] = $totalnum;
        $rdata['totalweight'] = $totalweight;
        $rdata['havetongcheng'] = $havetongcheng;
        $rdata['address'] = $address;
        $rdata['prolist'] = $prolist;
        $rdata['freightList'] = $freightList;
        $rdata['freightArr'] = $freightArr;
        $rdata['needLocation'] = $needLocation;
        return $this->json($rdata);
    }

    public function createOrder()
    {
        $this->checklogin();
        $post = input('post.');

        $prodata = explode('-', input('param.prodata'));
        $totalmoney = 0;
        $totalscore = 0;
        $totalweight = 0;
        $totalnum = 0;
        $prolist = [];
        $autofahuo = 0;
        $sysset = Db::name('admin_set')->where('aid', aid)->find();
        foreach ($prodata as $key => $gwc) {
            list($proid, $num) = explode(',', $gwc);
            $proid = intval($proid);
            $num = intval($num);
            if ($num < 1) $num = 1;
            $product = Db::name('scoreshop_product')->where('aid', aid)->where('status', 1)->where('id', $proid)->find();
            if (!$product) {
                return $this->json(['status' => 0, 'msg' => '产品不存在或已下架']);
            }
            if ($product['stock'] < $num) {
                return $this->json(['status' => 0, 'msg' => '库存不足']);
            }
            //是否达到限制兑换数
            if ($product['buymax'] > 0) {
                $buynum = $num + Db::name('scoreshop_order_goods')->where('aid', aid)->where('mid', mid)->where('proid', $proid)->where('status', 'in', '0,1,2,3')->sum('num');
                if ($buynum > $product['buymax']) {
                    return $this->json(['status' => 0, 'msg' => '每人限兑' . $product['buymax'] . '次']);
                }
            }
            $product = $this->formatScoreProduct($product);
            $totalmoney += $product['money_price'] * $num;
            $totalscore += $product['score_price'] * $num;
            $totalweight += $product['weight'] * $num;
            $totalnum += $num;
            $product['num'] = $num;
            $prolist[] = $product;
            if ($product['freighttype'] == 3 || $product['freighttype'] == 4) {
                $autofahuo = $product['freighttype'];
            }
        }
        if ($autofahuo && count($prodata) > 1) $this->json(['status' => 0, 'msg' => '虚拟商品请分别下单']);

        //收货地址
        if ($post['addressid'] == '' || $post['addressid'] == 0) {
            $address = ['id' => 0, 'name' => $post['linkman'], 'tel' => $post['tel'], 'area' => '', 'address' => ''];
        } else {
            $address = Db::name('member_address')->where('id', $post['addressid'])->where('aid', aid)->where('mid', mid)->find();
        }
        //运费
        $freight_price = 0;
        if ($post['freightid']) {
            $freight = Db::name('freight')->where('aid', aid)->where('id', $post['freightid'])->find();
            if (($address['name'] == '' || $address['tel'] == '') && ($freight['pstype'] == 1 || $freight['pstype'] == 3) && $freight['needlinkinfo'] == 1) {
                return $this->json(['status' => 0, 'msg' => '请填写联系人和联系电话']);
            }

            $rs = \app\model\Freight::getFreightPrice($freight, $address, $totalmoney, $totalnum, $totalweight);
            if ($rs['status'] == 0) return $this->json($rs);
            $freight_price = $rs['freight_price'];

            //判断配送时间选择是否符合要求
            if ($freight['pstimeset'] == 1) {
                //$freighttime = strtotime(explode('~',$post['freight_time'])[0]);
                $freight_times = explode('~', $post['freight_time']);
                if ($freight_times[1]) {
                    $freighttime = strtotime(explode(' ', $freight_times[0])[0] . ' ' . $freight_times[1]);
                } else {
                    $freighttime = strtotime($freight_times[0]);
                }
                if (time() + $freight['psprehour'] * 3600 > $freighttime) {
                    return $this->json(['status' => 0, 'msg' => (($freight['pstype'] == 0 || $freight['pstype'] == 2 || $freight['pstype'] == 10) ? '配送' : '提货') . '时间必须在' . $freight['psprehour'] . '小时之后']);
                }
            }
        } elseif ($product['freighttype'] == 3) {
            $freight = ['id' => 0, 'name' => '自动发货', 'pstype' => 3];
        } elseif ($product['freighttype'] == 4) {
            $freight = ['id' => 0, 'name' => '在线卡密', 'pstype' => 4];
        } else {
            $freight = ['id' => 0, 'name' => '包邮', 'pstype' => 0];
        }
        //$totalmoney = $totalmoney + $freight_price;

        $orderdata = [];
        $orderdata['aid'] = aid;
        $orderdata['mid'] = mid;

        $ordernum = date('ymdHis') . aid . rand(1000, 9999);
        $orderdata['ordernum'] = $ordernum;
        $orderdata['title'] = $prolist[0]['name'] . (count($prolist) > 1 ? '等' : '');
        $orderdata['linkman'] = $address['name'];
        $orderdata['tel'] = $address['tel'];
        $orderdata['area'] = $address['area'];
        $orderdata['area2'] = $address['province'] . ',' . $address['city'] . ',' . $address['district'];
        $orderdata['address'] = $address['address'];
        $orderdata['longitude'] = $address['longitude'];
        $orderdata['latitude'] = $address['latitude'];
        $orderdata['totalscore'] = intval($totalscore);
        $orderdata['totalmoney'] = $totalmoney;
        $orderdata['totalnum'] = $totalnum;
        $orderdata['freight_price'] = $freight_price; //运费
        $orderdata['totalprice'] = $totalmoney + $freight_price * 1;
        if ($freight && ($freight['pstype'] == 0 || $freight['pstype'] == 10)) {
            $orderdata['freight_text'] = $freight['name'] . '(' . $freight_price . '元)';
            $orderdata['freight_type'] = $freight['pstype'];
        } elseif ($freight && $freight['pstype'] == 1) {
            $storename = Db::name('mendian')->where('aid', aid)->where('id', $post['storeid'])->value('name');
            $orderdata['freight_text'] = $freight['name'] . '[' . $storename . ']';
            $orderdata['freight_type'] = 1;
            $orderdata['mdid'] = $post['storeid'];
        } elseif ($freight && $freight['pstype'] == 2) {
            $orderdata['freight_text'] = $freight['name'] . '(' . $freight_price . '元)';
            $orderdata['freight_type'] = 2;
        } elseif ($freight && ($freight['pstype'] == 3 || $freight['pstype'] == 4)) { //自动发货 在线卡密
            $orderdata['freight_text'] = $freight['name'];
            $orderdata['freight_type'] = $freight['pstype'];
        } else {
            $orderdata['freight_text'] = '包邮';
        }
        $orderdata['freight_id'] = $freight['id'];
        $orderdata['freight_time'] = $post['freight_time']; //配送时间
        $orderdata['createtime'] = time();
        $orderdata['hexiao_code'] = random(16);
        $orderdata['hexiao_qr'] = createqrcode(m_url('admin/hexiao/hexiao?type=scoreshop&co=' . $orderdata['hexiao_code']));
        $orderdata['platform'] = platform;

        $orderid = Db::name('scoreshop_order')->insertGetId($orderdata);
        \app\model\Freight::saveformdata($orderid, 'scoreshop_order', $freight['id'], $post['formdata']);
        $payorderid = \app\model\Payorder::createorder(aid, $orderdata['bid'], $orderdata['mid'], 'scoreshop', $orderid, $ordernum, $orderdata['title'], $orderdata['totalprice'], $orderdata['totalscore']);

        $istc1 = 0; //设置了按单固定提成时 只将佣金计算到第一个商品里
        $istc2 = 0;
        $istc3 = 0;
        foreach ($prolist as $product) {
            $ogdata = [];
            $ogdata['aid'] = aid;
            $ogdata['mid'] = mid;
            $ogdata['orderid'] = $orderid;
            $ogdata['ordernum'] = $orderdata['ordernum'];
            $ogdata['proid'] = $product['id'];
            $ogdata['name'] = $product['name'];
            $ogdata['pic'] = $product['pic'];
            $ogdata['procode'] = $product['procode'];
            $ogdata['num'] = $product['num'];
            $ogdata['sell_price'] = $product['sell_price'];
            $ogdata['cost_price'] = $product['cost_price'];
            $ogdata['money_price'] = $product['money_price'];
            $ogdata['score_price'] = $product['score_price'];
            $ogdata['totalscore'] = $product['score_price'] * $product['num'];
            $ogdata['totalmoney'] = $product['money_price'] * $product['num'];
            $ogdata['status'] = 0;
            $ogdata['createtime'] = time();

            //分销
            $og_totalprice = $ogdata['totalmoney'];

            //计算商品实际金额  商品金额 - 会员折扣 - 积分抵扣 - 满减抵扣 - 优惠券抵扣
            //0按商品价格，1按成交价，2按销售利润
            $leveldk_money = 0;
            $coupon_money = 0;
            $scoredk_money = 0;
            $manjian_money = 0;
            if ($sysset['fxjiesuantype'] == 1 || $sysset['fxjiesuantype'] == 2) {
                $allproduct_price = $og_totalprice;
                $og_leveldk_money = 0;
                $og_coupon_money = 0;
                $og_scoredk_money = 0;
                $og_manjian_money = 0;
                if ($allproduct_price > 0 && $og_totalprice > 0) {
                    if ($leveldk_money) {
                        $og_leveldk_money = $og_totalprice / $allproduct_price * $leveldk_money;
                    }
                    if ($coupon_money) {
                        $og_coupon_money = $og_totalprice / $allproduct_price * $coupon_money;
                    }
                    if ($scoredk_money) {
                        $og_scoredk_money = $og_totalprice / $allproduct_price * $scoredk_money;
                    }
                    if ($manjian_money) {
                        $og_manjian_money = $og_totalprice / $allproduct_price * $manjian_money;
                    }
                }
                $og_totalprice = $og_totalprice - $og_leveldk_money - $og_scoredk_money - $og_manjian_money;
//                if($couponrecord['type']!=4) {//运费抵扣券
                $og_totalprice -= $og_coupon_money;
//                }
                $og_totalprice = round($og_totalprice, 2);
                if ($og_totalprice < 0) $og_totalprice = 0;
            }

            //计算佣金的商品金额
            $commission_totalprice = $ogdata['totalmoney'];
            if ($sysset['fxjiesuantype'] == 1) { //按成交价格
                $commission_totalprice = $og_totalprice;
                if ($commission_totalprice < 0) $commission_totalprice = 0;
            }
            if ($sysset['fxjiesuantype'] == 2) { //按销售利润
                $commission_totalprice = $og_totalprice - $product['cost_price'] * $product['num'];
                if ($commission_totalprice < 0) $commission_totalprice = 0;
            }

            $agleveldata = Db::name('member_level')->where('aid', aid)->where('id', $this->member['levelid'])->find();
            if ($agleveldata['can_agent'] > 0 && $agleveldata['commission1own'] == 1) {
                $this->member['pid'] = mid;
            }

            if ($product['commissionset'] != -1) {
                if ($this->member['pid']) {
                    $parent1 = Db::name('member')->where('aid', aid)->where('id', $this->member['pid'])->find();
                    if ($parent1) {
                        $agleveldata1 = Db::name('member_level')->where('aid', aid)->where('id', $parent1['levelid'])->find();
                        if ($agleveldata1['can_agent'] != 0) {
                            $ogdata['parent1'] = $parent1['id'];
                        }
                    }
                }
                if ($parent1['pid']) {
                    $parent2 = Db::name('member')->where('aid', aid)->where('id', $parent1['pid'])->find();
                    if ($parent2) {
                        $agleveldata2 = Db::name('member_level')->where('aid', aid)->where('id', $parent2['levelid'])->find();
                        if ($agleveldata2['can_agent'] > 1) {
                            $ogdata['parent2'] = $parent2['id'];
                        }
                    }
                }
                if ($parent2['pid']) {
                    $parent3 = Db::name('member')->where('aid', aid)->where('id', $parent2['pid'])->find();
                    if ($parent3) {
                        $agleveldata3 = Db::name('member_level')->where('aid', aid)->where('id', $parent3['levelid'])->find();
                        if ($agleveldata3['can_agent'] > 2) {
                            $ogdata['parent3'] = $parent3['id'];
                        }
                    }
                }
                if ($product['commissionset'] == 1) {//按商品设置的分销比例
                    $commissiondata = json_decode($product['commissiondata1'], true);
                    if ($commissiondata) {
                        if ($agleveldata1) $ogdata['parent1commission'] = $commissiondata[$agleveldata1['id']]['commission1'] * $commission_totalprice * 0.01;
                        if ($agleveldata2) $ogdata['parent2commission'] = $commissiondata[$agleveldata2['id']]['commission2'] * $commission_totalprice * 0.01;
                        if ($agleveldata3) $ogdata['parent3commission'] = $commissiondata[$agleveldata3['id']]['commission3'] * $commission_totalprice * 0.01;
                    }
                } elseif ($product['commissionset'] == 2) {//按固定金额
                    $commissiondata = json_decode($product['commissiondata2'], true);
                    if ($commissiondata) {
                        if ($agleveldata1) $ogdata['parent1commission'] = $commissiondata[$agleveldata1['id']]['commission1'] * $ogdata['num'];
                        if ($agleveldata2) $ogdata['parent2commission'] = $commissiondata[$agleveldata2['id']]['commission2'] * $ogdata['num'];
                        if ($agleveldata3) $ogdata['parent3commission'] = $commissiondata[$agleveldata3['id']]['commission3'] * $ogdata['num'];
                    }
                } elseif ($product['commissionset'] == 3) {//提成是积分
                    $commissiondata = json_decode($product['commissiondata3'], true);
                    if ($commissiondata) {
                        if ($agleveldata1) $ogdata['parent1score'] = $commissiondata[$agleveldata1['id']]['commission1'] * $ogdata['num'];
                        if ($agleveldata2) $ogdata['parent2score'] = $commissiondata[$agleveldata2['id']]['commission2'] * $ogdata['num'];
                        if ($agleveldata3) $ogdata['parent3score'] = $commissiondata[$agleveldata3['id']]['commission3'] * $ogdata['num'];
                    }
                } elseif ($product['commissionset'] == 5) {//比例+积分
                    $commissiondata = json_decode($product['commissiondata5'], true);
                    if ($commissiondata) {
                        if ($agleveldata1) {
                            $ogdata['parent1commission'] = $commissiondata[$agleveldata1['id']]['commission1']['money'] * $commission_totalprice * 0.01;
                            $ogdata['parent1score'] = $commissiondata[$agleveldata1['id']]['commission1']['score'] * $ogdata['num'];
                        }
                        if ($agleveldata2) {
                            $ogdata['parent2commission'] = $commissiondata[$agleveldata2['id']]['commission2']['money'] * $commission_totalprice * 0.01;
                            $ogdata['parent2score'] = $commissiondata[$agleveldata2['id']]['commission2']['score'] * $ogdata['num'];
                        }
                        if ($agleveldata3) {
                            $ogdata['parent3commission'] = $commissiondata[$agleveldata3['id']]['commission3']['money'] * $commission_totalprice * 0.01;
                            $ogdata['parent3score'] = $commissiondata[$agleveldata3['id']]['commission3']['score'] * $ogdata['num'];
                        }
                    }
                } else { //按会员等级设置的分销比例
                    if ($agleveldata1) {
                        if ($agleveldata1['commissiontype'] == 1) { //固定金额按单
                            if ($istc1 == 0) {
                                $ogdata['parent1commission'] = $agleveldata1['commission1'];
                                $istc1 = 1;
                            }
                        } else {
                            $ogdata['parent1commission'] = $agleveldata1['commission1'] * $commission_totalprice * 0.01;
                        }
                    }
                    if ($agleveldata2) {
                        if ($agleveldata2['commissiontype'] == 1) {
                            if ($istc2 == 0) {
                                $ogdata['parent2commission'] = $agleveldata2['commission2'];
                                $istc2 = 1;
                            }
                        } else {
                            $ogdata['parent2commission'] = $agleveldata2['commission2'] * $commission_totalprice * 0.01;
                        }
                    }
                    if ($agleveldata3) {
                        if ($agleveldata3['commissiontype'] == 1) {
                            if ($istc3 == 0) {
                                $ogdata['parent3commission'] = $agleveldata3['commission3'];
                                $istc3 = 1;
                            }
                        } else {
                            $ogdata['parent3commission'] = $agleveldata3['commission3'] * $commission_totalprice * 0.01;
                        }
                    }
                }
            }

            //计算门店佣金
            if (getcustom('plug_zhiming')) {
                $mendian_money = 0;
                if ($ogdata['totalmoney'] > 0) {
                    if ($product['commission_money_type'] == 0) {
                        if ($product['commission_money_percent'] > 0)
                            $mendian_money = $ogdata['totalmoney'] * $product['commission_money_percent'] * 0.01;
                    } elseif ($product['commission_money_type'] == 1) {
                        $mendian_money = $product['commission_money'] * $product['num'];
                    }
                }
                $ogdata['mendian_commission'] = $mendian_money ? round($mendian_money, 2) : 0;

                $mendian_score = 0;
                if ($ogdata['totalscore'] > 0) {
                    if ($product['commission_score_type'] == 0) {
                        if ($product['commission_score_percent'] > 0)
                            $mendian_score = floor($ogdata['totalscore'] * $product['commission_score_percent'] * 0.01);
                    } elseif ($product['commission_score_type'] == 1) {
                        $mendian_score = $product['commission_score'] * $product['num'];
                    }
                }
                $ogdata['mendian_score'] = $mendian_score ? floor($mendian_score) : 0;
                $ogdata['mendian_iscommission'] = 0;
            }
            $ogid = Db::name('scoreshop_order_goods')->insertGetId($ogdata);
            if ($ogdata['parent1'] && ($ogdata['parent1commission'] > 0 || $ogdata['parent1score'] > 0)) {
                Db::name('member_commission_record')->insert(['aid' => aid, 'mid' => $ogdata['parent1'], 'frommid' => mid, 'orderid' => $orderid, 'ogid' => $ogid, 'type' => 'scoreshop', 'commission' => $ogdata['parent1commission'], 'score' => $ogdata['parent1score'], 'remark' => '下级购买积分商品奖励', 'createtime' => time()]);
            }
            if ($ogdata['parent2'] && ($ogdata['parent2commission'] || $ogdata['parent2score'])) {
                Db::name('member_commission_record')->insert(['aid' => aid, 'mid' => $ogdata['parent2'], 'frommid' => mid, 'orderid' => $orderid, 'ogid' => $ogid, 'type' => 'scoreshop', 'commission' => $ogdata['parent2commission'], 'score' => $ogdata['parent2score'], 'remark' => '下二级购买积分商品奖励', 'createtime' => time()]);
            }
            if ($ogdata['parent3'] && ($ogdata['parent3commission'] || $ogdata['parent3score'])) {
                Db::name('member_commission_record')->insert(['aid' => aid, 'mid' => $ogdata['parent3'], 'frommid' => mid, 'orderid' => $orderid, 'ogid' => $ogid, 'type' => 'scoreshop', 'commission' => $ogdata['parent3commission'], 'score' => $ogdata['parent3score'], 'remark' => '下三级购买积分商品奖励', 'createtime' => time()]);
            }
            //减库存加销量
            Db::name('scoreshop_product')->where('aid', aid)->where('id', $product['id'])->update(['stock' => $product['stock'] - $ogdata['num'], 'sales' => $product['sales'] + $ogdata['num']]);
        }
        //公众号通知 订单提交成功
        $tmplcontent = [];
        $tmplcontent['first'] = '有新' . t('积分') . '兑换订单提交成功';
        $tmplcontent['remark'] = '点击进入查看~';
        $tmplcontent['keyword1'] = Db::name('admin_set')->where('aid', aid)->value('name'); //店铺
        $tmplcontent['keyword2'] = date('Y-m-d H:i:s', $orderdata['createtime']);//下单时间
        $tmplcontent['keyword3'] = $orderdata['title'];//商品
        $tmplcontent['keyword4'] = $orderdata['totalscore'] . t('积分') . ($orderdata['totalprice'] > 0 ? ' + ' . $orderdata['totalprice'] . '元' : '');//金额
        \app\common\Wechat::sendhttmpl(aid, 0, 'tmpl_orderconfirm', $tmplcontent, m_url('admin/order/scoreshoporder'), $orderdata['mdid']);
        return $this->json(['status' => 1, 'orderid' => $orderid, 'payorderid' => $payorderid, 'msg' => '提交成功']);
    }

    public function orderlist()
    {
        $st = input('param.st');
        if (!input('?param.st') || $st === '') {
            $st = 'all';
        }
        $where = [];
        $where[] = ['aid', '=', aid];
        $where[] = ['mid', '=', mid];
        $where[] = ['delete', '=', 0];
        if ($st == 'all') {

        } elseif ($st == '0') {
            $where[] = ['status', '=', 0];
        } elseif ($st == '1') {
            $where[] = ['status', '=', 1];
        } elseif ($st == '2') {
            $where[] = ['status', '=', 2];
        } elseif ($st == '3') {
            $where[] = ['status', '=', 3];
        } elseif ($st == '10') {
            $where[] = ['refund_status', '>', 0];
        }
        $pernum = 10;
        $pagenum = input('post.pagenum');
        if (!$pagenum) $pagenum = 1;
        $datalist = Db::name('scoreshop_order')->where($where)->page($pagenum, $pernum)->order('id desc')->select()->toArray();
        if (!$datalist) $datalist = array();
        foreach ($datalist as $key => $v) {
            $datalist[$key]['prolist'] = Db::name('scoreshop_order_goods')->where('orderid', $v['id'])->select()->toArray();
            if (!$datalist[$key]['prolist']) $datalist[$key]['prolist'] = [];
            $datalist[$key]['procount'] = Db::name('scoreshop_order_goods')->where('orderid', $v['id'])->sum('num');
        }
        $rdata = [];
        $rdata['datalist'] = $datalist;
        $rdata['st'] = $st;
        return $this->json($rdata);
    }

    public function orderdetail()
    {
        $detail = Db::name('scoreshop_order')->where('id', input('param.id/d'))->where('aid', aid)->where('mid', mid)->find();
        if (!$detail) $this->json(['status' => 0, 'msg' => '订单不存在']);
        $detail['createtime'] = $detail['createtime'] ? date('Y-m-d H:i:s', $detail['createtime']) : '';
        $detail['collect_time'] = $detail['collect_time'] ? date('Y-m-d H:i:s', $detail['collect_time']) : '';
        $detail['paytime'] = $detail['paytime'] ? date('Y-m-d H:i:s', $detail['paytime']) : '';
        $detail['refund_time'] = $detail['refund_time'] ? date('Y-m-d H:i:s', $detail['refund_time']) : '';
        $detail['send_time'] = $detail['send_time'] ? date('Y-m-d H:i:s', $detail['send_time']) : '';
        $detail['formdata'] = \app\model\Freight::getformdata($detail['id'], 'scoreshop_order');

        $storeinfo = [];
        if ($detail['freight_type'] == 1) {
            $storeinfo = Db::name('mendian')->where('id', $detail['mdid'])->field('id,name,address,longitude,latitude')->find();
        }
        $prolist = Db::name('scoreshop_order_goods')->where('orderid', $detail['id'])->select()->toArray();
        $scoreshopset = Db::name('scoreshop_sysset')->where('aid', aid)->field('comment,autoclose')->find();
        if ($detail['status'] == 0 && $scoreshopset['autoclose'] > 0) {
            $lefttime = strtotime($detail['createtime']) + $scoreshopset['autoclose'] * 60 - time();
            if ($lefttime < 0) $lefttime = 0;
        } else {
            $lefttime = 0;
        }

        $rdata = [];
        $rdata['detail'] = $detail;
        $rdata['prolist'] = $prolist;
        $rdata['scoreshopset'] = $scoreshopset;
        $rdata['storeinfo'] = $storeinfo;
        $rdata['lefttime'] = $lefttime;
        return $this->json($rdata);
    }

    function logistics()
    {
        $get = input('param.');
        $list = \app\common\Common::getwuliu($get['express_no'], $get['logistics']);

        $rdata = [];
        $rdata['express_no'] = $get['express_no'];
        $rdata['logistics'] = $get['logistics'];
        $rdata['datalist'] = $list;
        return $this->json($rdata);
    }

    function closeOrder()
    {
        $post = input('post.');
        $orderid = intval($post['orderid']);
        $order = Db::name('scoreshop_order')->where('id', $orderid)->where('aid', aid)->where('mid', mid)->find();
        if (!$order || $order['status'] != 0) {
            return $this->json(['status' => 0, 'msg' => '关闭失败,订单状态错误']);
        }
        //加库存
        $oglist = Db::name('scoreshop_order_goods')->where('aid', aid)->where('orderid', $orderid)->select()->toArray();
        foreach ($oglist as $og) {
            Db::name('scoreshop_product')->where('aid', aid)->where('id', $og['proid'])->update(['stock' => Db::raw("stock+" . $og['num']), 'sales' => Db::raw("sales-" . $og['num'])]);
        }

        $rs = Db::name('scoreshop_order')->where('id', $orderid)->where('aid', aid)->where('mid', mid)->update(['status' => 4]);
        Db::name('scoreshop_order_goods')->where('orderid', $orderid)->where('aid', aid)->where('mid', mid)->update(['status' => 4]);
        return $this->json(['status' => 1, 'msg' => '操作成功']);
    }

    function delOrder()
    {
        $post = input('post.');
        $orderid = intval($post['orderid']);
        $order = Db::name('scoreshop_order')->where('id', $orderid)->where('aid', aid)->where('mid', mid)->find();
        if (!$order || ($order['status'] != 4 && $order['status'] != 3)) {
            return $this->json(['status' => 0, 'msg' => '删除失败,订单状态错误']);
        }
        if ($order['status'] == 3) {
            $rs = Db::name('scoreshop_order')->where('id', $orderid)->where('aid', aid)->where('mid', mid)->update(['delete' => 1]);
        } else {
            $rs = Db::name('scoreshop_order')->where('id', $orderid)->where('aid', aid)->where('mid', mid)->delete();
            $rs = Db::name('scoreshop_order_goods')->where('orderid', $orderid)->where('aid', aid)->where('mid', mid)->delete();
        }
        return $this->json(['status' => 1, 'msg' => '删除成功']);
    }

    function orderCollect()
    { //确认收货
        $post = input('post.');
        $orderid = intval($post['orderid']);
        $order = Db::name('scoreshop_order')->where('aid', aid)->where('mid', mid)->where('id', $orderid)->find();
        if (!$order || ($order['status'] != 2) || $order['paytype'] == '货到付款') {
            return $this->json(['status' => 0, 'msg' => '订单状态不符合收货要求']);
        }
        $rs = \app\common\Order::collect($order, 'scoreshop');
        if ($rs['status'] == 0) return $this->json($rs);

        Db::name('scoreshop_order')->where('aid', aid)->where('mid', mid)->where('id', $orderid)->update(['status' => 3, 'collect_time' => time()]);
        Db::name('scoreshop_order_goods')->where('aid', aid)->where('orderid', $orderid)->update(['status' => 3, 'endtime' => time()]);
        \app\common\Member::uplv(aid, mid);
        $return = ['status' => 1, 'msg' => '确认收货成功', 'url' => true];

        $tmplcontent = [];
        $tmplcontent['first'] = '有订单客户已确认收货';
        $tmplcontent['remark'] = '点击进入查看~';
        $tmplcontent['keyword1'] = $this->member['nickname'];
        $tmplcontent['keyword2'] = $order['ordernum'];
        $tmplcontent['keyword3'] = $order['totalprice'] . '元';
        $tmplcontent['keyword4'] = date('Y-m-d H:i', $order['paytime']);
        \app\common\Wechat::sendhttmpl(aid, 0, 'tmpl_ordershouhuo', $tmplcontent, m_url('admin/order/scoreshoporder'), $order['mdid']);
        return $this->json($return);
    }

    public function refundinit()
    {
        //订阅消息
        $wx_tmplset = Db::name('wx_tmplset')->where('aid', aid)->find();
        $tmplids = [];
        if ($wx_tmplset['tmpl_tuisuccess_new']) {
            $tmplids[] = $wx_tmplset['tmpl_tuisuccess_new'];
        } elseif ($wx_tmplset['tmpl_tuisuccess']) {
            $tmplids[] = $wx_tmplset['tmpl_tuisuccess'];
        }
        if ($wx_tmplset['tmpl_tuierror_new']) {
            $tmplids[] = $wx_tmplset['tmpl_tuierror_new'];
        } elseif ($wx_tmplset['tmpl_tuierror']) {
            $tmplids[] = $wx_tmplset['tmpl_tuierror'];
        }
        $rdata = [];
        $rdata['tmplids'] = $tmplids;
        return $this->json($rdata);
    }

    function refund()
    {//申请退款
        if (request()->isPost()) {
            $post = input('post.');
            $orderid = intval($post['orderid']);
            $money = floatval($post['money']);
            $score = intval($post['score']);
            $order = Db::name('scoreshop_order')->where('aid', aid)->where('mid', mid)->where('id', $orderid)->find();
            if (!$order || ($order['status'] != 1 && $order['status'] != 2) || $order['refund_status'] == 2) {
                return $this->json(['status' => 0, 'msg' => '订单状态不符合退款要求']);
            }
            //金额可为0
            if ($money < 0 || $money > $order['totalprice']) {
                return $this->json(['status' => 0, 'msg' => '退款金额有误']);
            }
            //积分可为0
            if ($score < 0 || $score > $order['totalscore']) {
                return $this->json(['status' => 0, 'msg' => '退回' . t('积分') . '有误']);
            }
            Db::name('scoreshop_order')->where('aid', aid)->where('mid', mid)->where('id', $orderid)->update(['refund_time' => time(), 'refund_status' => 1, 'refund_reason' => $post['reason'], 'refund_money' => $money]);

            $tmplcontent = [];
            $tmplcontent['first'] = '有订单客户申请退款';
            $tmplcontent['remark'] = '点击进入查看~';
            $tmplcontent['keyword1'] = $order['ordernum'];
            $tmplcontent['keyword2'] = $money . '元';
            $tmplcontent['keyword3'] = $post['reason'];
            \app\common\Wechat::sendhttmpl(aid, 'tmpl_ordertui', $tmplcontent, m_url('admin/order/scoreshoporder'), $order['mdid']);
            return $this->json(['status' => 1, 'msg' => '提交成功,请等待商家审核']);
        }
        $orderid = input('param.orderid/d');
        $price = input('param.price/f');
        $order = Db::name('scoreshop_order')->where('aid', aid)->where('mid', mid)->where('id', $orderid)->find();
        $price = $order['totalprice'];
        $this->assign('orderid', $orderid);
        $this->assign('price', $price);
        return $this->fetch();
    }

    //评价商品
    public function comment()
    {
        $ogid = input('param.ogid/d');
        $og = Db::name('scoreshop_order_goods')->where('id', $ogid)->where('mid', mid)->find();
        if (!$og) {
            return $this->json(['status' => 0, 'msg' => '未查找到相关记录']);
        }
        $comment = Db::name('shop_comment')->where('ogid', $ogid)->where('aid', aid)->where('mid', mid)->find();
        if (request()->isPost()) {
            $scoreshopset = Db::name('scoreshop_sysset')->where('aid', aid)->find();
            if ($scoreshopset['comment'] == 0) return $this->json(['status' => 0, 'msg' => '评价功能未开启']);
            if ($comment) {
                return $this->json(['status' => 0, 'msg' => '您已经评价过了']);
            }
            $order_good = Db::name('scoreshop_order_goods')->where('aid', aid)->where('mid', mid)->where('id', $ogid)->find();
            $order = Db::name('scoreshop_order')->where('id', $order_good['orderid'])->find();
            $content = input('post.content');
            $content_pic = input('post.content_pic');
            $score = input('post.score/d');
            if ($score < 1) {
                return $this->json(['status' => 0, 'msg' => '请打分']);
            }
            $data['aid'] = aid;
            $data['mid'] = mid;
            $data['ogid'] = $order_good['id'];
            $data['proid'] = $order_good['proid'];
            $data['proname'] = $order_good['name'];
            $data['propic'] = $order_good['pic'];
            $data['orderid'] = $order['id'];
            $data['ordernum'] = $order['ordernum'];
            $data['score'] = $score;
            $data['content'] = $content;
            $data['openid'] = $this->member['openid'];
            $data['nickname'] = $this->member['nickname'];
            $data['headimg'] = $this->member['headimg'];
            $data['createtime'] = time();
            $data['content_pic'] = $content_pic;
            $data['ggid'] = $order_good['ggid'];
            $data['ggname'] = $order_good['ggname'];
            $data['status'] = ($scoreshopset['comment_check'] == 1 ? 0 : 1);
            Db::name('scoreshop_comment')->insert($data);
            Db::name('scoreshop_order_goods')->where('aid', aid)->where('mid', mid)->where('id', $ogid)->update(['iscomment' => 1]);
            //Db::name('scoreshop_order')->where('id',$order['id'])->update(['iscomment'=>1]);

            //如果不需要审核 增加产品评论数及评分
            if ($scoreshopset['comment_check'] == 0) {
                $countnum = Db::name('scoreshop_comment')->where('proid', $order_good['proid'])->where('status', 1)->count();
                $score = Db::name('scoreshop_comment')->where('proid', $order_good['proid'])->where('status', 1)->avg('score');
                Db::name('scoreshop_product')->where('id', $order_good['proid'])->update(['comment_num' => $countnum, 'comment_score' => $score]);
            }
            return $this->json(['status' => 1, 'msg' => '评价成功']);
        }
        $rdata = [];
        $rdata['og'] = $og;
        $rdata['comment'] = $comment;
        return $this->json($rdata);
    }

    //商品海报
    function getposter()
    {
        $this->checklogin();
        $post = input('post.');
        $platform = platform;
        $page = '/activity/scoreshop/product';
        $scene = 'id_' . $post['proid'] . '-pid_' . $this->member['id'];
        //if($platform == 'mp' || $platform == 'h5' || $platform == 'app'){
        //	$page = PRE_URL .'/h5/'.aid.'.html#'. $page;
        //}
        $posterset = Db::name('admin_set_poster')->where('aid', aid)->where('type', 'scoreshop')->where('platform', $platform)->order('id')->find();

        $posterdata = Db::name('member_poster')->where('aid', aid)->where('mid', mid)->where('scene', $scene)->where('type', 'scoreshop')->where('posterid', $posterset['id'])->find();
        if (true || !$posterdata) {
            $product = Db::name('scoreshop_product')->where('id', $post['proid'])->find();
            $sysset = Db::name('admin_set')->where('aid', aid)->find();
            $textReplaceArr = [
                '[头像]' => $this->member['headimg'],
                '[昵称]' => $this->member['nickname'],
                '[姓名]' => $this->member['realname'],
                '[手机号]' => $this->member['mobile'],
                '[商城名称]' => $sysset['name'],
                '[商品名称]' => $product['name'],
                '[商品销售价]' => $product['score_price'] . '积分' . ($product['money_price'] > 0 ? '+' . $product['money_price'] . '元' : ''),
                '[商品市场价]' => $product['sell_price'],
                '[商品图片]' => $product['pic'],
            ];

            $poster = $this->_getposter(aid, $platform, $posterset['content'], $page, $scene, $textReplaceArr);
            $posterdata = [];
            $posterdata['aid'] = aid;
            $posterdata['mid'] = $this->member['id'];
            $posterdata['scene'] = $scene;
            $posterdata['page'] = $page;
            $posterdata['type'] = 'scoreshop';
            $posterdata['poster'] = $poster;
            $posterdata['createtime'] = time();
            Db::name('member_poster')->insert($posterdata);
        }
        return $this->json(['status' => 1, 'poster' => $posterdata['poster']]);
    }
}