<?php

namespace app\controller;

use think\facade\Db;

class ApiYuyue extends ApiCommon
{
    public function getprolist()
    {
        $where = [];
        $where[] = ['aid', '=', aid];
        $where[] = ['ischecked', '=', 1];
        //$where[] = ['status','=',1];
        $nowtime = time();
        $where[] = Db::raw("`status`=1  or (`status`=2 and unix_timestamp(start_time)<=$nowtime and unix_timestamp(end_time)>=$nowtime)");

        if (input('param.bid')) {
            $bid = input('param.bid/d');
        } else {
            $bid = 0;
        }
        if ($bid) {
            $where[] = ['bid', '=', $bid];
        } else {
            $business_sysset = Db::name('business_sysset')->where('aid', aid)->find();
            if (!$business_sysset || $business_sysset['status'] == 0 || $business_sysset['product_isshow'] == 0) {
                $where[] = ['bid', '=', 0];
            }
        }

        if (input('param.field') && input('param.order')) {
            $order = input('param.field') . ' ' . input('param.order') . ',sort,id desc';
        } else {
            $order = 'sort desc,id desc';
        }
        //分类
        if (input('param.cid')) {
            $cid = input('post.cid') ? input('post.cid/d') : input('param.cid/d');
            //子分类
            $clist = Db::name('yuyue_category')->where('aid', aid)->where('pid', $cid)->column('id');
            if ($clist) {
                $clist2 = Db::name('yuyue_category')->where('aid', aid)->where('pid', 'in', $clist)->column('id');
                $cCate = array_merge($clist, $clist2, [$cid]);
                if ($cCate) {
                    $whereCid = [];
                    foreach ($cCate as $k => $c2) {
                        $whereCid[] = "find_in_set({$c2},cid)";
                    }
                    $where[] = Db::raw(implode(' or ', $whereCid));
                }
            } else {
                $where[] = Db::raw("find_in_set(" . $cid . ",cid)");
            }
        }
        if (input('param.keyword')) {
            $where[] = ['name', 'like', '%' . input('param.keyword') . '%'];
        }
        $pernum = 10;
        $pagenum = input('post.pagenum');
        if (!$pagenum) $pagenum = 1;
        $datalist = Db::name('yuyue_product')->field("id,pic,name,sales,sell_price,danwei")->where($where)->page($pagenum, $pernum)->order($order)->select()->toArray();
        if (!$datalist) $datalist = [];
        $datalist = $this->formatprolist($datalist);
        return $this->json(['status' => 1, 'data' => $datalist]);
    }

    public function prolist()
    {
        if (input('param.bid')) {
            $bid = input('param.bid/d');
        } else {
            $bid = 0;
        }
        //分类
        if (input('param.cid')) {
            $clist = Db::name('yuyue_category')->where('aid', aid)->where('pid', input('param.cid/d'))->where('status', 1)->order('sort desc,id')->select()->toArray();
            if (!$clist) $clist = [];
        } else {
            $clist = Db::name('yuyue_category')->where('aid', aid)->where('bid', $bid)->where('pid', 0)->where('status', 1)->order('sort desc,id')->select()->toArray();
            if (!$clist) $clist = [];
        }
        return $this->json(['clist' => $clist]);
    }

    //分类商品
    public function classify()
    {
        if (input('param.bid')) {
            $bid = input('param.bid/d');
        } else {
            $bid = 0;
        }
        $clist = Db::name('yuyue_category')->where('aid', aid)->where('pid', 0)->where('bid', $bid)->where('status', 1)->order('sort desc,id')->select()->toArray();
        foreach ($clist as $k => $v) {
            $rs = Db::name('yuyue_category')->where('aid', aid)->where('pid', $v['id'])->where('status', 1)->order('sort desc,id')->select()->toArray();
            if (!$rs) $rs = [];
            $clist[$k]['child'] = $rs;
        }
        return $this->json(['status' => 1, 'data' => $clist]);
    }

    //商品
    public function product()
    {
        $proid = input('param.id/d');
        $product = Db::name('yuyue_product')->where('id', $proid)->where('aid', aid)->find();
        if (!$product) return $this->json(['status' => 0, 'msg' => '商品不存在']);
        if ($product['status'] == 0) return $this->json(['status' => 0, 'msg' => '商品未上架']);
        if ($product['ischecked'] != 1) return $this->json(['status' => 0, 'msg' => '商品未审核']);

        if ($product['status'] == 2 && (strtotime($product['start_time']) > time() || strtotime($product['end_time']) < time())) {
            return $this->json(['status' => 0, 'msg' => '商品未上架']);
        }
        if ($product['status'] == 2) $product['status'] = 1;
        if (!$product['pics']) $product['pics'] = $product['pic'];
        $product['pics'] = explode(',', $product['pics']);
        $product = $this->formatproduct($product);

        //优惠券
        $couponlist = Db::name('coupon')->where('aid', aid)->where('bid', $product['bid'])->where('tolist', 1)->where('type', 'in', '1,4')->where("unix_timestamp(starttime)<=" . time() . " and unix_timestamp(endtime)>=" . time())->order('sort desc')->select()->toArray();
        $newcplist = [];
        foreach ($couponlist as $k => $v) {
            $gettj = explode(',', $v['gettj']);
            if (!in_array('-1', $gettj) && !in_array($this->member['levelid'], $gettj)) { //不是所有人
                continue;
            }
            if ($v['fwtype'] == 2) {//指定商品可用
                continue;
            }
            if ($v['fwtype'] == 1) {//指定类目可用
                continue;
            }
            $haveget = Db::name('coupon_record')->where('aid', aid)->where('mid', mid)->where('couponid', $v['id'])->count();
            $v['haveget'] = $haveget;
            //$v['starttime'] = date('m-d H:i',strtotime($v['starttime']));
            //$v['endtime'] = date('m-d H:i',strtotime($v['endtime']));
            if ($v['yxqtype'] == 1) {
                $yxqtime = explode(' ~ ', $v['yxqtime']);
                $v['yxqdate'] = strtotime($yxqtime[1]);
            } elseif ($v['yxqtype'] == 2) {
                $v['yxqdate'] = time() + 86400 * $v['yxqdate'];
            } elseif ($v['yxqtype'] == 3) {
                //次日起计算有效期
                $v['yxqdate'] = strtotime(date('Y-m-d')) + 86400 * ($v['yxqdate'] + 1) - 1;
            }
            if ($v['bid'] > 0) {
                $v['bname'] = Db::name('business')->where('aid', aid)->where('id', $v['bid'])->value('name');
            }
            $newcplist[] = $v;
        }


        //是否收藏
        $rs = Db::name('member_favorite')->where('aid', aid)->where('mid', mid)->where('proid', $proid)->where('type', 'yuyue')->find();
        if ($rs) {
            $isfavorite = true;
        } else {
            $isfavorite = false;
        }
        //获取评论
        $commentlist = Db::name('yuyue_comment')->where('aid', aid)->where('proid', $proid)->where('status', 1)->order('id desc')->limit(10)->select()->toArray();
        if (!$commentlist) $commentlist = [];
        foreach ($commentlist as $k => $pl) {
            $commentlist[$k]['createtime'] = date('Y-m-d H:i', $pl['createtime']);
            if ($commentlist[$k]['content_pic']) $commentlist[$k]['content_pic'] = explode(',', $commentlist[$k]['content_pic']);
        }
        $commentcount = Db::name('yuyue_comment')->where('aid', aid)->where('proid', $proid)->where('status', 1)->count();

        //添加浏览历史
        if (mid) {
            $rs = Db::name('member_history')->where('aid', aid)->where('mid', mid)->where('proid', $proid)->where('type', 'yuyue')->find();
            if ($rs) {
                Db::name('member_history')->where('id', $rs['id'])->update(['createtime' => time()]);
            } else {
                Db::name('member_history')->insert(['aid' => aid, 'mid' => mid, 'proid' => $proid, 'type' => 'yuyue', 'createtime' => time()]);
            }
        }
        //商品服务
        if ($product['fuwupoint']) {
            $fuwulist = explode(' ', $product['fuwupoint']);
        } else {
            $fuwulist = [];
        }
        if ($product['fwid']) {
            $fuwulist2 = Db::name('yuyue_fuwu')->where('aid', aid)->where('status', 1)->where('id', 'in', $product['fwid'])->order('sort desc,id')->select()->toArray();
        } else {
            $fuwulist2 = [];
        }

        $sysset = Db::name('admin_set')->where('aid', aid)->field('name,logo,desc,fxjiesuantype,tel,kfurl,gzts,ddbb')->find();

        if ($product['balance'] > 0) {
            $product['advance_price'] = round($product['sell_price'] * (1 - $product['balance'] * 0.01), 2);
            $product['balance_price'] = round($product['sell_price'] * $product['balance'] * 0.01, 2);
        }
        if ($product['bid'] != 0) {
            $business = Db::name('business')->where('aid', aid)->where('id', $product['bid'])->field('id,name,logo,desc,tel,address,sales')->find();
        } else {
            $business = $sysset;
        }
        $product['detail'] = \app\common\System::initpagecontent($product['detail'], aid, mid, platform);
        $product['comment_starnum'] = floor($product['comment_score']);

        $sysset['showgzts'] = false;
        //关注提示
        if (platform == 'mp') {
            $sysset['gzts'] = explode(',', $sysset['gzts']);
            if (in_array('2', $sysset['gzts']) && $this->member['subscribe'] == 0) {
                $appinfo = \app\common\System::appinfo(aid, 'mp');
                $sysset['qrcode'] = $appinfo['qrcode'];
                $sysset['gzhname'] = $appinfo['nickname'];
                $sysset['showgzts'] = true;
            }
        }
        //获取设置
        $sets = Db::name('yuyue_set')->where('aid', aid)->find();
        $times = [];
        $j = $product['wanhour'] - $product['zaohour'];

        for ($i = strtotime($product['zaohour'] . ':00'); $i <= strtotime($product['wanhour'] . ':00'); $i = $i + 60 * $product['timejg']) {
            $times[] = date("H:i", $i);
        }
        if ($product['rqtype'] == 1) {
            $datelist = $this->GetWeeks($product['yyzhouqi']);
        } else {
            $datelist = [];
            $yybeigntime = strtotime($product['yybegintime']);
            $yyendtime = strtotime($product['yyendtime']);
            $days = ($yyendtime - $yybeigntime) / 86400;
            for ($i = 0; $i <= $days; $i++) {
                $year = date('Y', $yybeigntime + 86400 * $i) . '年';
                $month = date('m', $yybeigntime + 86400 * $i) . '月';
                $day = date('d', $yybeigntime + 86400 * $i);
                $week = date('w', $yybeigntime + 86400 * $i);
                if ($week == '1') {
                    $week = '周一';
                    $key = 1;
                } elseif ($week == '2') {
                    $week = '周二';
                    $key = 2;
                } elseif ($week == '3') {
                    $week = '周三';
                    $key = 3;
                } elseif ($week == '4') {
                    $week = '周四';
                    $key = 4;
                } elseif ($week == '5') {
                    $week = '周五';
                    $key = 5;
                } elseif ($week == '6') {
                    $week = '周六';
                    $key = 6;
                } elseif ($week == '0') {
                    $week = '周日';
                    $key = 0;
                }
                $datelist[$i]['key'] = $key;
                $datelist[$i]['weeks'] = $week;
                $datelist[$i]['date'] = $month . $day;
                $datelist[$i]['year'] = $year;

            }
        }
        $minprice = 999999999999999;
        $maxprice = 0;
        $gglist = Db::name('yuyue_guige')->where('proid', $product['id'])->select()->toArray();
        if ($product['lvprice'] == 1) $gglist = $this->formatgglist($gglist, $product['bid']);
        foreach ($gglist as $k => $v) {
            if ($v['sell_price'] < $minprice) {
                $minprice = $v['sell_price'];
            }
            if ($v['sell_price'] > $maxprice) {
                $maxprice = $v['sell_price'];
            }
        }
        $product['min_price'] = round($minprice, 2);
        $product['max_price'] = round($maxprice, 2);

        $rdata = [];
        $rdata['status'] = 1;
        $rdata['title'] = $product['name'];

        $rdata['sysset'] = $sysset;
        $rdata['isfavorite'] = $isfavorite;
        $rdata['product'] = $product;
        $rdata['fuwulist2'] = $fuwulist2;
        $rdata['fuwulist'] = $fuwulist;
        $rdata['business'] = $business;
        $rdata['commentlist'] = $commentlist;
        $rdata['commentcount'] = $commentcount;
        $rdata['datelist'] = $datelist;
        $rdata['daydate'] = $daydate;
        //$rdata['couponlist'] = $newcplist;
        return $this->json($rdata);
    }

    //获取商品详情

    function GetWeeks($yyzhouqi)
    {
        //查看今天周几
        $zqarr = explode(',', $yyzhouqi);
        $i = 0;
        $weeks = [];
        for ($i; $i < 7; $i++) {
            $year = date('Y', time() + 86400 * $i) . '年';
            $month = date('m', time() + 86400 * $i) . '月';
            $day = date('d', time() + 86400 * $i);
            $week = date('w', time() + 86400 * $i);
            if ($week == '1') {
                $week = '周一';
                $key = 1;
            } elseif ($week == '2') {
                $week = '周二';
                $key = 2;
            } elseif ($week == '3') {
                $week = '周三';
                $key = 3;
            } elseif ($week == '4') {
                $week = '周四';
                $key = 4;
            } elseif ($week == '5') {
                $week = '周五';
                $key = 5;
            } elseif ($week == '6') {
                $week = '周六';
                $key = 6;
            } elseif ($week == '0') {
                $week = '周日';
                $key = 0;
            }
            $weeks[$i]['key'] = $key;
            $weeks[$i]['weeks'] = $week;
            $weeks[$i]['date'] = $month . $day;
            $weeks[$i]['year'] = $year;
            //array_push($weeks,$month.$day."(".$week."）");
            $newweek = [];
            foreach ($weeks as $k => $w) {
                if (!in_array($w['key'], $zqarr)) {
                    unset($weeks[$k]);
                }
            }
        }
        $weeks = array_values($weeks);
        return $weeks;
    }

    //商品评价

    public function getproductdetail()
    {
        $proid = input('param.id/d');
        $where = [];
        $where[] = ['aid', '=', aid];
        $where[] = ['id', '=', $proid];
        $product = Db::name('yuyue_product')->field("bid,id,pic,name,sales,sell_price,guigedata,status,ischecked,start_time,end_time")->where($where)->find();
        if (!$product) {
            return $this->json(['status' => 0, 'msg' => '商品不存在']);
        }
        $product = $this->formatproduct($product);
        if ($product['status'] == 0) return $this->json(['status' => 0, 'msg' => '商品已下架']);
        if ($product['ischecked'] != 1) return $this->json(['status' => 0, 'msg' => '商品未审核']);
        if ($product['status'] == 2 && (strtotime($product['start_time']) > time() || strtotime($product['end_time']) < time())) {
            return $this->json(['status' => 0, 'msg' => '商品未上架']);
        }

        $gglist = Db::name('yuyue_guige')->where('proid', $product['id'])->select()->toArray();
        $guigelist = array();
        foreach ($gglist as $k => $v) {
            if ($product['balance'] > 0) {
                $v['advance_price'] = round($v['sell_price'] * (1 - $product['balance'] * 0.01), 2);
                $v['balance_price'] = round($v['sell_price'] * $product['balance'] * 0.01, 2);
            } else {
                $v['balance_price'] = 0;
            }
            $guigelist[$v['ks']] = $v;
        }
        $guigedata = json_decode($product['guigedata'], true);
        $ggselected = [];
        foreach ($guigedata as $v) {
            $ggselected[] = 0;
        }
        $ks = implode(',', $ggselected);
        return $this->json(['status' => 1, 'product' => $product, 'guigelist' => $guigelist, 'guigedata' => $guigedata, 'ggselected' => $ggselected, 'ks' => $ks]);
    }

    //商品海报

    public function commentlist()
    {
        $proid = input('param.proid/d');
        $pagenum = input('post.pagenum');
        if (!$pagenum) $pagenum = 1;
        $pernum = 20;
        $where = [];
        $where[] = ['aid', '=', aid];
        $where[] = ['proid', '=', $proid];
        $where[] = ['status', '=', 1];
        $datalist = Db::name('yuyue_comment')->where($where)->page($pagenum, $pernum)->order('id desc')->select()->toArray();
        if (!$datalist) $datalist = [];
        foreach ($datalist as $k => $pl) {
            $datalist[$k]['createtime'] = date('Y-m-d H:i', $pl['createtime']);
            if ($datalist[$k]['content_pic']) $datalist[$k]['content_pic'] = explode(',', $datalist[$k]['content_pic']);
        }
        if (request()->isPost()) {
            return $this->json(['status' => 1, 'data' => $datalist]);
        }
        $rdata = [];
        $rdata['datalist'] = $datalist;
        return $this->json($rdata);
    }


    //订单提交页

    function getposter()
    {
        $this->checklogin();
        $post = input('post.');
        $platform = platform;
        $page = '/activity/yuyue/product';
        $scene = 'id_' . $post['proid'] . '-pid_' . $this->member['id'];
        //if($platform == 'mp' || $platform == 'h5' || $platform == 'app'){
        //	$page = PRE_URL .'/h5/'.aid.'.html#'. $page;
        //}
        $posterset = Db::name('admin_set_poster')->where('aid', aid)->where('type', 'yuyue')->where('platform', $platform)->order('id')->find();

        $posterdata = Db::name('member_poster')->where('aid', aid)->where('mid', mid)->where('scene', $scene)->where('type', 'yuyue')->where('posterid', $posterset['id'])->find();
        if (true || !$posterdata) {
            $product = Db::name('yuyue_product')->where('id', $post['proid'])->find();
            $product = $this->formatproduct($product);
            $sysset = Db::name('admin_set')->where('aid', aid)->find();
            $textReplaceArr = [
                '[头像]' => $this->member['headimg'],
                '[昵称]' => $this->member['nickname'],
                '[姓名]' => $this->member['realname'],
                '[手机号]' => $this->member['mobile'],
                '[商城名称]' => $sysset['name'],
                '[商品名称]' => $product['name'],
                '[商品销售价]' => $product['sell_price'],
                '[商品市场价]' => $product['sell_price'],
                '[商品图片]' => $product['pic'],
            ];

            $poster = $this->_getposter(aid, $platform, $posterset['content'], $page, $scene, $textReplaceArr);
            $posterdata = [];
            $posterdata['aid'] = aid;
            $posterdata['mid'] = $this->member['id'];
            $posterdata['scene'] = $scene;
            $posterdata['page'] = $page;
            $posterdata['type'] = 'yuyue';
            $posterdata['poster'] = $poster;
            $posterdata['createtime'] = time();
            Db::name('member_poster')->insert($posterdata);
        }
        return $this->json(['status' => 1, 'poster' => $posterdata['poster']]);
    }

    public function buy()
    {
        $this->checklogin();
        $prodata = explode('-', input('param.prodata'));
        $multi_promotion = 0;
        if (in_array('multi_promotion', getcustom())) {
            $multi_promotion = 1;
        }
        $adminset = Db::name('admin_set')->where('aid', aid)->find();
        //会员折扣
        $userlevel = Db::name('member_level')->where('aid', aid)->where('id', $this->member['levelid'])->find();
        $userinfo = [];
        $userinfo['id'] = $this->member['id'];
        $userinfo['realname'] = $this->member['realname'];
        $userinfo['tel'] = $this->member['tel'];
        $userinfo['discount'] = $userlevel['discount'];


        $allbuydata = [];
        $autofahuo = 0;

        foreach ($prodata as $key => $gwc) {
            list($proid, $ggid, $num) = explode(',', $gwc);
            $product = Db::name('yuyue_product')->field("id,aid,bid,cid,pic,name,sales,sell_price,status,balance,fwpeople,fwtype,formdata,start_time,end_time")->where('aid', aid)->where('ischecked', 1)->where('id', $proid)->find();
            if ($product['status'] == 0) {
                return $this->json(['status' => 0, 'msg' => '商品未上架']);
            }
            if ($product['status'] == 2 && (strtotime($product['start_time']) > time() || strtotime($product['end_time']) < time())) {
                return $this->json(['status' => 0, 'msg' => '商品未上架']);
            }

            if ($product['freighttype'] == 3 || $product['freighttype'] == 4) $autofahuo = $product['freighttype'];
            $guige = Db::name('yuyue_guige')->where('id', $ggid)->find();

            if (!$allbuydata[$product['bid']]) $allbuydata[$product['bid']] = [];
            if (!$allbuydata[$product['bid']]['prodata']) $allbuydata[$product['bid']]['prodata'] = [];
            $allbuydata[$product['bid']]['prodata'][] = ['product' => $product, 'guige' => $guige, 'num' => $num];
        }

        //服务人员
        $worker_id = input('param.worker_id');
        if ($worker_id) {
            $fw = Db::name('yuyue_worker')->where('aid', aid)->where('id', $worker_id)->find();
            if (!$fw) $fw = [];
        }

        $address = Db::name('member_address')->where('aid', aid)->where('mid', mid)->order('isdefault desc,id desc')->find();
        if (!$address) $address = [];
        $needLocation = 0;
        $allproduct_price = 0;
        foreach ($allbuydata as $bid => $buydata) {
            if ($bid != 0) {
                $business = Db::name('business')->where('id', $bid)->field('id,aid,cid,name,logo,tel,address,sales,longitude,latitude,start_hours,end_hours,start_hours2,end_hours2,start_hours3,end_hours3,invoice,invoice_type')->find();

                $is_open = 0;
                if ($is_open == 0) {
                    if ($business['start_hours'] != $business['end_hours']) {
                        $start_time = strtotime(date('Y-m-d ' . $business['start_hours']));
                        $end_time = strtotime(date('Y-m-d ' . $business['end_hours']));
                        if (($start_time < $end_time && ($start_time > time() || $end_time < time())) || ($start_time > $end_time && ($start_time > time() && $end_time < time()))) {
                            //return $this->json(['status'=>-4,'msg'=>'商家不在营业时间']);
                        } else {
                            $is_open = 1;
                        }
                    } else {
                        $is_open = 1;
                    }
                }
                if ($is_open == 0) {
                    $start_time = strtotime(date('Y-m-d ' . $business['start_hours2']));
                    $end_time = strtotime(date('Y-m-d ' . $business['end_hours2']));
                    if ($start_time == $end_time || ($start_time < $end_time && ($start_time > time() || $end_time < time())) || ($start_time > $end_time && ($start_time > time() && $end_time < time()))) {
                        //return $this->json(['status'=>-4,'msg'=>'商家不在营业时间']);
                    } else {
                        $is_open = 1;
                    }
                }
                if ($is_open == 0) {
                    $start_time = strtotime(date('Y-m-d ' . $business['start_hours3']));
                    $end_time = strtotime(date('Y-m-d ' . $business['end_hours3']));
                    if ($start_time == $end_time || ($start_time < $end_time && ($start_time > time() || $end_time < time())) || ($start_time > $end_time && ($start_time > time() && $end_time < time()))) {
                        //return $this->json(['status'=>-4,'msg'=>'商家不在营业时间']);
                    } else {
                        $is_open = 1;
                    }
                }
                if ($is_open == 0) {
                    return $this->json(['status' => -4, 'msg' => '商家不在营业时间']);
                }
            } else {
                $business = Db::name('admin_set')->where('aid', aid)->field('id,name,logo,desc,tel')->find();
            }

            $product_priceArr = [];
            $product_price = 0;
            $needzkproduct_price = 0;
            $totalweight = 0;
            $totalnum = 0;
            $prodataArr = [];
            $proids = [];
            $cids = [];
            foreach ($buydata['prodata'] as $prodata) {
                $product_priceArr[] = $prodata['guige']['sell_price'] * $prodata['num'];
                $product_price += $prodata['guige']['sell_price'] * $prodata['num'];
                if ($prodata['product']['balance']) {
                    $product_price = $product_price * (1 - $prodata['product']['balance'] * 0.01);
                }
                if ($prodata['product']['lvprice'] == 0 && $prodata['product']['no_discount'] == 0) { //未开启会员价
                    $needzkproduct_price += $prodata['guige']['sell_price'] * $prodata['num'];
                }
                $totalprice = $prodata['guige']['sell_price'] * $prodata['num'];
                $totalnum += $prodata['num'];
                $prodataArr[] = $prodata['product']['id'] . ',' . $prodata['guige']['id'] . ',' . $prodata['num'];
                $proids[] = $prodata['product']['id'];
                $cids = array_merge(explode(',', $prodata['product']['cid']), $cids);
            }
            $prodatastr = implode('-', $prodataArr);

            $yyset = db('yuyue_set')->where('aid', aid)->find();
            if (!$yyset) $yyset = [];
            $leveldk_money = 0;
            if ($yyset['discount'] == 1 && $userlevel && $userlevel['discount'] > 0 && $userlevel['discount'] < 10) {
                $leveldk_money = $needzkproduct_price * (1 - $userlevel['discount'] * 0.1);
            }
            $leveldk_money = round($leveldk_money, 2);
            $price = $product_price - $leveldk_money;
            $newcouponlist = [];
            if ($yyset['iscoupon'] == 1) {
                $couponList = Db::name('coupon_record')
                    ->where("bid=-1 or bid=" . $bid)->where('aid', aid)->where('mid', mid)->where('type', 'in', '1,4')->where('status', 0)->where('minprice', '<=', $price - $manjian_money)->where('starttime', '<=', time())->where('endtime', '>', time())
                    ->order('id desc')->select()->toArray();
                if (!$couponList) $couponList = [];
                //echo db('coupon_record')->getlastsql();
                foreach ($couponList as $k => $v) {
                    //$couponList[$k]['starttime'] = date('m-d H:i',$v['starttime']);
                    //$couponList[$k]['endtime'] = date('m-d H:i',$v['endtime']);
                    $couponinfo = Db::name('coupon')->where('aid', aid)->where('id', $v['couponid'])->find();
                    if ($couponinfo['fwtype'] == 2) {//指定商品可用
                        continue;
                    }
                    if ($couponinfo['fwtype'] == 1) {//指定类目可用
                        continue;
                    }
                    if ($v['bid'] > 0) {
                        $binfo = Db::name('business')->where('aid', aid)->where('id', $v['bid'])->find();
                        $v['bname'] = $binfo['name'];
                    }
                    $newcouponlist[] = $v;
                }
            }
            //查看服务方式
            if ($product['fwtype']) {
                $fwtype = explode(',', $product['fwtype']);
                $fwtypelist = [];
                if (in_array(2, $fwtype)) $fwtypelist[] = ['name' => '上门服务', 'key' => 2];
                if (in_array(1, $fwtype)) $fwtypelist[] = ['name' => '到店服务', 'key' => 1];
            }
            //取出设置的自定义表单
            //$yyset = db('yuyue_set')->where('aid',aid)->find();
            $couponList = $newcouponlist;

            $allbuydata[$bid]['bid'] = $bid;
            $allbuydata[$bid]['business'] = $business;
            $allbuydata[$bid]['prodatastr'] = $prodatastr;
            $allbuydata[$bid]['couponList'] = $couponList;
            $allbuydata[$bid]['couponCount'] = count($couponList);
            $allbuydata[$bid]['sell_price'] = round($price, 2);
            $allbuydata[$bid]['leveldk_money'] = $leveldk_money;
            $allbuydata[$bid]['coupon_money'] = 0;
            $allbuydata[$bid]['coupontype'] = 1;
            $allbuydata[$bid]['couponrid'] = 0;
            $allbuydata[$bid]['editorFormdata'] = [];
            $allbuydata[$bid]['product_price'] = round($product_price, 2);
            $allbuydata[$bid]['balance'] = $product['balance'];
            $allbuydata[$bid]['formdata'] = json_decode($product['formdata'], true);
            $allbuydata[$bid]['fwtype'] = $product['fwtype'];
            $allbuydata[$bid]['fwpeople'] = $product['fwpeople'];
            $allbuydata[$bid]['fw'] = $fw;
            $allproduct_price += $product_price;
        }

        $rdata = [];
        $rdata['status'] = 1;
        $rdata['address'] = $address;
        $rdata['linkman'] = $address ? $address['name'] : strval($userinfo['realname']);
        $rdata['tel'] = $address ? $address['tel'] : strval($userinfo['tel']);
        if (!$rdata['linkman']) {
            $lastorder = Db::name('yuyue_order')->where('aid', aid)->where('mid', mid)->where('linkman', '<>', '')->find();
            if ($lastorder) {
                $rdata['linkman'] = $lastorder['linkman'];
                $rdata['tel'] = $lastorder['tel'];
            }
        }
        $rdata['userinfo'] = $userinfo;
        $rdata['allbuydata'] = $allbuydata;
        $rdata['fwtypelist'] = $fwtypelist;
        $rdata['yyset'] = $yyset;
        return $this->json($rdata);
    }

    //保存自定义表单内容

    public function createOrder()
    {
        $this->checklogin();
        $sysset = Db::name('admin_set')->where('aid', aid)->find();
        $post = input('post.');
        //收货地址
        if ($post['fwtype'] == 1) {
            $address = ['id' => 0, 'name' => $post['linkman'], 'tel' => $post['tel'], 'area' => '', 'address' => ''];
        } else {
            $address = Db::name('member_address')->where('id', $post['addressid'])->where('aid', aid)->where('mid', mid)->find();
            if (!$address) return $this->json(['status' => 0, 'msg' => '所选收货地址不存在']);
        }
        $userlevel = Db::name('member_level')->where('aid', aid)->where('id', $this->member['levelid'])->find();


        $buydata = $post['buydata'];
        $couponridArr = [];
        foreach ($buydata as $data) { //判断有没有重复选择的优惠券
            if ($data['couponrid'] && in_array($data['couponrid'], $couponridArr)) {
                return $this->json(['status' => 0, 'msg' => t('优惠券') . '不可重复使用']);
            } elseif ($data['couponrid']) {
                $couponridArr[] = $data['couponrid'];
            }
        }
        $ordernum = date('ymdHis') . rand(100000, 999999);
        $i = 0;
        $alltotalprice = 0;
        foreach ($buydata as $data) {
            $i++;
            if ($data['prodata']) {
                $prodata = explode('-', $data['prodata']);
            } else {
                return $this->json(['status' => 0, 'msg' => '产品数据错误']);
            }
            $bid = $data['bid'];
            $product_priceArr = [];
            $product_price = 0;
            $balance_price = 0;
            $needzkproduct_price = 0;
            $totalnum = 0;
            $prolist = [];
            $proids = [];
            $cids = [];
            foreach ($prodata as $key => $pro) {
                $sdata = explode(',', $pro);
                $sdata[2] = intval($sdata[2]);
                if ($sdata[2] <= 0) return $this->json(['status' => 0, 'msg' => '购买数量有误']);
                $product = Db::name('yuyue_product')->where('aid', aid)->where('id', $sdata[0])->find();

                if ($product['fwpeople'] == 1 && !$post['worker_id']) {
                    return $this->json(['status' => 0, 'msg' => '请选择服务人员']);
                }
                if (!$product) return $this->json(['status' => 0, 'msg' => '产品不存在或已下架']);
                if ($product['status'] == 0) {
                    return $this->json(['status' => 0, 'msg' => '商品未上架']);
                }
                if ($product['status'] == 2 && (strtotime($product['start_time']) > time() || strtotime($product['end_time']) < time())) {
                    return $this->json(['status' => 0, 'msg' => '商品未上架']);
                }
                if ($key == 0) $title = $product['name'];
                $guige = Db::name('yuyue_guige')->where('aid', aid)->where('id', $sdata[1])->find();

                if (!$guige) return $this->json(['status' => 0, 'msg' => '产品规格不存在或已下架']);
                $totalnum += $sdata[2];
                $product_price += $guige['sell_price'] * $sdata[2];
                if ($product['balance']) {
                    $balance_price += $product_price * $product['balance'] * 0.01;
                    $product_price = $product_price * (1 - $product['balance'] * 0.01);
                }

                $product_priceArr[] = $guige['sell_price'] * $sdata[2];
                if ($product['lvprice'] == 0 && $product['no_discount'] == 0) { //未开启会员价
                    $needzkproduct_price += $guige['sell_price'] * $sdata[2];
                }
                $prolist[] = ['product' => $product, 'guige' => $guige, 'num' => $sdata[2]];
                $proids[] = $product['id'];
                $cids = array_merge($cids, explode(',', $product['cid']));
            }
            $totalprice = $product_price;
        }
        //查看预约人数
        if (input('param.yydate')) {
            $count = $this->getyytime(input('param.yydate'), $product['id']);
            if ($count >= $product['yynum']) {
                return $this->json(['status' => 0, 'msg' => '该段时间预约人数已满']);
            }
        }


        //查看该服务人员该时间是否已经预约出去
        if ($post['worker_id'] > 0) {
            $order = Db::name('yuyue_order')->where('aid', aid)->where('worker_id', $post['worker_id'])->where('status', 'in', '1,2')->where('yy_time', $post['yydate'])->find();
            if ($order) {
                return $this->json(['status' => 0, 'msg' => '该段时间不可预约']);
            }
        }


        $yyset = db('yuyue_set')->where('aid', aid)->find();
        //会员折扣
        $leveldk_money = 0;
        if ($yyset['discount'] == 1 && $userlevel && $userlevel['discount'] > 0 && $userlevel['discount'] < 10) {
            $leveldk_money = $needzkproduct_price * (1 - $userlevel['discount'] * 0.1);
        }
        if ($product_price >= $leveldk_money) {
            $totalprice = $product_price - $leveldk_money;
        } else {
            $totalprice = 0;
        }
        //派单设置
        if ($product['jiesuantype'] == 1) {
            if ($balance_price > 0) {
                $product_price = $product_price + $balance_price;
            }
            $paidan_money = $product_price * $product['tc_bfb'] / 100;
        } else {
            $paidan_money = $product['tcmoney'];
        }
        $yydate = explode('-', $post['yydate']);
        //开始时间
        $begindate = $yydate[0];
        if (strpos($begindate, '年') === false) {
            $begindate = date('Y') . '年' . $begindate;
        }
        $begindate = preg_replace(['/年|月/', '/日/'], ['-', ''], $begindate);
        $date = date('Y-m-d H:i:s', strtotime(date('H:i', time())));
        $begintime = strtotime($begindate);
        if ($begintime <= strtotime(date('H:i', time())) + $product['pdprehour'] * 60 * 60) {
            return $this->json(['status' => 0, 'msg' => '预约时间已过，请选择其他时间']);
        }

        //优惠券
        if ($data['couponrid'] > 0) {
            $couponrid = $data['couponrid'];
            $couponrecord = Db::name('coupon_record')->where("bid=-1 or bid=" . $data['bid'])->where('aid', aid)->where('mid', mid)->where('id', $couponrid)->find();
            if (!$couponrecord) {
                return $this->json(['status' => 0, 'msg' => '该' . t('优惠券') . '不存在']);
            } elseif ($couponrecord['status'] != 0) {
                return $this->json(['status' => 0, 'msg' => '该' . t('优惠券') . '已使用过了']);
            } elseif ($couponrecord['starttime'] > time()) {
                return $this->json(['status' => 0, 'msg' => '该' . t('优惠券') . '尚未开始使用']);
            } elseif ($couponrecord['endtime'] < time()) {
                return $this->json(['status' => 0, 'msg' => '该' . t('优惠券') . '已过期']);
            } elseif ($couponrecord['minprice'] > $totalprice) {
                return $this->json(['status' => 0, 'msg' => '该' . t('优惠券') . '不符合条件']);
            } elseif ($couponrecord['type'] != 1 && $couponrecord['type'] != 4) {
                return $this->json(['status' => 0, 'msg' => '该' . t('优惠券') . '不符合条件']);
            }

            $couponinfo = Db::name('coupon')->where('aid', aid)->where('id', $couponrecord['couponid'])->find();
            if ($couponinfo['fwtype'] == 2) {//指定商品可用
                return $this->json(['status' => 0, 'msg' => '该' . t('优惠券') . '指定商品可用']);
            }
            if ($couponinfo['fwtype'] == 1) {//指定类目可用
                return $this->json(['status' => 0, 'msg' => '该' . t('优惠券') . '指定分类可用']);
            }

            Db::name('coupon_record')->where('id', $couponrid)->update(['status' => 1, 'usetime' => time()]);
            if ($couponrecord['type'] == 4) {//运费抵扣券
                $coupon_money = $freight_price;
            } else {
                $coupon_money = $couponrecord['money'];
                if ($coupon_money > $totalprice) $coupon_money = $totalprice;
            }
        } else {
            $coupon_money = 0;
        }
        $totalprice = $totalprice - $coupon_money;
        $orderdata = [];
        $orderdata['aid'] = aid;
        $orderdata['mid'] = mid;
        $orderdata['bid'] = $data['bid'];
        if (count($buydata) > 1) {
            $orderdata['ordernum'] = $ordernum . '_' . $i;
        } else {
            $orderdata['ordernum'] = $ordernum;
        }

        $orderdata['title'] = $title . (count($prodata) > 1 ? '等' : '');
        $orderdata['linkman'] = $address['name'];
        $orderdata['tel'] = $address['tel'];
        $orderdata['area'] = $address['area'];
        $orderdata['address'] = $address['address'];
        $orderdata['longitude'] = $address['longitude'];
        $orderdata['latitude'] = $address['latitude'];
        $orderdata['area2'] = $address['province'] . ',' . $address['city'] . ',' . $address['district'];
        $orderdata['totalprice'] = $totalprice;
        $orderdata['leveldk_money'] = $leveldk_money;    //会员折扣
        $orderdata['product_price'] = $product_price;
        $orderdata['coupon_money'] = $coupon_money;        //优惠券抵扣
        $orderdata['coupon_rid'] = $couponrid;
        $orderdata['yy_time'] = $post['yydate']; //预约时间
        $orderdata['createtime'] = time();
        $orderdata['platform'] = platform;
        $orderdata['hexiao_code'] = random(16);
        $orderdata['remark'] = $post['remark'];
        $orderdata['proname'] = $product['name'];
        $orderdata['ggname'] = $guige['name'];
        $orderdata['num'] = $sdata[2];
        $orderdata['balance_price'] = $balance_price;
        $orderdata['propic'] = $product['pic'];
        $orderdata['proid'] = $product['id'];
        $orderdata['paidan_type'] = $product['fwpeople'];
        $orderdata['ggid'] = $guige['id'];
        $orderdata['worker_id'] = $post['worker_id'];
        $orderdata['fwtype'] = $post['fwtype']; //服务方式
        $orderdata['begintime'] = $begintime;
        $orderdata['hexiao_qr'] = createqrcode(m_url('admin/hexiao/hexiao?type=yuyue&co=' . $orderdata['hexiao_code']));
        if (in_array('buy_selectmember', getcustom())) {
            if ($post['checkmemid']) $orderdata['checkmemid'] = $post['checkmemid'];
        }
        $orderdata['paidan_money'] = $paidan_money;
        if (in_array('hmy_yuyue', getcustom())) {    //预约定制自己的分类id
            $cidarr = explode(',', $product['cid']);
            $appcid = db('yuyue_category')->field('appid')->where('id', 'in', $cidarr)->order('pid asc')->select()->toArray();
            $appcid = array_column($appcid, 'appid');
            $orderdata['firstCategory'] = $appcid[0];
            $orderdata['secondCategory'] = $appcid[1];

            $config = include(ROOT_PATH . 'config.php');
            $appId = $config['hmyyuyue']['appId'];
            $appSecret = $config['hmyyuyue']['appSecret'];
            $url2 = 'https://shifu.api.kkgj123.cn/api/1/commission/rule';
            $headrs = array('content-type: application/json;charset=UTF-8', 'appid:' . $appId, 'appSecret:' . $appSecret);
            $res2 = curl_get($url2, $param = [], $headrs);
            $res2 = json_decode($res2, true);
            if ($res2['code'] == 200) {
                $res2 = $res2['data'];
                if ($res2['platformServiceCommissionRule'] == 1) {
                    $orderdata['commission'] = $paidan_money;
                } else {
                    $orderdata['commission'] = $product_price;
                }
            }
        }
        $orderid = Db::name('yuyue_order')->insertGetId($orderdata);
        $this->saveformdata($orderid, 'yuyue_order', $data['formdata'], $product['id']);
        $payorderid = \app\model\Payorder::createorder(aid, $orderdata['bid'], $orderdata['mid'], 'yuyue', $orderid, $orderdata['ordernum'], $orderdata['title'], $orderdata['totalprice']);

        if ($balance_price > 0) {
            $balancedata = [];
            $balancedata['aid'] = aid;
            $balancedata['bid'] = $orderdata['bid'];
            $balancedata['mid'] = $orderdata['mid'];
            $balancedata['orderid'] = $orderid;
            $balancedata['ordernum'] = $orderdata['ordernum'];
            $balancedata['title'] = $orderdata['title'];
            $balancedata['money'] = $orderdata['balance_price'];
            $balancedata['type'] = 'yuyue_balance';
            $balancedata['score'] = 0;
            $balancedata['createtime'] = time();
            $balancedata['status'] = 0;
            $balance_pay_orderid = Db::name('payorder')->insertGetId($balancedata);
            Db::name('yuyue_order')->where('id', $orderid)->update(['balance_pay_orderid' => $balance_pay_orderid]);
        }
        $num = $sdata[2];
        Db::name('yuyue_product')->where('aid', aid)->where('id', $product['id'])->update(['sales' => Db::raw("sales+$num")]);
        return $this->json(['status' => 1, 'payorderid' => $payorderid, 'msg' => '提交成功']);

    }

    //订单列表

    public function getyytime($yydate, $proid)
    {
        $yydate = explode('-', $yydate);
        //开始时间
        $begindate = date('Y年') . $yydate[0];
        $begindate = preg_replace(['/年|月/', '/日/'], ['-', ''], $begindate);
        $begintime = strtotime($begindate);
        $ends = explode(' ', $yydate[0]);

        $where[] = ['begintime', '=', $begintime];
        $count = 0 + Db::name('yuyue_order')->where($where)->where('aid', aid)->where('status', 'in', '1,2')->where('proid', $proid)->count();
        return $count;
    }

    function saveformdata($orderid, $type = 'yuyue_order', $formdata, $proid)
    {
        if (!$orderid || !$formdata) return ['status' => 0];
        //根据orderid 取出proid
        $formfield = Db::name('yuyue_product')->where('id', $proid)->find();
        $formdataSet = json_decode($formfield['formdata'], true);
        //var_dump($formdataSet);die;
        $data = [];
        foreach ($formdataSet as $k => $v) {
            $value = $formdata['form' . $k];
            if (is_array($value)) {
                $value = implode(',', $value);
            }
            $value = strval($value);
            $data['form' . $k] = $v['val1'] . '^_^' . $value . '^_^' . $v['key'];
            if ($v['val3'] == 1 && $value === '') {
                return ['status' => 0, 'msg' => $v['val1'] . ' 必填'];
            }
        }
        $data['aid'] = aid;
        $data['type'] = 'yuyue_order';
        $data['orderid'] = $orderid;
        $data['createtime'] = time();
        Db::name('freight_formdata')->insert($data);
        return ['status' => 1];
    }

    function orderlist()
    {
        $this->checklogin();
        $st = input('param.st');
        if (!$st && $st !== '0') $st = 'all';
        $pagenum = input('param.pagenum') ? input('param.pagenum') : 1;
        $where = [];
        $where[] = ['aid', '=', aid];
        $where[] = ['mid', '=', mid];
        $where[] = ['delete', '=', 0];
        if ($st == 'all') {

        } elseif ($st == '0') {
            $where[] = ['status', '=', 0];
        } elseif ($st == '1') {
            $where[] = ['status', '=', 1];
        } elseif ($st == '2') {
            $where[] = ['status', '=', 2];
        } elseif ($st == '3') {
            $where[] = ['status', '=', 3];
        } elseif ($st == '4') {
            $where[] = ['status', '=', 4];
        }

        $datalist = Db::name('yuyue_order')->where($where)->order('id desc')->page($pagenum, 10)->select()->toArray();
        if (!$datalist) $datalist = [];
        foreach ($datalist as $key => $v) {
            if ($v['bid'] != 0) {
                $datalist[$key]['binfo'] = Db::name('business')->where('aid', aid)->where('id', $v['bid'])->field('id,name,logo')->find();
            }
            //查看服务状态
            if ($v['worker_orderid'] > 0) {
                $datalist[$key]['worker'] = Db::name('yuyue_worker_order')->where('aid', aid)->where('id', $v['worker_orderid'])->field('id,status')->find();
            }
            $datalist[$key]['senddate'] = date('Y-m-d H:i:s', $v['send_time']);
        }
        $rdata = [];
        $rdata['st'] = $st;
        $rdata['datalist'] = $datalist;
        return $this->json($rdata);
    }

    //获取接下来一周的日期

    public function orderdetail()
    {
        $detail = Db::name('yuyue_order')->where('id', input('param.id/d'))->where('aid', aid)->where('mid', mid)->find();
        if (!$detail) return $this->json(['status' => 0, 'msg' => '订单不存在']);
        $detail['createtime'] = $detail['createtime'] ? date('Y-m-d H:i:s', $detail['createtime']) : '';
        $detail['collect_time'] = $detail['collect_time'] ? date('Y-m-d H:i:s', $detail['collect_time']) : '';
        $detail['paytime'] = $detail['paytime'] ? date('Y-m-d H:i:s', $detail['paytime']) : '';
        $detail['refund_time'] = $detail['refund_time'] ? date('Y-m-d H:i:s', $detail['refund_time']) : '';
        $detail['send_time'] = $detail['send_time'] ? date('Y-m-d H:i:s', $detail['send_time']) : '';
        $detail['formdata'] = \app\model\Freight::getformdata($detail['id'], 'yuyue_order');
        $storeinfo = [];
        if ($detail['freight_type'] == 1) {
            $storeinfo = Db::name('mendian')->where('id', $detail['mdid'])->field('id,name,address,longitude,latitude')->find();
        }

        if ($detail['bid'] > 0) {
            $detail['binfo'] = Db::name('business')->where('aid', aid)->where('id', $detail['bid'])->field('id,name,logo')->find();
            $iscommentdp = 0;
            $commentdp = Db::name('business_comment')->where('orderid', $detail['id'])->where('aid', aid)->where('mid', mid)->find();
            if ($commentdp) $iscommentdp = 1;
        } else {
            $iscommentdp = 1;
        }

        $prolist = Db::name('yuyue_order')->where('id', $detail['id'])->find();

        $yuyueset = Db::name('yuyue_set')->where('aid', aid)->field('autoclose')->find();

        if ($detail['status'] == 0 && $yuyueset['autoclose'] > 0 && $detail['paytypeid'] != 5) {
            $lefttime = strtotime($detail['createtime']) + $yuyueset['autoclose'] * 60 - time();
            if ($lefttime < 0) $lefttime = 0;
        } else {
            $lefttime = 0;
        }

        //退款记录
        $refundOrder = Db::name('shop_refund_order')->where('refund_status', '>', 0)->where('aid', aid)->where('orderid', $detail['id'])->count();
        $refundingMoneyTotal = Db::name('shop_refund_order')->where('refund_status', 'in', [1, 4])->where('aid', aid)->where('orderid', $detail['id'])->sum('refund_money');
        $refundedMoneyTotal = Db::name('shop_refund_order')->where('refund_status', '=', 2)->where('aid', aid)->where('orderid', $detail['id'])->sum('refund_money');
        $detail['refundCount'] = $refundOrder;
        $detail['refundingMoneyTotal'] = $refundingMoneyTotal;
        $detail['refundedMoneyTotal'] = $refundedMoneyTotal;

        $rdata = [];
        $rdata['detail'] = $detail;
        $rdata['iscommentdp'] = $iscommentdp;
        $rdata['prolist'] = $prolist;
        $rdata['storeinfo'] = $storeinfo;
        $rdata['lefttime'] = $lefttime;
        $rdata['codtxt'] = Db::name('shop_sysset')->where('aid', aid)->value('codtxt');

        //转账汇款
        if ($detail['paytypeid'] == 5) {
            $set = Db::Name('admin_set')->Where('aid', Aid)->Find();
            $pay_transfer = 1;
            $pay_transfer_info['pay_transfer_account_name'] = $pay_transfer ? $set['pay_transfer_account_name'] : '';
            $pay_transfer_info['pay_transfer_account'] = $pay_transfer ? $set['pay_transfer_account'] : '';
            $pay_transfer_info['pay_transfer_bank'] = $pay_transfer ? $set['pay_transfer_bank'] : '';
            $pay_transfer_info['pay_transfer_desc'] = $pay_transfer ? $set['pay_transfer_desc'] : '';
            $rdata['pay_transfer_info'] = $pay_transfer_info;
            $payorder = Db::Name('payorder')->Where('id', $detail['payorderid'])->Where('aid', Aid)->Find();
            if ($payorder) {
                if ($payorder['check_status'] === 0) {
                    $payorder['check_status_label'] = '待审核';
                } elseif ($payorder['check_status'] == 1) {
                    $payorder['check_status_label'] = '通过';
                } elseif ($payorder['check_status'] == 2) {
                    $payorder['check_status_label'] = '驳回';
                } else {
                    $payorder['check_status_label'] = '未上传';
                }
            }
            $rdata['payorder'] = $payorder ? $payorder : [];
        }
        //发票
        $rdata['invoice'] = 0;
        if ($detail['bid']) {
            $rdata['invoice'] = Db::name('business')->where('aid', aid)->where('id', $detail['bid'])->value('invoice');
        } else {
            $rdata['invoice'] = Db::name('admin_set')->where('aid', aid)->value('invoice');
        }


        //定制 查看是否有工单可提交
        $rdata['isworkorder'] = 0;
        if (getcustom('workorder')) {
            $workcount = 0 + Db::name('workorder_category')->where('aid', aid)->where('status', 1)->where('isglorder', 2)->count();
            $rdata['detail']['isworkorder'] = $workcount > 0 ? 1 : 0;
        }

        return $this->json($rdata);
    }

    function refund()
    {//申请退款
        $this->checklogin();
        if (request()->isPost()) {
            $post = input('post.');
            $orderid = intval($post['orderid']);
            $money = floatval($post['money']);
            $order = Db::name('yuyue_order')->where('aid', aid)->where('mid', mid)->where('id', $orderid)->find();
            if ($order['worker_orderid'] > 0) {
                return $this->json(['status' => 0, 'msg' => '订单接单后不允许退款']);
            }
            if (!$order || ($order['status'] != 1 && $order['status'] != 2) || $order['refund_status'] == 2) {
                return $this->json(['status' => 0, 'msg' => '订单状态不符合退款要求']);
            }
            if ($money <= 0 || $money > $order['totalprice']) {
                return $this->json(['status' => 0, 'msg' => '退款金额有误']);
            }
            if (getcustom('hmy_yuyue')) {
                Db::name('yuyue_order')->where('id', $orderid)->where('aid', aid)->where('bid', $order['bid'])->update(['refund_time' => time(), 'status' => 4, 'refund_status' => 2, 'refund_reason' => $post['reason'], 'refund_money' => $money]);
                $order = Db::name('yuyue_order')->where('aid', aid)->where('mid', mid)->where('id', $orderid)->find();
                $rs = \app\custom\Yuyue::refund($order);
                return $this->json(['status' => 1, 'msg' => '退款成功']);
            } else {
                Db::name('yuyue_order')->where('aid', aid)->where('mid', mid)->where('id', $orderid)->update(['refund_time' => time(), 'refund_status' => 1, 'refund_reason' => $post['reason'], 'refund_money' => $money]);
                $tmplcontent = [];
                $tmplcontent['first'] = '有服务订单客户申请退款';
                $tmplcontent['remark'] = '点击进入查看~';
                $tmplcontent['keyword1'] = $order['ordernum'];
                $tmplcontent['keyword2'] = $money . '元';
                $tmplcontent['keyword3'] = $post['reason'];
                \app\common\Wechat::sendhttmpl(aid, $order['bid'], 'tmpl_ordertui', $tmplcontent, m_url('admin/order/yuyueorder'), $order['mdid']);
                return $this->json(['status' => 1, 'msg' => '提交成功,请等待商家审核']);
            }
        }
        $rdata = [];
        $rdata['price'] = input('param.price/f');
        $rdata['orderid'] = input('param.orderid/d');
        $order = Db::name('yuyue_order')->where('aid', aid)->where('mid', mid)->where('id', $rdata['orderid'])->find();
        $rdata['price'] = $order['totalprice'];
        return $this->json($rdata);
    }

    function logistics()
    {
        $get = input('param.');
        $worker_order = Db::name('yuyue_worker_order')->where('id', $get['express_no'])->find();
        if (getcustom('hmy_yuyue')) {
            //获取师傅信息
            $rs = \app\custom\Yuyue::getMaster($worker_order['worker_id']);
            $worker = [];
            $worker['realname'] = $rs['data']['name'];
            $worker['tel'] = $rs['data']['phone'] ? $rs['data']['phone'] : '';
            //$worker['lon'] =$rs['data']['lon'];
            //$worker['lat'] =$rs['data']['lat'];
        } else {
            $worker = Db::name('yuyue_worker')->where('id', $worker_order['worker_id'])->find();
        }
        $orderinfo = json_decode($worker_order['orderinfo'], true);
        $binfo = json_decode($worker_order['binfo'], true);
        $prolist = json_decode($worker_order['prolist'], true);

        if ($worker_order['juli'] > 1000) {
            $worker_order['juli'] = round($worker_order['juli'] / 1000, 1);
            $worker_order['juli_unit'] = 'km';
        } else {
            $worker_order['juli_unit'] = 'm';
        }
        $juli2 = getdistance($worker_order['longitude2'], $worker_order['latitude2'], $worker['longitude'], $worker['latitude'], 1);
        $worker_order['juli2'] = $juli2;
        if ($juli2 > 1000) {
            $worker_order['juli2'] = round($juli2 / 1000, 1);
            $worker_order['juli2_unit'] = 'km';
        } else {
            $worker_order['juli2_unit'] = 'm';
        }
        $worker_order['leftminute'] = ceil(($worker_order['yujitime'] - time()) / 60);
        $worker_order['ticheng'] = round($worker_order['ticheng'], 2);
        if ($worker_order['status'] == 3) {
            $worker_order['useminute'] = ceil(($worker_order['endtime'] - $worker_order['createtime']) / 60);
            $worker_order['useminute2'] = ceil(($worker_order['endtime'] - $worker_order['starttime']) / 60);
        }
        $info = Db::name('yuyue_set')->where('aid', aid)->find();
        $rdata = [];
        $rdata['worker_order'] = $worker_order;
        $rdata['binfo'] = $binfo;
        $rdata['worker'] = $worker;
        $rdata['orderinfo'] = $orderinfo;
        $rdata['prolist'] = $prolist;
        $rdata['set'] = $info;
        return $this->json($rdata);
    }

    function closeOrder()
    {
        $post = input('post.');
        $orderid = intval($post['orderid']);
        $order = Db::name('yuyue_order')->where('id', $orderid)->where('aid', aid)->where('mid', mid)->find();
        if (!$order || $order['status'] != 0) {
            return $this->json(['status' => 0, 'msg' => '关闭失败,订单状态错误']);
        }
        //优惠券抵扣的返还
        if ($order['coupon_rid'] > 0) {
            Db::name('coupon_record')->where('aid', aid)->where('mid', mid)->where('id', $order['coupon_rid'])->update(['status' => 0, 'usetime' => '']);
        }
        $rs = Db::name('yuyue_order')->where('id', $orderid)->where('aid', aid)->where('mid', mid)->update(['status' => 4]);
        return $this->json(['status' => 1, 'msg' => '操作成功']);
    }

    function delOrder()
    {
        $post = input('post.');
        $orderid = intval($post['orderid']);
        $order = Db::name('yuyue_order')->where('id', $orderid)->where('aid', aid)->where('mid', mid)->find();
        if (!$order || ($order['status'] != 4 && $order['status'] != 3)) {
            return $this->json(['status' => 0, 'msg' => '删除失败,订单状态错误']);
        }
        if ($order['status'] == 3) {
            $rs = Db::name('yuyue_order')->where('id', $orderid)->where('aid', aid)->where('mid', mid)->update(['delete' => 1]);
        } else {
            $rs = Db::name('yuyue_order')->where('id', $orderid)->where('aid', aid)->where('mid', mid)->delete();
        }
        return $this->json(['status' => 1, 'msg' => '删除成功']);
    }

    //评价

    function orderCollect()
    { //确认完成
        $post = input('post.');
        $orderid = intval($post['orderid']);
        $order = Db::name('yuyue_order')->where('aid', aid)->where('mid', mid)->where('id', $orderid)->find();
        if (!$order || ($order['status'] != 2) || $order['paytype'] == '货到付款') {
            return $this->json(['status' => 0, 'msg' => '订单状态不符合收货要求']);
        }
        if ($order['balance_price'] > 0 && $order['balance_pay_status'] == 0) return $this->json(['status' => 0, 'msg' => '请先支付尾款']);
        $rs = \app\common\Order::collect($order, 'shop');
        if ($rs['status'] == 0) return $this->json($rs);

        Db::name('yuyue_order')->where('aid', aid)->where('mid', mid)->where('id', $orderid)->update(['status' => 3, 'collect_time' => time()]);

        $return = ['status' => 1, 'msg' => '确认收货成功', 'url' => true];

        $tmplcontent = [];
        $tmplcontent['first'] = '有订单客户已确认完成';
        $tmplcontent['remark'] = '点击进入查看~';
        $tmplcontent['keyword1'] = $this->member['nickname'];
        $tmplcontent['keyword2'] = $order['ordernum'];
        $tmplcontent['keyword3'] = $order['totalprice'] . '元';
        $tmplcontent['keyword4'] = date('Y-m-d H:i', $order['paytime']);
        \app\common\Wechat::sendhttmpl(aid, $order['bid'], 'tmpl_ordershouhuo', $tmplcontent, m_url('admin/order/shoporder'), $order['mdid']);
        return $this->json($return);
    }

    //评价服务人员

    public function comment()
    {
        $oid = input('param.oid/d');
        $order = Db::name('yuyue_order')->where('id', $oid)->where('mid', mid)->find();
        if (!$order) {
            return $this->json(['status' => 0, 'msg' => '未查找到相关记录']);
        }
        $comment = Db::name('yuyue_comment')->where('orderid', $oid)->where('aid', aid)->where('mid', mid)->find();
        if (request()->isPost()) {
            if ($comment) {
                return $this->json(['status' => 0, 'msg' => '您已经评价过了']);
            }
            $content = input('post.content');
            $content_pic = input('post.content_pic');
            $score = input('post.score/d');
            if ($score < 1) {
                return $this->json(['status' => 0, 'msg' => '请打分']);
            }
            $data['aid'] = aid;
            $data['mid'] = mid;
            $data['bid'] = $order['bid'];
            $data['proid'] = $order['proid'];
            $data['proname'] = $order['proname'];
            $data['propic'] = $order['propic'];
            $data['orderid'] = $order['id'];
            $data['ordernum'] = $order['ordernum'];
            $data['score'] = $score;
            $data['content'] = $content;
            $data['openid'] = $this->member['openid'];
            $data['nickname'] = $this->member['nickname'];
            $data['headimg'] = $this->member['headimg'];
            $data['createtime'] = time();
            $data['content_pic'] = $content_pic;
            $data['ggid'] = $order['ggid'];
            $data['ggname'] = $order['ggname'];
            $data['status'] = ($shopset['comment_check'] == 1 ? 0 : 1);
            Db::name('yuyue_comment')->insert($data);
            Db::name('yuyue_order')->where('aid', aid)->where('mid', mid)->where('id', $oid)->update(['iscomment' => 1]);
            //Db::name('shop_order')->where('id',$order['id'])->update(['iscomment'=>1]);

            //如果不需要审核 增加产品评论数及评分
            if ($shopset['comment_check'] == 0) {
                $countnum = Db::name('yuyue_comment')->where('proid', $order_good['proid'])->where('status', 1)->count();
                $score = Db::name('yuyue_comment')->where('proid', $order_good['proid'])->where('status', 1)->avg('score'); //平均评分
                $haonum = Db::name('yuyue_comment')->where('proid', $order_good['proid'])->where('status', 1)->where('score', '>', 3)->count(); //好评数
                if ($countnum > 0) {
                    $haopercent = $haonum / $countnum * 100;
                } else {
                    $haopercent = 100;
                }
                Db::name('yuyue_product')->where('id', $order['proid'])->update(['comment_num' => $countnum, 'comment_score' => $score, 'comment_haopercent' => $haopercent]);
            }
            return $this->json(['status' => 1, 'msg' => '评价成功']);
        }
        $rdata = [];
        $rdata['order'] = $order;
        $rdata['comment'] = $comment;
        return $this->json($rdata);
    }

    public function commentps()
    {
        $id = input('param.id/d');
        $worker_order = Db::name('yuyue_worker_order')->where('id', $id)->where('mid', mid)->find();
        if (!$worker_order) return $this->json(['status' => 0, 'msg' => '未找到相关记录']);
        $comment = Db::name('yuyue_worker_comment')->where('orderid', $id)->where('aid', aid)->where('mid', mid)->find();
        if (request()->isPost()) {
            if ($comment) {
                return $this->json(['status' => 0, 'msg' => '您已经评价过了']);
            }
            $content = input('post.content');
            $content_pic = input('post.content_pic');
            $score = input('post.score/d');
            if ($score < 1) {
                return $this->json(['status' => 0, 'msg' => '请打分']);
            }
            $data['aid'] = aid;
            $data['mid'] = mid;
            $data['bid'] = $worker_order['bid'];
            $data['worker_id'] = $worker_order['worker_id'];
            $data['orderid'] = $worker_order['id'];
            $data['ordernum'] = $worker_order['ordernum'];
            $data['score'] = $score;
            $data['content'] = $content;
            $data['content_pic'] = $content_pic;
            $data['nickname'] = $this->member['nickname'];
            $data['headimg'] = $this->member['headimg'];
            $data['createtime'] = time();
            $data['status'] = 1;
            Db::name('yuyue_worker_comment')->insert($data);

            //如果不需要审核 增加配送员评论数及评分
            $countnum = Db::name('yuyue_worker_comment')->where('worker_id', $worker_order['worker_id'])->where('status', 1)->count();
            $score = Db::name('yuyue_worker_comment')->where('worker_id', $worker_order['worker_id'])->where('status', 1)->avg('score'); //平均评分
            $haonum = Db::name('yuyue_worker_comment')->where('worker_id', $worker_order['worker_id'])->where('status', 1)->where('score', '>', 3)->count(); //好评数
            if ($countnum > 0) {
                $haopercent = $haonum / $countnum * 100;
            } else {
                $haopercent = 100;
            }
            Db::name('yuyue_worker_order')->where('id', $worker_order['worker_id'])->update(['comment_num' => $countnum, 'comment_score' => $score, 'comment_haopercent' => $haopercent]);

            return $this->json(['status' => 1, 'msg' => '评价成功']);
        }
        $rdata = [];
        $rdata['worker_order'] = $worker_order;
        $rdata['comment'] = $comment;
        return $this->json($rdata);
    }

    //人员分类

    public function selectpeople()
    {
        $bid = input('param.bid');
        if (!$bid) $bid = 0;
        $type = input('param.type');
        $pernum = 10;
        $pagenum = input('post.pagenum');
        if (!$pagenum) $pagenum = 1;
        if ($type == 'list') {
            $where = [];
            $where[] = ['aid', '=', aid];
            $where[] = ['bid', '=', $bid];
            $where[] = Db::raw("`status`=1");
            if (input('param.field') && input('param.order')) {
                $order = input('param.field') . ' ' . input('param.order') . ',sort,id desc';
            } else {
                $order = 'sort desc,id desc';
            }
            if (input('param.cid')) {
                $where[] = ['cid', '=', input('param.cid')];
            }
            if (input('param.keyword')) {
                $where[] = ['realname', 'like', '%' . input('param.keyword') . '%'];
            }
            $datalist = Db::name('yuyue_worker')->where($where)->page($pagenum, $pernum)->order($order)->select()->toArray();
            foreach ($datalist as &$d) {
                $type = Db::name('yuyue_worker_category')->where(['id' => $d['cid']])->find();
                $d['typename'] = $type['name'];
            }
        } else {
            $pro = explode(',', input('param.prodata'));
            $yydate = input('param.yydate');
            $product = Db::name('yuyue_product')->field('fwpeoid')->where('id', $pro[0])->find();
            $peoarr = explode(',', $product['fwpeoid']);
            $datalist = Db::name('yuyue_worker')->where('aid', aid)->where('id', 'in', $peoarr)->order('sort desc,id')->select()->toArray();
            //查看该时间是否已经预约出去
            foreach ($datalist as &$d) {
                $type = Db::name('yuyue_worker_category')->where(['id' => $d['cid']])->find();
                $d['typename'] = $type['name'];
                $order = Db::name('yuyue_order')->where('aid', aid)->where('worker_id', $d['id'])->where('status', 'in', '1,2')->where('yy_time', $yydate)->find();
                $d['yystatus'] = 1;
                if ($order) {
                    $d['yystatus'] = -1;
                }
            }
        }

        if (!$datalist) $datalist = [];
        return $this->json(['status' => 1, 'data' => $datalist]);
    }

    //人员详情

    public function peocategory()
    {
        $bid = input('param.bid');
        if (!$bid) $bid = 0;
        $clist = Db::name('yuyue_worker_category')->where('aid', aid)->where('bid', $bid)->where('pid', 0)->where('status', 1)->order('sort desc,id')->select()->toArray();
        return $this->json(['status' => 1, 'data' => $clist]);
    }

    public function peodetail()
    {
        $id = input('param.id/d');
        $detail = Db::name('yuyue_worker')->where('id', $id)->where('aid', aid)->find();
        //服务商品数量
        $detail['count'] = 0 + Db::name('yuyue_product')->where('aid', aid)->where("find_in_set({$id},fwpeoid)")->count();
        if (!$detail) return $this->json(['status' => 0, 'msg' => '不存在']);
        return $this->json(['status' => 1, 'data' => $detail]);
    }

    public function getdlist()
    {
        $id = input('param.id/d');
        $type = input('param.curTopIndex/d');
        $pernum = 10;
        $pagenum = input('post.pagenum');
        if (!$pagenum) $pagenum = 1;
        if ($type == 0) {
            //服务商品
            $datalist = Db::name('yuyue_product')->where('aid', aid)->where("find_in_set({$id},fwpeoid)")->page($pagenum, $pernum)->order($order)->select();
        }
        if ($type == 1) {
            //	//评价列表
            $datalist = Db::name('yuyue_worker_comment')->where(['aid' => aid, 'worker_id' => $id])->page($pagenum, $pernum)->order($order)->select();
        }
        if (!$datalist) $datalist = [];
        $datalist = $this->formatprolist($datalist);
        return $this->json(['status' => 1, 'data' => $datalist]);
    }

    public function isgettime()
    {
        $date = input('param.date/t');
        if (strpos($date, '年') === false) {
            $date = date('Y') . '年' . $date;
        }
        $proid = input('param.proid/d');
        //获取设置
        $product = Db::name('yuyue_product')->where('aid', aid)->where('id', $proid)->find();
        $sets = Db::name('yuyue_set')->where('aid', aid)->find();
        if ($product['datetype'] == 1) {
            $timearr = [];
            $j = 0;
            $nowdate = strtotime(date('H:i', time())) + $product['pdprehour'] * 60 * 60;
            for ($i = strtotime($product['zaohour'] . ':00'); $i <= strtotime($product['wanhour'] . ':00'); $i = $i + 60 * $product['timejg']) {
                $j++;
                $time = strtotime(preg_replace(['/年|月/', '/日/'], ['-', ''], $date . ' ' . date("H:i", $i)));
                $count = Db::name('yuyue_order')->where('aid', aid)->where('proid', $proid)->where('begintime', '=', $time)->where('status', 'in', '1,2')->count();
                $order = Db::name('yuyue_order')->where('aid', aid)->where('proid', $proid)->where('begintime', '=', $time)->where('status', 'in', '1,2')->find();

                if ($count >= $product['yynum'] || $time < $nowdate) {
                    $timearr[$j]['status'] = 0;
                } else {
                    $timearr[$j]['status'] = 1;
                }
                $timearr[$j]['time'] = date("H:i", $i);
                $timearr[$j]['timeint'] = str_replace(':', '', date("H:i", $i));
            }
        }
        if ($product['datetype'] == 2) {
            $timearr = [];
            $timearrs = explode(',', $product['timepoint']);
            $nowdate = strtotime(date('H:i', time())) + $product['pdprehour'] * 60 * 60;
            foreach ($timearrs as $k => $t) {
                $time = strtotime(preg_replace(['/年|月/', '/日/'], ['-', ''], $date . ' ' . $t));
                $count = Db::name('yuyue_order')->where('aid', aid)->where('proid', $proid)->where('begintime', '=', $time)->where('status', 'in', '1,2')->count();
                if ($count >= $product['yynum'] || $time < $nowdate) {
                    $timearr[$k]['status'] = 0;
                } else {
                    $timearr[$k]['status'] = 1;
                }
                $timearr[$k]['time'] = $t;
                $timearr[$k]['timeint'] = str_replace(':', '', $t);
            }
        }
        return $this->json(['status' => 1, 'data' => $timearr]);
    }
}