<?php
/**
 * 点大商城（www.diandashop.com） - 微信公众号小程序商城系统!
 * Copyright © 2020 山东点大网络科技有限公司 保留所有权利
 * =========================================================

 * ----------------------------------------------
 * 您只能在商业授权范围内使用，不可二次转售、分发、分享、传播
 * 任何企业和个人不得对代码以任何目的任何形式的再发布
 * =========================================================
 */

// +----------------------------------------------------------------------
// | 收银台后台登录 
// +----------------------------------------------------------------------
namespace app\controller;
use app\BaseController;
use think\facade\View;
use think\facade\Db;

class RestaurantCashierLogin extends BaseController
{
	public $webinfo;
    public function initialize(){
		$request = request();
		$this->webinfo = Db::name('sysset')->where(['name'=>'webinfo'])->value('value');
		$this->webinfo = json_decode($this->webinfo,true);
		
		View::assign('webinfo',$this->webinfo);
		$reg_open = isset($this->webinfo['reg_open']) ? $this->webinfo['reg_open'] : 0;
		View::assign('reg_open',$reg_open);
		View::assign('webname',$this->webinfo['webname']);
	}
    //登录页
	public function index(){
		$remember = cookie('cashier_remember');
		if($remember == 1){//自动登录
			$rs = Db::name('admin_user')->where('un',cookie('username'))->find();
			if($rs && md5($rs['pwd']) == cookie('password')){
				session('ADMIN_LOGIN',1);
				session('ADMIN_UID',$rs['id']);
				session('ADMIN_AID',$rs['aid']);
				session('ADMIN_BID',$rs['bid']);
				session('ADMIN_NAME',$rs['un'] ? $rs['un'] : $rs['nickname']);
				session('IS_ADMIN',$rs['isadmin']);
				if($rs['isadmin'] == 2){ //有控制台权限
					session('BST_ID',$rs['id']);
				}else{
					session('BST_ID',null);
				}
				Db::name('admin_user')->where('id',$rs['id'])->update(['ip'=>request()->ip(),'logintime'=>time()]);
				Db::name('admin_loginlog')->insert(['aid'=>$rs['aid'],'uid'=>$rs['id'],'logintime'=>time(),'loginip'=>request()->ip(),'logintype'=>'收银台账号登录']);
				if(input('param.fromurl')){
					return redirect(input('param.fromurl'));
				}else{
                    $cwhere []= ['aid' ,'=',$rs['aid']];
                    if($rs['bid'] > 0){
                        $cwhere []= ['bid' ,'=',$rs['bid']];
                    }else{
                        $cwhere []= ['bid' ,'=',0];
                    }
                    $cashier = Db::name('cashier')->where($cwhere)->find();
                    if(!empty($cashier)){
                        header("Location:".PRE_URL.'/cashier/index.html#/index/index?id='.$cashier['id'].'&logout=1');
                    }
				}
			}
		}
        if(request()->isAjax()){
            $username = trim(input('post.username'));
            $password = trim(input('post.password'));
            $captcha = trim(input('post.captcha'));
            if($username=='' || $password==''){
                return json(['status'=>0,'msg'=>'用户名和密码不能为空']);
            }elseif($captcha == ''){
                return json(['status'=>0,'msg'=>'验证码不能为空']);
            }elseif(!captcha_check($captcha)){
                return json(['status'=>0,'msg'=>'验证码错误']);
            }
            $rs = Db::name('admin_user')->where('un',$username)->where('pwd',md5($password))->find();
            if(!$rs){
                return json(['status'=>2,'msg'=>'用户名或密码错误']);
            }elseif($rs['status']!=1){
                return json(['status'=>0,'msg'=>'该账号已禁用']);
            }
            if($rs['bid'] > 0){
                $binfo = Db::name('business')->where('id',$rs['bid'])->find();
                if($binfo['status'] != 1){
                    return json(['status'=>0,'msg'=>'该商家尚未审核通过']);
                }

            }
            $auth = $this->checkAuth($rs['id']);
            if($auth['status'] ==0){
                return json(['status'=>0,'msg'=>$auth['msg']]);
            }
            Db::name('admin_user')->where('un',$username)->where('pwd',md5($password))->update(['ip'=>request()->ip(),'logintime'=>time()]);

            session('ADMIN_LOGIN',1);
            session('ADMIN_UID',$rs['id']);
            session('ADMIN_AID',$rs['aid']);
            session('ADMIN_BID',$rs['bid']);
            session('ADMIN_NAME',$rs['un']);
            session('IS_ADMIN',$rs['isadmin']);
            if($rs['isadmin'] == 2){ //有控制台权限
                session('BST_ID',$rs['id']);
            }else{
                session('BST_ID',null);
            }
            Db::name('admin_loginlog')->insert(['aid'=>$rs['aid'],'uid'=>$rs['id'],'logintime'=>time(),'loginip'=>request()->ip(),'logintype'=>'收银台账号登录']);
            if(input('post.cashier_remember')){//记住密码
                cookie('cashier_remember',1,30*86400);
                cookie('username',$username,30*86400);
                cookie('password',md5(md5($password)),30*86400);
            }else{
                cookie('cashier_remember',null);
                cookie('username',null);
                cookie('password',null);
            }
            if(input('param.fromurl')){
                return json(['status'=>1,'msg'=>'登录成功','url'=>input('param.fromurl')]);
            }else{
                $cwhere []= ['aid' ,'=',$rs['aid']];
                if($rs['bid'] > 0){
                    $cwhere []= ['bid' ,'=',$rs['bid']];
                }else{
                    $cwhere []= ['bid' ,'=',0];
                }
                $cashier = Db::name('cashier')->where($cwhere)->find();
                if(empty($cashier)){
                    return  json(['status' =>0,'msg' =>'请创建收银台后再登录']);
                }else{
                    return json(['status'=>1,'msg'=>'登录成功','url'=>'/cashier/index.html#/index/index?id='.$cashier['id'].'&logout=1']);
                }
            }
        }
		$webinfo = Db::name('sysset')->where('name','webinfo')->value('value');
		$webinfo = json_decode($webinfo,true);
		View::assign('webinfo',$webinfo);
		return View::fetch();
    }
    public function login(){
        if(request()->isAjax()){
            $username = trim(input('post.username'));
            $password = trim(input('post.password'));
            $captcha = trim(input('post.captcha'));
            if($username=='' || $password==''){
                return json(['status'=>0,'msg'=>'用户名和密码不能为空']);
            }elseif($captcha == ''){
                return json(['status'=>0,'msg'=>'验证码不能为空']);
            }elseif(!captcha_check($captcha)){
                return json(['status'=>0,'msg'=>'验证码错误']);
            }
            $rs = Db::name('admin_user')->where('un',$username)->where('pwd',md5($password))->find();
            if(!$rs){
                return json(['status'=>2,'msg'=>'用户名或密码错误']);
            }elseif($rs['status']!=1){
                return json(['status'=>0,'msg'=>'该账号已禁用']);
            }
            if($rs['bid'] > 0){
                $binfo = Db::name('business')->where('id',$rs['bid'])->find();
                if($binfo['status'] != 1){
                    return json(['status'=>0,'msg'=>'该商家尚未审核通过']);
                }

            }
            $auth = $this->checkAuth($rs['id']);
            if($auth['status'] ==0){
                return json(['status'=>0,'msg'=>$auth['msg']]);
            }
            Db::name('admin_user')->where('un',$username)->where('pwd',md5($password))->update(['ip'=>request()->ip(),'logintime'=>time()]);

            session('ADMIN_LOGIN',1);
            session('ADMIN_UID',$rs['id']);
            session('ADMIN_AID',$rs['aid']);
            session('ADMIN_BID',$rs['bid']);
            session('ADMIN_NAME',$rs['un']);
            session('IS_ADMIN',$rs['isadmin']);
            if($rs['isadmin'] == 2){ //有控制台权限
                session('BST_ID',$rs['id']);
            }else{
                session('BST_ID',null);
            }
            Db::name('admin_loginlog')->insert(['aid'=>$rs['aid'],'uid'=>$rs['id'],'logintime'=>time(),'loginip'=>request()->ip(),'logintype'=>'收银台账号登录']);
            if(input('post.cashier_remember')){//记住密码
                cookie('cashier_remember',1,30*86400);
                cookie('username',$username,30*86400);
                cookie('password',md5(md5($password)),30*86400);
            }else{
                cookie('cashier_remember',null);
                cookie('username',null);
                cookie('password',null);
            }
            if(input('param.fromurl')){
                return json(['status'=>1,'msg'=>'登录成功','url'=>input('param.fromurl')]);
            }else{
                $cwhere []= ['aid' ,'=',$rs['aid']];
                if($rs['bid'] > 0){
                    $cwhere []= ['bid' ,'=',$rs['bid']];
                }else{
                    $cwhere []= ['bid' ,'=',0];
                }
                $cashier = Db::name('cashier')->where($cwhere)->find();
                if(empty($cashier)){
                    return  json(['status' =>0,'msg' =>'请创建收银台后再登录']);
                }else{
                    return json(['status'=>1,'msg'=>'登录成功','url'=>'/cashier/index.html#/index/index?id='.$cashier['id'].'&logout=1']);
                }
            }
        }
    }
    public function checkAuth($uid=0){
        $user = Db::name('admin_user')->where('id',$uid)->find();
        if($user['auth_type']==0){
            if($user['groupid']){
                $user['auth_data'] = Db::name('admin_user_group')->where('id',$user['groupid'])->value('auth_data');
            }
            $auth_data = json_decode($user['auth_data'],true);
            $auth_path = \app\common\Menu::blacklist();
            foreach($auth_data as $v){
                $auth_path = array_merge($auth_path,explode(',',$v));
            }
            $thispath = 'Cashier/index';
            if(!in_array('Cashier/*',$auth_path) && !in_array($thispath,$auth_path) && !session('BST_ID')){
                return ['status'=>0,'msg'=>'当前账号没有收银台权限'];
            }
        }
        return ['status'=>1,'msg'=>''];
        
    }
    //交班
    public function  jiaoban(){
        $rdata = $this->tongji();
        return json(['status'=>1,'msg'=>'成功','data' => $rdata]);
    }
    public function tongji(){
        $uid = session('ADMIN_UID');
        $aid =  session('ADMIN_AID');
        $bid = session('ADMIN_BID');
        $jiaobantime = time();
        $logintime = Db::name('admin_loginlog')->where('aid',$aid)->where('uid',$uid)->order('id desc')->where('logintype','收银台账号登录')->value('logintime');
        //收银员账号
        $cwhere []= ['aid' ,'=',$aid];
        if($bid > 0){
            $cwhere []= ['bid' ,'=',$bid];
        }else{
            $cwhere []= ['bid' ,'=',0];
        }
        $cashier = Db::name('cashier')->where($cwhere)->find();
        //订单数量
        $ordercount = Db::name('cashier_order')->where('uid',$uid)->where('paytime','between',[$logintime,$jiaobantime])->count();
        //今日营业总额  分别 现金 随行付 微信  支付宝
        $today_start_time = strtotime(date('Y-m-d 00:00:01'));
        if($logintime > $today_start_time){
            $today_start_time =$logintime;
        }
        $today_total_money = 0+ Db::name('cashier_order')->where('uid',$uid)->where('paytime','between',[$today_start_time,$jiaobantime])->sum('totalprice');
        //现金 0
        $today_cash_money =  0+ Db::name('cashier_order')->where('uid',$uid)->where('paytime','between',[$today_start_time,$jiaobantime])->where('paytypeid','=',0)->sum('totalprice');
        //随行付 5
        $today_sxf_money =  0+ Db::name('cashier_order')->where('uid',$uid)->where('paytime','between',[$today_start_time,$jiaobantime])->where('paytypeid','=',5)->sum('totalprice');
        //微信 2
        $today_wx_money =  0+ Db::name('cashier_order')->where('uid',$uid)->where('paytime','between',[$today_start_time,$jiaobantime])->where('paytypeid','=',2)->sum('totalprice');
        //支付宝
        $today_alipay_money =  0+ Db::name('cashier_order')->where('uid',$uid)->where('paytime','between',[$today_start_time,$jiaobantime])->where('paytypeid','=',3)->sum('totalprice');
        //余额 1
        $today_yue_money =  0+ Db::name('cashier_order')->where('uid',$uid)->where('paytime','between',[$today_start_time,$jiaobantime])->where('paytypeid','=',1)->sum('totalprice');
//今日会员总计 
        $today_member_total_money =0+ Db::name('cashier_order')->where('uid',$uid)->where('paytime','between',[$today_start_time,$jiaobantime])->where('mid','>',0)->sum('totalprice');
        //现金收款
        $today_member_cash_money =0+ Db::name('cashier_order')->where('uid',$uid)->where('paytime','between',[$today_start_time,$jiaobantime])->where('mid','>',0)->where('paytypeid','=',0)->sum('totalprice');
        //随行付
        $today_member_sxf_money =0+ Db::name('cashier_order')->where('uid',$uid)->where('paytime','between',[$today_start_time,$jiaobantime])->where('mid','>',0)->where('paytypeid','=',5)->sum('totalprice');
        //微信
        $today_member_wx_money =0+ Db::name('cashier_order')->where('uid',$uid)->where('paytime','between',[$today_start_time,$jiaobantime])->where('mid','>',0)->where('paytypeid','=',2)->sum('totalprice');
        //支付宝
        $today_member_alipay_money =0+ Db::name('cashier_order')->where('uid',$uid)->where('paytime','between',[$today_start_time,$jiaobantime])->where('mid','>',0)->where('paytypeid','=',3)->sum('totalprice');
        //余额
        $today_member_yue_money =0+ Db::name('cashier_order')->where('uid',$uid)->where('paytime','between',[$today_start_time,$jiaobantime])->where('mid','>',0)->where('paytypeid','=',1)->sum('totalprice');
//退款总额
        $today_refund_total_money=0+ Db::name('cashier_order')->where('uid',$uid)->where('refund_time','between',[$today_start_time,$jiaobantime])->where('status','=',10)->sum('refund_money');
        //退款 现金
        $today_refund_cash_money = 0+ Db::name('cashier_order')->where('uid',$uid)->where('refund_time','between',[$today_start_time,$jiaobantime])->where('status','=',10)->where('paytypeid','=',0)->sum('refund_money');
        //退款 随行付
        $today_refund_sxf_money = 0+ Db::name('cashier_order')->where('uid',$uid)->where('refund_time','between',[$today_start_time,$jiaobantime])->where('status','=',10)->where('paytypeid','=',5)->sum('refund_money');
        //退款 微信
        $today_refund_wx_money = 0+ Db::name('cashier_order')->where('uid',$uid)->where('refund_time','between',[$today_start_time,$jiaobantime])->where('status','=',10)->where('paytypeid','=',2)->sum('refund_money');
        //退款 支付宝
        $today_refund_alipay_money = 0+ Db::name('cashier_order')->where('uid',$uid)->where('refund_time','between',[$today_start_time,$jiaobantime])->where('status','=',10)->where('paytypeid','=',3)->sum('refund_money');
        //充值
        $total_recharge = Db::name('member_moneylog')->where('aid',$aid)->where('remark','收银台充值')->where('createtime','between',[$today_start_time,$jiaobantime])->sum('money');
        $cashdesk_user = Db::name('admin_user')->where('id',$uid)->value('un');

        //根据设置显示不同的支付信息
        $wxpay_show = true;
        $sxfpay_show = true;
        $alipay_show = true;
        $cashpay_show = true;
        $yuepay_show =  true;
        if($cashier['bid'] ==0){
            if(!$cashier['wxpay']){
                $wxpay_show = false;
            }
            if(!$cashier['sxpay']){
                $sxfpay_show = false;
            }
            if(!getcustom('cashdesk_alipay')){
                $alipay_show = false;
            }
            if(!$cashier['cashpay']){
                $cashpay_show = false;
            }
            if(!$cashier['cashpay']){
                $yuepay_show = false;
            }
        }else{//bid>0
            $sysset = Db::name('restaurant_admin_set')->where('aid',aid)->find();
            if(!$sysset['business_cashdesk_wxpay_type']){
                $wxpay_show = false;
            }
            if(!$sysset['business_cashdesk_sxpay_type']){
                $sxfpay_show = false;
            }
            if(!$sysset['business_cashdesk_alipay_type'] ||!getcustom('cashdesk_alipay')){
                $alipay_show = false;
            }
            if(!$sysset['business_cashdesk_cashpay']){
                $cashpay_show = false;
            }
            if(!$sysset['business_cashdesk_yue']){
                $yuepay_show = false;
            }
        }
        $rdata = [];
        $rdata['logintime'] = date('Y-m-d H:i:s',$logintime);
        $rdata['jiaobantime'] = date('Y-m-d H:i:s',$jiaobantime);
        $rdata['ordercount'] = $ordercount;
        $rdata['cashier_info'] =$cashier;
        $rdata['cashdesk_user'] =$cashdesk_user;
        //今日总营业额      
        $rdata['today_total_money'] =dd_money_format($today_total_money);
        $rdata['today_cash_money'] =dd_money_format($today_cash_money); //现金
        $rdata['today_sxf_money'] =dd_money_format($today_sxf_money); //随行付
        $rdata['today_wx_money'] =dd_money_format($today_wx_money); //微信
        $rdata['today_alipay_money'] =dd_money_format($today_alipay_money); //支付宝
        $rdata['today_yue_money'] =dd_money_format($today_yue_money); //支付宝
        //今日会员总计 
        $rdata['today_member_total_money'] =dd_money_format($today_member_total_money); //会员总计
        $rdata['today_member_cash_money'] =dd_money_format($today_member_cash_money); //会员 现金
        $rdata['today_member_sxf_money'] =dd_money_format($today_member_sxf_money); //会员 随行付
        $rdata['today_member_wx_money'] =dd_money_format($today_member_wx_money); //会员 微信
        $rdata['today_member_alipay_money'] =dd_money_format($today_member_alipay_money); //会员 支付宝
        $rdata['today_member_yue_money'] =dd_money_format($today_member_yue_money); //会员 余额
        //退款 
        $rdata['today_refund_total_money'] =dd_money_format($today_refund_total_money); //会员 总退款
        $rdata['today_refund_cash_money'] =dd_money_format($today_refund_cash_money); //会员 现金退款
        $rdata['today_refund_sxf_money'] =dd_money_format($today_refund_sxf_money); //会员 随行付退款
        $rdata['today_refund_wx_money'] =dd_money_format($today_refund_wx_money); //会员 微信退款
        $rdata['today_refund_alipay_money'] =dd_money_format($today_refund_alipay_money); //会员 支付宝退款
        $rdata['total_recharge'] =dd_money_format($total_recharge); //会员充值
        $rdata['total_money'] = dd_money_format($today_total_money - $today_yue_money + $total_recharge -$today_refund_total_money);
        $rdata['wxpay_show'] =$wxpay_show;
        $rdata['sxfpay_show'] =$sxfpay_show;
        $rdata['alipay_show'] =$alipay_show;
        $rdata['cashpay_show'] =$cashpay_show;
        $rdata['yuepay_show'] =$yuepay_show;
        return  $rdata;
    }
	//退出登录
	public function logout(){
        $is_print = input('param.is_print',0);//1：打印 0不打印
        if($is_print){
            $rdata = $this->tongji();
            \app\common\Wifiprint::jiaobanPrint($rdata,'cashier');
        }
        session('ADMIN_LOGIN',null);
		session('ADMIN_UID',null);
		session('ADMIN_AID',null);
		session('ADMIN_BID',null);
		session('ADMIN_NAME',null);
		session('IS_ADMIN',null);
		session('BST_ID',null);
		cookie('cashier_remember',null);
		cookie('usertel',null);
		cookie('password',null);
        return json(['status' =>1 ,'msg'=>'退出成功']);
    }
    
    public function tablelist()
    {
        $debug = request()->header('debug');
        if($debug){
             $where[] = ['aid', '=', 2];
        $where[] = ['bid', '=', 57];
        }else{
  if(!session('ADMIN_LOGIN')||!session('ADMIN_UID')){
            return json(['status'=>0,'msg'=>'请登录后操作']);
        }
        $where[] = ['aid', '=', session('ADMIN_AID')];
        $where[] = ['bid', '=', session('ADMIN_BID')];
        }
      

        $pernum = 20;
        $pagenum = input('param.pagenum');
        if (!$pagenum) $pagenum = 1;
        $keyword = input('param.keyword');
        if ($keyword) $where[] = ['name', 'like', '%' . $keyword . '%'];
        $cid = input('param.cid');
        if ($cid) $where[] = ['cid', '=', $cid];
        $status = input('param.tableStatus');
        if ($status !== '' && !is_null($status)) $where[] = ['status', '=', $status];
        $datalist = Db::name('restaurant_table')->where($where)->order('sort desc,id desc')->select()->toArray();
        
        if (!$datalist) $datalist = array();
        
        if($datalist){
            foreach ($datalist as $k=>$v){
                $order = [];
                if($v['orderid']){
                    $order = Db::name('restaurant_shop_order')->field('id,renshu,totalprice,createtime')->where('id',$v['orderid'])->find();
                    $difference = abs(time() - $order['createtime']);
                    $datearr = timediff($order['createtime'],time());
                    
                    $order['timelong'] = $datearr['day'].'天'.$datearr['hour'].'小时'.$datearr['min'].'分钟'.$datearr['sec'].'秒';
                    $order['dinnertime'] = date('Y-m-d H:i:s',$order['createtime']);
                    
                }
                $datalist[$k]['order'] = $order;
            }
        }
        
        $rdata = [];
        $rdata['status'] = 1;
        $rdata['datalist'] = $datalist;
        return json($rdata);
    }
    
    
    public function add()
    {
        if (request()->isPost()) {
            if(!session('ADMIN_LOGIN')||!session('ADMIN_UID')){
                return json(['status'=>0,'msg'=>'请登录后操作']);
            }
            define('aid',session('ADMIN_AID'));
            define('bid',session('ADMIN_BID'));
            $info = input('param.info');
            if (!empty($info['tel']) && !preg_match("/^1[3456789]{1}\d{9}$/", $info['tel'])) {
                return json(['status' => 0, 'msg' => '请检查手机号格式']);
            }
            if (empty($info['tableId'])) {
                return json(['status' => 0, 'msg' => '请选择餐桌']);
            }

            $table = Db::name('restaurant_table')->where('aid', aid)->where('bid', bid)->where('id', $info['tableId'])->find();
            if (empty($table)) {
                return json(['status' => 0, 'msg' => '餐桌不存在']);
            }
            if ($table['status'] != 0 || $table['orderid']) {
                return json(['status' => 0, 'msg' => '餐桌当前状态不可接受新订单，请检查']);
            }

            $insert = [
                'aid' => aid,
                'bid' => $info['bid'] ? $info['bid'] : 0,
                'mid' => session('ADMIN_UID'),
                'ordernum' => \app\common\Common::generateOrderNo(aid, 'restaurant_shop_order'),
                'tableid' => $info['tableId'],
                'renshu' => $info['renshu'],
                'tel' => $info['tel'],
                'message' => $info['message'],
                'linkman' => $info['linkman'],
                'platform' => 'cashier',
                'createtime' => time(),
                'status' => 0
            ];
            $insert['title'] = '堂食订单:' . $insert['ordernum'];
            //
            $orderid = Db::name('restaurant_shop_order')->insertGetId($insert);
//            $payorderid = \app\model\Payorder::createorder(aid,$insert['bid'],$insert['mid'],'restaurant_booking',$orderid,$insert['ordernum'],'预定订单：'.$insert['ordernum'],$insert['totalprice']);

            //更新餐桌状态
            Db::name('restaurant_table')->where('aid', aid)->where('bid', bid)->where('id', $info['tableId'])->update(['status' => 2, 'orderid' => $orderid]);
            return json_encode(['status' => 1, 'msg' => '下单成功，请开始点餐', 'id' => $orderid/*, 'payorderid' => $payorderid*/]);
        }
    }
    
    //点餐页面
    public function chooselist()
    {
        if(request()->header('debug')){
               define('aid',2);
        define('mid',65);
        }else{
             if(!session('ADMIN_LOGIN')||!session('ADMIN_UID')){
            return json(['status'=>0,'msg'=>'请登录后操作']);
        }
        define('aid',session('ADMIN_AID'));
        define('mid',session('ADMIN_UID'));
        }
         
        $bid = input('param.bid/d', 0);
        $bid = $bid?$bid:session('ADMIN_BID');
        // $bid = $bid?$bid:57;
        $tableId = input('param.tableId/d', 0);
        if(!$tableId){
            return json(['status' => 0, 'msg' => '桌号错误']);
        }
        if (!$bid) $bid = 0;
        if ($bid != 0) {
            $business = Db::name('business')->where('aid', aid)->where('id', $bid)->field('id,name,logo,content,pics,desc,tel,address,sales,start_hours,end_hours,zhengming')->find();
            $business['pic'] = explode(',', $business['pics'])[0];
            $business['zhengming'] = $business['zhengming'] ? explode(',', $business['zhengming']) : [];
        } else {
            $business = Db::name('admin_set')->where('aid', aid)->field('id,name,logo,desc,tel,address')->find();
        }
        $shop_set = Db::name('restaurant_shop_sysset')->where('aid', aid)->where('bid', $bid)->find();
        if ($shop_set['banner']) $business['pic'] = $shop_set['banner'];

        if ($shop_set['status'] == 0) {
            return json(['status' => 0, 'msg' => '该商家未开启点餐']);
        }
        if ($shop_set['start_hours'] != $shop_set['end_hours']) {
            $start_time = strtotime(date('Y-m-d ' . $shop_set['start_hours']));
            $end_time = strtotime(date('Y-m-d ' . $shop_set['end_hours']));
            if (($start_time < $end_time && ($start_time > time() || $end_time < time()))
                || ($start_time >= $end_time && ($start_time > time() && $end_time < time()))) {
                return json(['status' => 0, 'msg' => '该商家不在营业时间']);
            }
        }

        $cid = input('param.cid');
        if (!$cid) $cid = 0;
        $clist = Db::name('restaurant_product_category')->where('aid', aid)->where('bid', $bid)->where('pid', $cid)->where('status', 1)->where('is_shop', 1)->order('sort desc,id')->select()->toArray();
  
        foreach ($clist as $k => $v) {
            $where = [];
            $where[] = ['aid', '=', aid];
            $where[] = ['bid', '=', $bid];
            //$where[] = ['status','=',1];
            $where[] = ['ischecked', '=', 1];
//			$where[] = ['stock','>',0];
//			$where[] = Db::raw('stock_daily-sales_daily>0');
            $week = date("w");
            if ($week == 0) $week = 7;
            $where[] = Db::raw('find_in_set(' . $week . ',status_week)');
            $nowtime = time();
            $nowhm = date('H:i');
            $where[] = Db::raw("`status`=1 or (`status`=2 and unix_timestamp(start_time)<=$nowtime and unix_timestamp(end_time)>=$nowtime) or (`status`=3 and ((start_hours<end_hours and start_hours<='$nowhm' and end_hours>='$nowhm') or (start_hours>=end_hours and (start_hours<='$nowhm' or end_hours>='$nowhm'))) )");

            $where[] = Db::raw("find_in_set(" . $v['id'] . ",cid)");

            $prolist = Db::name('restaurant_product')->field("pic,id,name,stock,sales,market_price,sell_price,lvprice,lvprice_data,guigedata,limit_start,limit_per,stock_daily,sales_daily")->where($where)->orderRaw('if(stock_daily>sales_daily,1,0) desc,sort desc,id desc')->select()->toArray();
            if (!$prolist) $prolist = [];
            //$prolist = $this->formatprolist($prolist);
            if (!$prolist) {
                unset($clist[$k]);
            } else {
                foreach ($prolist as $k2 => $v2) {
                    $gglist = Db::name('restaurant_product_guige')->where('product_id', $v2['id'])->select()->toArray();
                    $prolist[$k2]['gglist'] = $gglist;
                    $prolist[$k2]['ggcount'] = count($gglist);
                    if ($v2['limit_start'] == 0) $v2['limit_start'] = 1;
                    if ($v2['limit_per'] == 0) $v2['limit_per'] = 999999;
                }
                $clist[$k]['prolist'] = $prolist;
            }
        }
        $clist = array_values($clist);

        $list = Db::name('restaurant_shop_cart')->where('aid', aid)->where('bid', $bid)->where('mid', mid)->where('tid', $tableId)->order('createtime desc')->select()->toArray();
        $total = 0;
        $totalprice = 0;
        foreach ($list as $k => $v) {
            $product = Db::name('restaurant_product')->field('cid,pic,id,name,stock,sales,market_price,sell_price,lvprice,lvprice_data,sellpoint,guigedata')->where('id', $v['proid'])->find();
            if (!$product) {
                unset($list[$k]);
                Db::name('restaurant_shop_cart')->where('id', $v['id'])->delete();
                continue;
            }
            //$product = $this->formatproduct($product);
            $guige = Db::name('restaurant_product_guige')->where('id', $v['ggid'])->find();
            if (!$guige) {
                unset($list[$k]);
                Db::name('restaurant_shop_cart')->where('id', $v['id'])->delete();
                continue;
            }
            if ($product['lvprice'] == 1) $guige = $this->formatguige($guige);
            $list[$k]['product'] = $product;
            $list[$k]['guige'] = $guige;
            $total += $v['num'];
            $totalprice += $guige['sell_price'] * $v['num'];
        }
        $totalprice = number_format($totalprice, 2, '.', '');

        $cartList = ['list' => $list, 'total' => $total, 'totalprice' => $totalprice];
        $numtotal = [];
        $numCat = [];
        foreach ($clist as $i => $v) {
            foreach ($v['prolist'] as $j => $pro) {
                $numtotal[$pro['id']] = 0;
                $numCat[$v['id']] = 0;
            }
        }
        foreach ($cartList['list'] as $i => $v) {
            $numtotal[$v['proid']] += $v['num'];
            //分类数量
            if ($v['product']['cid']) {
                $cids = explode(',', $v['product']['cid']);
                if ($cids) {
                    foreach ($cids as $cid)
                        $numCat[$cid] += $v['num'];
                }
            }
        }
        $tableName = '';
        if ($tableId) {
            $tableName = \db('restaurant_table')->where('aid', aid)->where('bid', $bid)->value('name');
        }

        $rdata = [];
        $rdata['status'] = 1;
        $rdata['data'] = $clist;
        $rdata['cartList'] = $cartList;
        $rdata['numtotal'] = $numtotal;
        $rdata['numCat'] = $numCat;
        $rdata['business'] = $business;
        $rdata['sysset'] = $shop_set;
        $rdata['table'] = ['name' => $tableName];
        return json($rdata);
    }
    
    //选菜
     public function addcart()
    {
        if(request()->header('debug')){
               define('aid',2);
        define('mid',65);
        }else{
             if(!session('ADMIN_LOGIN')||!session('ADMIN_UID')){
            return json(['status'=>0,'msg'=>'请登录后操作']);
        }
        define('aid',session('ADMIN_AID'));
        define('mid',session('ADMIN_UID'));
        }
        $post = input('post.');
        $oldnum = 0;
        $num = $post['num'];
        $tableId = intval($post['tableId']);

        $product = Db::name('restaurant_product')->where('aid', aid)->where('status', '<>', 0)->where('ischecked', 1)->where('id', $post['proid'])->find();
        if (!$product) return json(['status' => 0, 'msg' => '产品不存在或已下架']);
        if (!$tableId) return json(['status' => 0, 'msg' => '桌号错误']);
        if (!$post['ggid']) {
            if ($num > 0) {
                $post['ggid'] = Db::name('restaurant_product_guige')->where('proid', $post['proid'])->value('id');
            } else {
                $post['ggid'] = Db::name('restaurant_shop_cart')->where('aid', aid)->where('mid', mid)->where('tid', $tableId)->where('proid', $post['proid'])->order('id desc')->value('ggid');
            }
        }
        if ($num > 0 && $product['limit_per'] > 0) { //每单限购
            $hasnum = Db::name('restaurant_shop_cart')->where('aid', aid)->where('mid', mid)->where('tid', $tableId)->where('proid', $post['proid'])->sum('num');
            if ($hasnum + $num > $product['limit_per']) {
                return json(['status' => 0, 'msg' => '每单限购' . $product['limit_per'] . '份']);
            }
        }
        if ($product['limit_start'] > 0) { //有起售数量
            $hasnum = Db::name('restaurant_shop_cart')->where('aid', aid)->where('mid', mid)->where('tid', $tableId)->where('proid', $post['proid'])->sum('num');
            if ($num > 0) { // +
                if ($hasnum + $num < $product['limit_start']) $num = $product['limit_start'] - $hasnum;
            } else { // -
                if ($hasnum + $num < $product['limit_start']) $num = -$hasnum;
            }
        }

        $gwc = Db::name('restaurant_shop_cart')->where('aid', aid)->where('mid', mid)->where('tid', $tableId)->where('proid', $post['proid'])->where('ggid', $post['ggid'])->find();
        if ($gwc) $oldnum = $gwc['num'];

        if ($oldnum + $num <= 0) {
            Db::name('restaurant_shop_cart')->where('aid', aid)->where('mid', mid)->where('tid', $tableId)->where('proid', $post['proid'])->where('ggid', $post['ggid'])->delete();
            $cartnum = Db::name('restaurant_shop_cart')->where('aid', aid)->where('tid', $tableId)->where('mid', mid)->sum('num');
            return json(['status' => 1, 'msg' => '移除成功', 'cartnum' => $cartnum]);
        }
        if ($gwc) {
          if ($post['method']=='input') {
       Db::name('restaurant_shop_cart')->where('aid', aid)->where('mid', mid)->where('tid', $tableId)->where('proid', $post['proid'])->where('ggid', $post['ggid'])->update([
           'num' =>  floatval($post['num'])
       ]);
          }else{
             Db::name('restaurant_shop_cart')->where('aid', aid)->where('mid', mid)->where('tid', $tableId)->where('proid', $post['proid'])->where('ggid', $post['ggid'])->inc('num', $num)->update();
          }
        } else {
            $data = [];
            $data['aid'] = aid;
            $data['bid'] = $product['bid'];
            $data['mid'] = mid;
            $data['ggid'] = $post['ggid'];
            $data['createtime'] = time();
            $data['proid'] = $post['proid'];
            $data['num'] = $num;
            $data['tid'] = $tableId;
            Db::name('restaurant_shop_cart')->insert($data);
        }
        $cartnum = Db::name('restaurant_shop_cart')->where('aid', aid)->where('mid', mid)->where('tid', $tableId)->sum('num');
        return json(['status' => 1, 'msg' => '加入成功', 'cartnum' => $cartnum]);
    }
    
    //订单预览
    public function buyview()
    {
        //   if(request()->header('debug')){
        //        define('aid',2);
        // define('mid',65);
        // }else{
        //      if(!session('ADMIN_LOGIN')||!session('ADMIN_UID')){
        //     return json(['status'=>0,'msg'=>'请登录后操作']);
        // }
        // define('aid',session('ADMIN_AID'));
        // define('mid',session('ADMIN_UID'));
        // }
                     if(!session('ADMIN_LOGIN')||!session('ADMIN_UID')){
            return json(['status'=>0,'msg'=>'请登录后操作']);
        }
        define('aid',session('ADMIN_AID'));
        define('mid',session('ADMIN_UID'));
        $prodata = explode('-', input('param.prodata'));

        $tableid = input('param.tableId');
        $frompage = input('param.frompage');
        if (!$tableid) return json(['status' => 0, 'msg' => '请先扫描桌台二维码']);

        $tableinfo = Db::name('restaurant_table')->where('id', $tableid)->find();

        //桌子有订单号时,并且订单内有菜，为加菜
        if ($tableinfo['orderid']) {
            $order = Db::name('restaurant_shop_order')->where('aid', aid)->where('id', $tableinfo['orderid'])->find();
            $order['goods_count'] = Db::name('restaurant_shop_order_goods')->where('aid', aid)->where('orderid', $order['id'])->count();
        }

        $ordertype = $order['goods_count'] && ($order['status'] == 0 || $frompage == 'admin') ? 'edit_order' : 'create_order';//加菜，点菜

        //$userlevel = Db::name('member_level')->where('aid', aid)->where('id', $this->member['levelid'])->find();
        $adminset = Db::name('admin_set')->where('aid', aid)->find();
        $userinfo = [];
        $userinfo['discount'] = 0;
        //$userinfo['score'] = $this->member['score'];
        $userinfo['score'] = 0;
        $userinfo['score2money'] = $adminset['score2money'];
        $userinfo['scoredk_money'] = round($userinfo['score'] * $userinfo['score2money'], 2);
        $userinfo['scoredkmaxpercent'] = $adminset['scoredkmaxpercent'];
        $userinfo['scoremaxtype'] = 0; //0最大百分比 1最大抵扣金额
        /*$userinfo['realname'] = $this->member['realname'];
        $userinfo['tel'] = $this->member['tel'];*/

        $scoredkmaxmoney = 0;
        $allbuydata = [];
        $autofahuo = 0;
        foreach ($prodata as $key => $gwc) {
            list($proid, $ggid, $num) = explode(',', $gwc);
            $product = Db::name('restaurant_product')->field("id,aid,bid,cid,pic,name,sales,market_price,sell_price,lvprice,lvprice_data,freightdata,limit_per,scored_set,scored_val,status,start_time,end_time,start_hours,end_hours")->where('aid', aid)->where('ischecked', 1)->where('id', $proid)->find();
            if (!$product) {
                Db::name('restaurant_shop_cart')->where('aid', aid)->where('proid', $proid)->delete();
                return json(['status' => 0, 'msg' => '产品不存在或已下架']);
            }
            if ($product['status'] == 0) {
                return json(['status' => 0, 'msg' => '商品未上架']);
            }
            if ($product['status'] == 2 && (strtotime($product['start_time']) > time() || strtotime($product['end_time']) < time())) {
                return json(['status' => 0, 'msg' => '商品未上架']);
            }
            if ($product['status'] == 3) {
                $start_time = strtotime(date('Y-m-d ' . $product['start_hours']));
                $end_time = strtotime(date('Y-m-d ' . $product['end_hours']));
                if (($start_time < $end_time && ($start_time > time() || $end_time < time())) || ($start_time >= $end_time && ($start_time > time() && $end_time < time()))) {
                    return json(['status' => 0, 'msg' => '商品未上架']);
                }
            }
            if ($product['limit_per'] > 0 && $num > $product['limit_per']) { //每单限购
                return json(['status' => 0, 'msg' => $product['name'] . '每单限购' . $product['limit_per'] . '份']);
            }
            if ($product['limit_start'] > 0 && $num < $product['limit_start']) { //起售份数
                return json(['status' => 0, 'msg' => $product['name'] . '最低购买' . $product['limit_start'] . '份']);
            }
            if ($product['limit_start'] > 0) { //有起售数量
                $hasnum = Db::name('restaurant_shop_cart')->where('aid', aid)->where('mid', mid)->where('proid', $post['proid'])->sum('num');
                if ($num > 0) { // +
                    if ($hasnum + $num < $product['limit_start']) $num = $product['limit_start'] - $hasnum;
                } else { // -
                    if ($hasnum + $num < $product['limit_start']) $num = -$hasnum;
                }
            }

            $guige = Db::name('restaurant_product_guige')->where('id', $ggid)->find();
            if (!$guige) {
                Db::name('restaurant_shop_cart')->where('aid', aid)->where('ggid', $ggid)->delete();
                return json(['status' => 0, 'msg' => '产品该规格不存在或已下架']);
            }
            if ($guige['stock'] < $num || $guige['stock_daily'] - $guige['sales_daily'] < $num) {
                return json(['status' => 0, 'msg' => '库存不足']);
            }
            //$gettj = explode(',',$product['gettj']);
            //if(!in_array('-1',$gettj) && !in_array($this->member['levelid'],$gettj) && (!in_array('0',$gettj) || $this->member['subscribe']!=1)){ //不是所有人
            //	if(!$product['gettjtip']) $product['gettjtip'] = '没有权限购买该商品';
            //	return json(['status'=>0,'msg'=>$product['gettjtip'],'url'=>$product['gettjurl']]);
            //}
            if ($product['limit_per'] > 0) {
                if ($num > $product['limit_per']) {
                    return json(['status' => 0, 'msg' => '每单限购' . $product['limit_per'] . '件']);
                }
            }
            //if ($product['lvprice'] == 1) $guige = $this->formatguige($guige);
            if ($product['scored_set'] == 0) {
                if ($userinfo['scoredkmaxpercent'] > 0 && $userinfo['scoredkmaxpercent'] < 100) {
                    $scoredkmaxmoney += $userinfo['scoredkmaxpercent'] * 0.01 * $guige['sell_price'] * $num;
                } else {
                    $scoredkmaxmoney += $guige['sell_price'] * $num;
                }
            } elseif ($product['scored_set'] == 1) {
                $userinfo['scoremaxtype'] = 1;
                $scoredkmaxmoney += $product['scored_val'] * 0.01 * $guige['sell_price'] * $num;
            } elseif ($product['scored_set'] == 2) {
                $userinfo['scoremaxtype'] = 1;
                $scoredkmaxmoney += $product['scored_val'] * $num;
            } else {
                $userinfo['scoremaxtype'] = 1;
                $scoredkmaxmoney += 0;
            }

            if (!$allbuydata[$product['bid']]) $allbuydata[$product['bid']] = [];
            if (!$allbuydata[$product['bid']]['prodata']) $allbuydata[$product['bid']]['prodata'] = [];
            $allbuydata[$product['bid']]['prodata'][] = ['product' => $product, 'guige' => $guige, 'num' => $num];
        }
        $userinfo['scoredkmaxmoney'] = round($scoredkmaxmoney, 2);

        $allproduct_price = 0;
        foreach ($allbuydata as $bid => $buydata) {
            if ($bid != 0) {
                $business = Db::name('business')->where('id', $bid)->field('id,aid,cid,name,logo,tel,address,sales,longitude,latitude,start_hours,end_hours')->find();
                if ($business['start_hours'] != $business['end_hours']) {
                    $start_time = strtotime(date('Y-m-d ' . $business['start_hours']));
                    $end_time = strtotime(date('Y-m-d ' . $business['end_hours']));
                    if (($start_time < $end_time && ($start_time > time() || $end_time < time())) || ($start_time >= $end_time && ($start_time > time() && $end_time < time()))) {
                        return json(['status' => 0, 'msg' => '商家不在营业时间']);
                    }
                }
            } else {
                $business = Db::name('admin_set')->where('aid', aid)->field('id,name,logo,desc,tel')->find();
            }

            $shop_set = Db::name('restaurant_shop_sysset')->where('aid', aid)->where('bid', $bid)->find();
            if ($shop_set['status'] == 0) {
                return json(['status' => 0, 'msg' => '商家未开启点餐']);
            }
            if ($shop_set['start_hours'] != $shop_set['end_hours']) {
                $start_time = strtotime(date('Y-m-d ' . $shop_set['start_hours']));
                $end_time = strtotime(date('Y-m-d ' . $shop_set['end_hours']));
                if (($start_time < $end_time && ($start_time > time() || $end_time < time())) || ($start_time >= $end_time && ($start_time > time() && $end_time < time()))) {
                    return json(['status' => 0, 'msg' => '商家不在点餐时间']);
                }
            }

            $product_price = 0;
            $needzkproduct_price = 0;
            $totalweight = 0;
            $totalnum = 0;
            $prodataArr = [];
            $proids = [];
            $cids = [];
            foreach ($buydata['prodata'] as $prodata) {
                $product_price += $prodata['guige']['sell_price'] * $prodata['num'];
                if ($prodata['product']['lvprice'] == 0) { //未开启会员价
                    $needzkproduct_price += $prodata['guige']['sell_price'] * $prodata['num'];
                }
                $totalweight += $prodata['guige']['weight'] * $prodata['num'];
                $totalnum += $prodata['num'];
                $prodataArr[] = $prodata['product']['id'] . ',' . $prodata['guige']['id'] . ',' . $prodata['num'];
                $proids[] = $prodata['product']['id'];
                $cids = array_merge(explode(',', $prodata['product']['cid']), $cids);
            }
            $prodatastr = implode('-', $prodataArr);


            $leveldk_money = 0;
            if ($userlevel && $userlevel['discount'] > 0 && $userlevel['discount'] < 10) {
                $leveldk_money = $needzkproduct_price * (1 - $userlevel['discount'] * 0.1);
            }
            $leveldk_money = round($leveldk_money, 2);
            $price = $product_price - $leveldk_money;

            if ($ordertype == 'create_order') {
                //满减活动
                $mjset = Db::name('manjian_set')->where('aid', aid)->find();
                if ($mjset && $mjset['status'] == 1) {
                    $mjdata = json_decode($mjset['mjdata'], true);
                } else {
                    $mjdata = array();
                }
                $manjian_money = 0;
                $moneyduan = 0;
                if ($mjdata) {
                    foreach ($mjdata as $give) {
                        if (($product_price - $leveldk_money) * 1 >= $give['money'] * 1 && $give['money'] * 1 > $moneyduan) {
                            $moneyduan = $give['money'] * 1;
                            $manjian_money = $give['jian'] * 1;
                        }
                    }
                }
                if ($manjian_money > 0) {
                    $allbuydata[$bid]['manjian_money'] = round($manjian_money, 2);
                } else {
                    $allbuydata[$bid]['manjian_money'] = 0;
                }

                $newcouponlist = [];
                $couponList = Db::name('coupon_record')
                    ->where("bid=-1 or bid=" . $bid)->where('aid', aid)->where('mid', mid)->where('type', 'in', '1,4,5')->where('status', 0)->where('minprice', '<=', $price - $manjian_money)->where('starttime', '<=', time())->where('endtime', '>', time())
                    ->order('id desc')->select()->toArray();
                if (!$couponList) $couponList = [];
                foreach ($couponList as $k => $v) {
                    //$couponList[$k]['starttime'] = date('m-d H:i',$v['starttime']);
                    //$couponList[$k]['endtime'] = date('m-d H:i',$v['endtime']);
                    $couponinfo = Db::name('coupon')->where('aid', aid)->where('id', $v['couponid'])->find();
                    if ($couponinfo['fwtype'] == 2) {//指定商品可用
                        $productids = explode(',', $couponinfo['productids']);
                        if (!array_intersect($proids, $productids)) {
                            continue;
                        }
                    }
                    if ($couponinfo['fwtype'] == 1) {//指定类目可用
                        $categoryids = explode(',', $couponinfo['categoryids']);
                        $clist = Db::name('restaurant_product_category')->where('pid', 'in', $categoryids)->select()->toArray();
                        foreach ($clist as $kc => $vc) {
                            $categoryids[] = $vc['id'];
                            $cate2 = Db::name('restaurant_product_category')->where('pid', $vc['id'])->find();
                            $categoryids[] = $cate2['id'];
                        }
                        if (!array_intersect($cids, $categoryids)) {
                            continue;
                        }
                    }
                    $newcouponlist[] = $v;
                }
                $couponList = $newcouponlist;

                //促销活动
                $cuxiaolist = Db::name('restaurant_cuxiao')->where('aid', aid)->where('bid', $bid)->where("(type in (1,2,3,4) and minprice<=" . ($price - $manjian_money) . ") or ((type=5 or type=6) and minnum<=" . $totalnum . ") ")->where('starttime', '<', time())->where('endtime', '>', time())->order('sort desc')->select()->toArray();
                $newcxlist = [];
                foreach ($cuxiaolist as $k => $v) {
                    $gettj = explode(',', $v['gettj']);
                    /*if (!in_array('-1', $gettj) && !in_array($this->member['levelid'], $gettj)) { //不是所有人
                        continue;
                    }*/
                    if ($v['fwtype'] == 2) {//指定商品可用
                        $productids = explode(',', $v['productids']);
                        if (!array_intersect($proids, $productids)) {
                            continue;
                        }
                    }
                    if ($v['fwtype'] == 1) {//指定类目可用
                        $categoryids = explode(',', $v['categoryids']);
                        $clist = Db::name('restaurant_product_category')->where('pid', 'in', $categoryids)->select()->toArray();
                        foreach ($clist as $kc => $vc) {
                            $categoryids[] = $vc['id'];
                            $cate2 = Db::name('restaurant_product_category')->where('pid', $vc['id'])->find();
                            $categoryids[] = $cate2['id'];
                        }
                        if (!array_intersect($cids, $categoryids)) {
                            continue;
                        }
                    }
                    $newcxlist[] = $v;
                }

                $allbuydata[$bid]['couponList'] = $couponList;
                $allbuydata[$bid]['couponCount'] = count($couponList);
                $allbuydata[$bid]['tea_fee'] = $shop_set['tea_fee_status'] == 1 ? round($shop_set['tea_fee'], 2) : 0;
                $allbuydata[$bid]['tea_fee_text'] = $shop_set['tea_fee_text'];
                $allbuydata[$bid]['coupon_money'] = 0;
                $allbuydata[$bid]['coupontype'] = 1;
                $allbuydata[$bid]['couponrid'] = 0;
                $allbuydata[$bid]['cuxiaolist'] = $newcxlist;
                $allbuydata[$bid]['cuxiaoCount'] = count($newcxlist);
                $allbuydata[$bid]['cuxiao_money'] = 0;
                $allbuydata[$bid]['cuxiaotype'] = 0;
                $allbuydata[$bid]['cuxiaoid'] = 0;
                $allbuydata[$bid]['renshu'] = 1;
            } elseif ($ordertype == 'edit_order') {
                $allbuydata[$bid]['coupon_money'] = 0;
                $allbuydata[$bid]['cuxiao_money'] = 0;
                $allbuydata[$bid]['tea_fee'] = 0;
                $allbuydata[$bid]['manjian_money'] = 0;
            }
            
            $shop_sysset = Db::name('restaurant_shop_sysset')->where('aid',aid)->where('bid',$bid)->find();
        
            $taxes_fee = 0;
            $money = 0;
            $num = $order['renshu']?$order['renshu']:1;
            if($shop_sysset){
                $money = $shop_sysset['server_fee_status']?$shop_sysset['person_num']*$num:(round($product_price,2)*($shop_sysset['order_rado']*0.01));
                $taxes_fee = $shop_sysset['taxes_fee'];
            }
            $taxes = round($product_price,2)*($taxes_fee*0.01);
            $taxes = round($taxes,2);
            $money = round($money,2);
            
            $allbuydata[$bid]['bid'] = $bid;
            $allbuydata[$bid]['business'] = $business;
            $allbuydata[$bid]['prodatastr'] = $prodatastr;
            $allbuydata[$bid]['product_price'] = round($product_price, 2);
            $allbuydata[$bid]['leveldk_money'] = $leveldk_money;
            $allbuydata[$bid]['message'] = '';
            $allbuydata[$bid]['field1'] = '';
            $allbuydata[$bid]['field2'] = '';
            $allbuydata[$bid]['field3'] = '';
            $allbuydata[$bid]['field4'] = '';
            $allbuydata[$bid]['field5'] = '';
            
            $allbuydata[$bid]['server_fee'] = $money;
            $allbuydata[$bid]['taxes'] = $taxes;
            $allbuydata[$bid]['totalprice'] = round($product_price, 2)+$money+$taxes+$allbuydata[$bid]['tea_fee'];


            $allproduct_price += $product_price;
        }

        $rdata = [];
        $rdata['status'] = 1;
        $rdata['linkman'] = $userinfo['realname'] ? strval($userinfo['realname']) : strval($userinfo['nickname']);
        $rdata['tel'] = strval($userinfo['tel']);
        $rdata['userinfo'] = $userinfo;
        $rdata['allbuydata'] = $allbuydata;
        $rdata['needLocation'] = $needLocation;
        $rdata['scorebdkyf'] = Db::name('admin_set')->where('aid', aid)->value('scorebdkyf');
        $rdata['order'] = $order ? $order : [];
        $rdata['ordertype'] = $ordertype;
        $rdata['tableinfo'] = $tableinfo;
        return json($rdata);
    }
    
    public function editOrder()
    {
        if(!session('ADMIN_LOGIN')||!session('ADMIN_UID')){
            return json(['status'=>0,'msg'=>'请登录后操作']);
        }
        define('aid',session('ADMIN_AID'));
        define('mid',session('ADMIN_UID'));
        
        $post = input('post.');
        
        if (empty($post['tableid'])) {
            return json(['status' => 0, 'msg' => '请先扫描桌台二维码或选择桌台']);
        }

        //todo 一个桌子同时只能点一单
        $table = Db::name('restaurant_table')->where('aid', aid)->where('id', $post['tableid'])->find();
        //桌子有订单号时,并且订单内有菜，为加菜
        if ($table['orderid']) {
            $order = Db::name('restaurant_shop_order')->where('aid', aid)->where('id', $table['orderid'])->find();
            $order['goods_count'] = Db::name('restaurant_shop_order_goods')->where('aid', aid)->where('orderid', $order['id'])->count();
        }
        $mid = $order['mid'] ? $order['mid'] : mid;
        $member = Db::name('member')->where('aid', aid)->where('id', $mid)->find();
        $shop_set = Db::name('restaurant_shop_sysset')->where('aid', aid)->where('bid', $order['bid'])->find();

        $ordertype = $order['goods_count'] ? 'edit_order' : 'create_order';//加菜，点菜

        $sysset = Db::name('admin_set')->where('aid', aid)->find();
        $levelid = Db::name('member')->where('aid', aid)->where('id', $mid)->value('levelid');
        $userlevel = Db::name('member_level')->where('aid', aid)->where('id', $levelid)->find();

        $buydata = $post['buydata'];
        $couponridArr = [];
        $userinfo = [];
        $userinfo['discount'] = $userlevel['discount'];
        $userinfo['score'] = $member['score'];
        $userinfo['score2money'] = $sysset['score2money'];
        $userinfo['scoredk_money'] = round($userinfo['score'] * $userinfo['score2money'], 2);
        $userinfo['scoredkmaxpercent'] = $sysset['scoredkmaxpercent'];
        $userinfo['scoremaxtype'] = 0; //0最大百分比 1最大抵扣金额
        $userinfo['realname'] = $member['realname'];
        $userinfo['tel'] = $member['tel'];

        $ordernum = date('ymdHis') . rand(100000, 999999);
        $scoredkmaxmoney = 0;
        $scoremaxtype = 0;
        $i = 0;
        $alltotalprice = 0;
        $alltotalscore = 0;
        foreach ($buydata as $data) {
            $i++;
            $bid = $data['bid'];
            if ($data['prodata']) {
                $prodata = explode('-', $data['prodata']);
            } else {
                return json(['status' => 0, 'msg' => '产品数据错误']);
            }
            if ($bid != 0) {
                $business = Db::name('business')->where('id', $bid)->field('id,aid,cid,name,logo,tel,address,sales,longitude,latitude,start_hours,end_hours')->find();
                if ($business['start_hours'] != $business['end_hours']) {
                    $start_time = strtotime(date('Y-m-d ' . $business['start_hours']));
                    $end_time = strtotime(date('Y-m-d ' . $business['end_hours']));
                    if (($start_time < $end_time && ($start_time > time() || $end_time < time())) || ($start_time >= $end_time && ($start_time > time() && $end_time < time()))) {
                        return json(['status' => 0, 'msg' => '商家不在营业时间']);
                    }
                }
            }
            $shop_set = Db::name('restaurant_shop_sysset')->where('aid', aid)->where('bid', $bid)->find();
            if ($shop_set['status'] == 0) {
                return json(['status' => 0, 'msg' => '该商家未开启点餐']);
            }
            if ($shop_set['start_hours'] != $shop_set['end_hours']) {
                $start_time = strtotime(date('Y-m-d ' . $shop_set['start_hours']));
                $end_time = strtotime(date('Y-m-d ' . $shop_set['end_hours']));
                if (($start_time < $end_time && ($start_time > time() || $end_time < time())) || ($start_time >= $end_time && ($start_time > time() && $end_time < time()))) {
                    return json(['status' => 0, 'msg' => '该商家不在点餐时间']);
                }
            }

            $product_price = 0;
            $needzkproduct_price = 0;
            $givescore = 0; //奖励积分
            $totalweight = 0;//重量
            $totalnum = 0;
            $prolist = [];
            $proids = [];
            $cids = [];

            foreach ($prodata as $key => $pro) {
                $sdata = explode(',', $pro);
                // $sdata[2] = $sdata[2];
                if ($sdata[2] <= 0) return json(['status' => 0, 'msg' => '购买数量有误']);
                $product = Db::name('restaurant_product')->where('aid', aid)->where('ischecked', 1)->where('bid', $bid)->where('id', $sdata[0])->find();
                if (!$product) return json(['status' => 0, 'msg' => '产品不存在或已下架']);

                if ($product['status'] == 0) {
                    return json(['status' => 0, 'msg' => '商品未上架']);
                }
                if ($product['status'] == 2 && (strtotime($product['start_time']) > time() || strtotime($product['end_time']) < time())) {
                    return json(['status' => 0, 'msg' => '商品未上架']);
                }
                if ($product['status'] == 3) {
                    $start_time = strtotime(date('Y-m-d ' . $product['start_hours']));
                    $end_time = strtotime(date('Y-m-d ' . $product['end_hours']));
                    if (($start_time < $end_time && ($start_time > time() || $end_time < time())) || ($start_time >= $end_time && ($start_time > time() && $end_time < time()))) {
                        return json(['status' => 0, 'msg' => '商品未上架']);
                    }
                }

                if ($key == 0) $title = $product['name'];

                $guige = Db::name('restaurant_product_guige')->where('aid', aid)->where('id', $sdata[1])->find();
                if (!$guige) return json(['status' => 0, 'msg' => '产品规格不存在或已下架']);
                if ($guige['stock'] < $sdata[2] || $guige['stock_daily'] - $guige['sales_daily'] < $sdata[2]) {
                    return json(['status' => 0, 'msg' => '库存不足']);
                }

                if ($product['limit_per'] > 0 && $sdata[2] > $product['limit_per']) { //每单限购
                    return json(['status' => 0, 'msg' => $product['name'] . '每单限购' . $product['limit_per'] . '份']);
                }
                if ($product['limit_start'] > 0 && $sdata[2] < $product['limit_start']) { //起售份数
                    return json(['status' => 0, 'msg' => $product['name'] . '最低购买' . $product['limit_start'] . '份']);
                }

                //if ($product['lvprice'] == 1) $guige = $this->formatguige($guige);
                $product_price += $guige['sell_price'] * $sdata[2];
                if ($product['lvprice'] == 0) { //未开启会员价
                    $needzkproduct_price += $guige['sell_price'] * $sdata[2];
                }
                $totalweight += $guige['weight'] * $sdata[2];
                $totalnum += $sdata[2];

                if ($product['scored_set'] == 0) {
                    if ($userinfo['scoredkmaxpercent'] > 0 && $userinfo['scoredkmaxpercent'] < 100) {
                        $scoredkmaxmoney += $userinfo['scoredkmaxpercent'] * 0.01 * $guige['sell_price'] * $sdata[2];
                    } else {
                        $scoredkmaxmoney += $guige['sell_price'] * $sdata[2];
                    }
                } elseif ($product['scored_set'] == 1) {
                    $scoremaxtype = 1;
                    $scoredkmaxmoney += $product['scored_val'] * 0.01 * $guige['sell_price'] * $sdata[2];
                } elseif ($product['scored_set'] == 2) {
                    $scoremaxtype = 1;
                    $scoredkmaxmoney += $product['scored_val'] * $sdata[2];
                } else {
                    $scoremaxtype = 1;
                    $scoredkmaxmoney += 0;
                }

                $prolist[] = ['product' => $product, 'guige' => $guige, 'num' => $sdata[2], 'skrs' => $skrs, 'isSeckill' => $isSeckill];

                $proids[] = $product['id'];
                $cids = array_merge($cids, explode(',', $product['cid']));
                $givescore += $guige['givescore'] * $sdata[2];
            }

//            dd($prolist);
            //会员折扣
            $leveldk_money = 0;
            if ($userlevel && $userlevel['discount'] > 0 && $userlevel['discount'] < 10) {
                $leveldk_money = $needzkproduct_price * (1 - $userlevel['discount'] * 0.1);
            }
            $totalprice = $product_price - $leveldk_money;

            //积分抵扣
            $scoredkscore = 0;
            $scoredk_money = 0;
            
            $shop_sysset = Db::name('restaurant_shop_sysset')->where('aid',aid)->where('bid',$order['bid'])->find();
        
            $taxes_fee = 0;
            $money = 0;
            if($shop_sysset){
                $money = $shop_sysset['server_fee_status']?$shop_sysset['person_num']*$order['renshu']:(round($order['totalprice'],2)*($shop_sysset['order_rado']*0.01));
                $taxes_fee = $shop_sysset['taxes_fee'];
            }
            $taxes = round($order['totalprice'],2)*($taxes_fee*0.01);
            $taxes = round($taxes,2);
            $money = round($money,2);
            
            $orderdata = [];
            $orderdata['totalprice'] = $totalprice + $order['totalprice'];
            $orderdata['product_price'] = $product_price + $order['product_price'];
            $orderdata['leveldk_money'] = $leveldk_money + $order['leveldk_money'];  //会员折扣
            $orderdata['givescore'] = $givescore + $order['givescore'];
            $orderdata['message'] = $data['message'] . ' ' . $order['message'];
            $orderdata['server_fee'] = $money;
            $orderdata['taxes'] = $taxes;
            
            

            $orderid = $order['id'];
            Db::name('restaurant_shop_order')->where('id', $table['orderid'])->update($orderdata);
            $orderdata = Db::name('restaurant_shop_order')->where('id', $table['orderid'])->find();

            $payorderid = \app\model\Payorder::createorder(aid, $orderdata['bid'], $orderdata['mid'], 'restaurant_shop', $orderid, $orderdata['ordernum'], $orderdata['title'], $orderdata['totalprice'], $orderdata['scoredkscore']);

            $alltotalprice += $orderdata['totalprice'];
            $alltotalscore += $orderdata['scoredkscore'];

            $istc1 = 0; //设置了按单固定提成时 只将佣金计算到第一个商品里
            $istc2 = 0;
            $istc3 = 0;
            foreach ($prolist as $key => $v) {
                $product = $v['product'];
                $guige = $v['guige'];
                $num = $v['num'];
                $ogdata = [];
                $ogdata['aid'] = aid;
                $ogdata['bid'] = $product['bid'];
                $ogdata['mid'] = $mid;
                $ogdata['orderid'] = $orderid;
                $ogdata['ordernum'] = $orderdata['ordernum'];
                $ogdata['proid'] = $product['id'];
                $ogdata['name'] = $product['name'];
                $ogdata['pic'] = $product['pic'];
                $ogdata['procode'] = $product['procode'];
                $ogdata['ggid'] = $guige['id'];
                $ogdata['ggname'] = $guige['name'];
                //$ogdata['cid'] = $product['cid'];
                $ogdata['num'] = $num;
                $ogdata['cost_price'] = $guige['cost_price'];
                $ogdata['sell_price'] = $guige['sell_price'];
                $ogdata['totalprice'] = $num * $guige['sell_price'];
                $ogdata['status'] = 0;
                $ogdata['createtime'] = time();

                $og_totalprice = $ogdata['totalprice'];

                //计算商品实际金额  商品金额 - 会员折扣 - 积分抵扣 - 满减抵扣 - 优惠券抵扣
                if ($sysset['fxjiesuantype'] == 1 || $sysset['fxjiesuantype'] == 2) {
                    $allproduct_price = $product_price;
                    $og_leveldk_money = 0;
                    $og_coupon_money = 0;
                    $og_scoredk_money = 0;
                    $og_manjian_money = 0;
                    if ($allproduct_price > 0 && $og_totalprice > 0) {
                        if ($leveldk_money) {
                            $og_leveldk_money = $og_totalprice / $allproduct_price * $leveldk_money;
                        }
                        if ($coupon_money) {
                            $og_coupon_money = $og_totalprice / $allproduct_price * $coupon_money;
                        }
                        if ($scoredk_money) {
                            $og_scoredk_money = $og_totalprice / $allproduct_price * $scoredk_money;
                        }
                        if ($manjian_money) {
                            $og_manjian_money = $og_totalprice / $allproduct_price * $manjian_money;
                        }
                    }
                    $og_totalprice = round($og_totalprice - $og_coupon_money - $og_scoredk_money - $og_manjian_money, 2);
                    if ($og_totalprice < 0) $og_totalprice = 0;
                }
                $ogdata['real_totalprice'] = $og_totalprice; //实际商品销售金额

                //计算佣金的商品金额
                $commission_totalprice = $ogdata['totalprice'];
                if ($sysset['fxjiesuantype'] == 1) { //按成交价格
                    $commission_totalprice = $ogdata['real_totalprice'];
                }
                if ($sysset['fxjiesuantype'] == 2) { //按利润提成
                    $commission_totalprice = $ogdata['real_totalprice'] - $guige['cost_price'] * $num;
                }
                if ($commission_totalprice < 0) $commission_totalprice = 0;

                $pid = $member['pid'];
                $agleveldata = Db::name('member_level')->where('aid', aid)->where('id', $levelid)->find();
                if ($agleveldata['can_agent'] > 0 && $agleveldata['commission1own'] == 1) {
                    $pid = $mid;
                }
                if ($product['commissionset'] != -1) {
                    if ($pid) {
                        $parent1 = Db::name('member')->where('aid', aid)->where('id', $pid)->find();
                        if ($parent1) {
                            $agleveldata1 = Db::name('member_level')->where('aid', aid)->where('id', $parent1['levelid'])->find();
                            if ($agleveldata1['can_agent'] != 0) {
                                $ogdata['parent1'] = $parent1['id'];
                            }
                        }
                    }
                    if ($parent1['pid']) {
                        $parent2 = Db::name('member')->where('aid', aid)->where('id', $parent1['pid'])->find();
                        if ($parent2) {
                            $agleveldata2 = Db::name('member_level')->where('aid', aid)->where('id', $parent2['levelid'])->find();
                            if ($agleveldata2['can_agent'] > 1) {
                                $ogdata['parent2'] = $parent2['id'];
                            }
                        }
                    }
                    if ($parent2['pid']) {
                        $parent3 = Db::name('member')->where('aid', aid)->where('id', $parent2['pid'])->find();
                        if ($parent3) {
                            $agleveldata3 = Db::name('member_level')->where('aid', aid)->where('id', $parent3['levelid'])->find();
                            if ($agleveldata3['can_agent'] > 2) {
                                $ogdata['parent3'] = $parent3['id'];
                            }
                        }
                    }
                    if ($product['commissionset'] == 1) {//按商品设置的分销比例
                        $commissiondata = json_decode($product['commissiondata1'], true);
                        if ($commissiondata) {
                            if ($agleveldata1) $ogdata['parent1commission'] = $commissiondata[$agleveldata1['id']]['commission1'] * $commission_totalprice * 0.01;
                            if ($agleveldata2) $ogdata['parent2commission'] = $commissiondata[$agleveldata2['id']]['commission2'] * $commission_totalprice * 0.01;
                            if ($agleveldata3) $ogdata['parent3commission'] = $commissiondata[$agleveldata3['id']]['commission3'] * $commission_totalprice * 0.01;
                        }
                    } elseif ($product['commissionset'] == 2) {//按固定金额
                        $commissiondata = json_decode($product['commissiondata2'], true);
                        if ($commissiondata) {
                            if ($agleveldata1) $ogdata['parent1commission'] = $commissiondata[$agleveldata1['id']]['commission1'] * $num;
                            if ($agleveldata2) $ogdata['parent2commission'] = $commissiondata[$agleveldata2['id']]['commission2'] * $num;
                            if ($agleveldata3) $ogdata['parent3commission'] = $commissiondata[$agleveldata3['id']]['commission3'] * $num;
                        }
                    } elseif ($product['commissionset'] == 3) {//提成是积分
                        $commissiondata = json_decode($product['commissiondata3'], true);
                        if ($commissiondata) {
                            if ($agleveldata1) $ogdata['parent1score'] = $commissiondata[$agleveldata1['id']]['commission1'] * $num;
                            if ($agleveldata2) $ogdata['parent2score'] = $commissiondata[$agleveldata2['id']]['commission2'] * $num;
                            if ($agleveldata3) $ogdata['parent3score'] = $commissiondata[$agleveldata3['id']]['commission3'] * $num;
                        }
                    } else { //按会员等级设置的分销比例
                        if ($agleveldata1) {
                            if ($agleveldata1['commissiontype'] == 1) { //固定金额按单
                                if ($istc1 == 0) {
                                    $ogdata['parent1commission'] = $agleveldata1['commission1'];
                                    $istc1 = 1;
                                }
                            } else {
                                $ogdata['parent1commission'] = $agleveldata1['commission1'] * $commission_totalprice * 0.01;
                            }
                        }
                        if ($agleveldata2) {
                            if ($agleveldata2['commissiontype'] == 1) {
                                if ($istc2 == 0) {
                                    $ogdata['parent2commission'] = $agleveldata2['commission2'];
                                    $istc2 = 1;
                                }
                            } else {
                                $ogdata['parent2commission'] = $agleveldata2['commission2'] * $commission_totalprice * 0.01;
                            }
                        }
                        if ($agleveldata3) {
                            if ($agleveldata3['commissiontype'] == 1) {
                                if ($istc3 == 0) {
                                    $ogdata['parent3commission'] = $agleveldata3['commission3'];
                                    $istc3 = 1;
                                }
                            } else {
                                $ogdata['parent3commission'] = $agleveldata3['commission3'] * $commission_totalprice * 0.01;
                            }
                        }
                    }
                }


                $ogid = Db::name('restaurant_shop_order_goods')->insertGetId($ogdata);
                $ogids[] = $ogid;
                if ($ogdata['parent1'] && ($ogdata['parent1commission'] > 0 || $ogdata['parent1score'] > 0)) {
                    Db::name('member_commission_record')->insert(['aid' => aid, 'mid' => $ogdata['parent1'], 'frommid' => $mid, 'orderid' => $orderid, 'ogid' => $ogid, 'type' => 'restaurant_shop', 'commission' => $ogdata['parent1commission'], 'score' => $ogdata['parent1score'], 'remark' => '下级购买菜品奖励', 'createtime' => time()]);
                }
                if ($ogdata['parent2'] && ($ogdata['parent2commission'] || $ogdata['parent2score'])) {
                    Db::name('member_commission_record')->insert(['aid' => aid, 'mid' => $ogdata['parent2'], 'frommid' => $mid, 'orderid' => $orderid, 'ogid' => $ogid, 'type' => 'restaurant_shop', 'commission' => $ogdata['parent2commission'], 'score' => $ogdata['parent2score'], 'remark' => '下二级购买菜品奖励', 'createtime' => time()]);
                }
                if ($ogdata['parent3'] && ($ogdata['parent3commission'] || $ogdata['parent3score'])) {
                    Db::name('member_commission_record')->insert(['aid' => aid, 'mid' => $ogdata['parent3'], 'frommid' => $mid, 'orderid' => $orderid, 'ogid' => $ogid, 'type' => 'restaurant_shop', 'commission' => $ogdata['parent3commission'], 'score' => $ogdata['parent3score'], 'remark' => '下三级购买菜品奖励', 'createtime' => time()]);
                }

                //删除购物车
                Db::name('restaurant_shop_cart')->where('aid', aid)->where('mid', mid)->where('ggid', $guige['id'])->where('proid', $product['id'])->delete();
                Db::name('restaurant_product_guige')->where('aid', aid)->where('id', $guige['id'])->update(['stock' => Db::raw("stock-$num"), 'sales' => Db::raw("sales+$num"), 'sales_daily' => Db::raw("sales_daily+$num")]);
                Db::name('restaurant_product')->where('aid', aid)->where('id', $product['id'])->update(['stock' => Db::raw("stock-$num"), 'sales' => Db::raw("sales+$num"), 'sales_daily' => Db::raw("sales_daily+$num")]);
            }

            //根据餐后付款设置，开启时下单后打印小票，关闭时付款后打印小票
            $restaurant_shop_sysset = Db::name('restaurant_shop_sysset')->where('aid', aid)->where('bid', $data['bid'])->find();
            if ($restaurant_shop_sysset['pay_after'] == 1) {
                //仅打印加菜
                $orderGoods = Db::name('restaurant_shop_order_goods')->alias('og')->where('orderid', $orderid)->where('og.id', 'in', $ogids)->leftJoin('restaurant_product p', 'p.id=og.proid')
                    ->fieldRaw('og.*,p.area_id')->select()->toArray();
                \app\custom\Restaurant::print('restaurant_shop', $orderdata, $orderGoods);
            }

        }
        if (count($buydata) > 1) { //创建合并支付单
            $payorderid = \app\model\Payorder::createorder(aid, 0, $mid, 'restaurant_shop_hb', $orderid, $ordernum, $orderdata['title'], $alltotalprice, $alltotalscore);
        }

        return json(['status' => 1, 'payorderid' => $payorderid, 'msg' => '提交成功', 'pay_after' => $shop_set['pay_after']]);
    }
    
    public function createOrder()
    {
        if(!session('ADMIN_LOGIN')||!session('ADMIN_UID')){
            return json(['status'=>0,'msg'=>'请登录后操作']);
        }
        define('aid',session('ADMIN_AID'));
        define('mid',session('ADMIN_UID'));
        $post = input('post.');
        /*if ($post['frompage'] != 'admin') {
            //非管理员点餐验证登录
            $this->checklogin();
        }*/
        if (empty($post['tableid'])) {
            return json_encode(['status' => 0, 'msg' => '请先扫描桌台二维码或选择桌台']);
        }

        //todo 一个桌子同时只能点一单
        $table = Db::name('restaurant_table')->where('aid', aid)->where('id', $post['tableid'])->find();
     
        if ($table) {
//            if($table['orderid']) {
//                //todo 修改菜品
//                return json(['status'=>0,'msg'=>'当前桌台已存在订单，请联系服务员']);
//            }
            $order = Db::name('restaurant_shop_order')->where('aid', aid)->where('id', $table['orderid'])->find();
//            if($table['status'] != 0 && $order['status'] == 0 && $post['frompage'] != 'admin') {
//                return json(['status'=>0,'msg'=>'当前桌台状态不可下单，请联系服务员']);
//            }
        }

        $sysset = Db::name('admin_set')->where('aid', aid)->find();
        $userlevel = Db::name('member_level')->where('aid', aid)->where('id', member['levelid'])->find();

        $buydata = $post['buydata'];

        $shop_sysset = Db::name('restaurant_shop_sysset')->where('aid', aid)->where('bid', $buydata[0]['bid'])->find();

        $couponridArr = [];
        foreach ($buydata as $data) { //判断有没有重复选择的优惠券
            if ($data['couponrid'] && in_array($data['couponrid'], $couponridArr)) {
                return json(['status' => 0, 'msg' => t('优惠券') . '不可重复使用']);
            } elseif ($data['couponrid']) {
                $couponridArr[] = $data['couponrid'];
            }
        }

        $ordernum = date('ymdHis') . rand(100000, 999999);
        $scoredkmaxmoney = 0;
        $scoremaxtype = 0;
        $i = 0;
        $alltotalprice = 0;
        $alltotalscore = 0;
        foreach ($buydata as $data) {
            $i++;
            $bid = $data['bid'];
            if ($data['prodata']) {
                $prodata = explode('-', $data['prodata']);
            } else {
                return json(['status' => 0, 'msg' => '产品数据错误']);
            }
            if ($bid != 0) {
                $business = Db::name('business')->where('id', $bid)->field('id,aid,cid,name,logo,tel,address,sales,longitude,latitude,start_hours,end_hours')->find();
                if ($business['start_hours'] != $business['end_hours']) {
                    $start_time = strtotime(date('Y-m-d ' . $business['start_hours']));
                    $end_time = strtotime(date('Y-m-d ' . $business['end_hours']));
                    if (($start_time < $end_time && ($start_time > time() || $end_time < time())) || ($start_time >= $end_time && ($start_time > time() && $end_time < time()))) {
                        return json(['status' => 0, 'msg' => '商家不在营业时间']);
                    }
                }
            }
            $shop_set = Db::name('restaurant_shop_sysset')->where('aid', aid)->where('bid', $bid)->find();
            if ($shop_set['status'] == 0) {
                return json(['status' => 0, 'msg' => '该商家未开启点餐']);
            }
            if ($shop_set['start_hours'] != $shop_set['end_hours']) {
                $start_time = strtotime(date('Y-m-d ' . $shop_set['start_hours']));
                $end_time = strtotime(date('Y-m-d ' . $shop_set['end_hours']));
                if (($start_time < $end_time && ($start_time > time() || $end_time < time())) || ($start_time >= $end_time && ($start_time > time() && $end_time < time()))) {
                    return json(['status' => 0, 'msg' => '该商家不在点餐时间']);
                }
            }

            $product_price = 0;
            $needzkproduct_price = 0;
            $givescore = 0; //奖励积分
            $totalweight = 0;//重量
            $totalnum = 0;
            $prolist = [];
            $proids = [];
            $cids = [];

            foreach ($prodata as $key => $pro) {
                $sdata = explode(',', $pro);
                $sdata[2] = intval($sdata[2]);
                if ($sdata[2] <= 0) return json(['status' => 0, 'msg' => '购买数量有误']);
                $product = Db::name('restaurant_product')->where('aid', aid)->where('ischecked', 1)->where('bid', $bid)->where('id', $sdata[0])->find();
                if (!$product) return json(['status' => 0, 'msg' => '产品不存在或已下架']);

                if ($product['status'] == 0) {
                    return json(['status' => 0, 'msg' => '商品未上架']);
                }
                if ($product['status'] == 2 && (strtotime($product['start_time']) > time() || strtotime($product['end_time']) < time())) {
                    return json(['status' => 0, 'msg' => '商品未上架']);
                }
                if ($product['status'] == 3) {
                    $start_time = strtotime(date('Y-m-d ' . $product['start_hours']));
                    $end_time = strtotime(date('Y-m-d ' . $product['end_hours']));
                    if (($start_time < $end_time && ($start_time > time() || $end_time < time())) || ($start_time >= $end_time && ($start_time > time() && $end_time < time()))) {
                        return json(['status' => 0, 'msg' => '商品未上架']);
                    }
                }

                if ($key == 0) $title = $product['name'];

                $guige = Db::name('restaurant_product_guige')->where('aid', aid)->where('id', $sdata[1])->find();
                if (!$guige) return json(['status' => 0, 'msg' => '产品规格不存在或已下架']);
                if ($guige['stock'] < $sdata[2] || $guige['stock_daily'] - $guige['sales_daily'] < $sdata[2]) {
                    return json(['status' => 0, 'msg' => '库存不足']);
                }
                //$gettj = explode(',',$product['gettj']);
                //if(!in_array('-1',$gettj) && !in_array($this->member['levelid'],$gettj) && (!in_array('0',$gettj) || $this->member['subscribe']!=1)){ //不是所有人
                //	if(!$product['gettjtip']) $product['gettjtip'] = '没有权限购买该商品';
                //	return json(['status'=>0,'msg'=>$product['gettjtip'],'url'=>$product['gettjurl']]);
                //}

                if ($product['limit_per'] > 0 && $sdata[2] > $product['limit_per']) { //每单限购
                    return json(['status' => 0, 'msg' => $product['name'] . '每单限购' . $product['limit_per'] . '份']);
                }
                if ($product['limit_start'] > 0 && $sdata[2] < $product['limit_start']) { //起售份数
                    return json(['status' => 0, 'msg' => $product['name'] . '最低购买' . $product['limit_start'] . '份']);
                }

                //if ($product['lvprice'] == 1) $guige = $this->formatguige($guige);
                $product_price += $guige['sell_price'] * $sdata[2];
                if ($product['lvprice'] == 0) { //未开启会员价
                    $needzkproduct_price += $guige['sell_price'] * $sdata[2];
                }
                $totalweight += $guige['weight'] * $sdata[2];
                $totalnum += $sdata[2];

                if ($product['scored_set'] == 0) {
                    if ($userinfo['scoredkmaxpercent'] > 0 && $userinfo['scoredkmaxpercent'] < 100) {
                        $scoredkmaxmoney += $userinfo['scoredkmaxpercent'] * 0.01 * $guige['sell_price'] * $sdata[2];
                    } else {
                        $scoredkmaxmoney += $guige['sell_price'] * $sdata[2];
                    }
                } elseif ($product['scored_set'] == 1) {
                    $scoremaxtype = 1;
                    $scoredkmaxmoney += $product['scored_val'] * 0.01 * $guige['sell_price'] * $sdata[2];
                } elseif ($product['scored_set'] == 2) {
                    $scoremaxtype = 1;
                    $scoredkmaxmoney += $product['scored_val'] * $sdata[2];
                } else {
                    $scoremaxtype = 1;
                    $scoredkmaxmoney += 0;
                }

                $prolist[] = ['product' => $product, 'guige' => $guige, 'num' => $sdata[2], 'skrs' => $skrs, 'isSeckill' => $isSeckill];

                $proids[] = $product['id'];
                $cids = array_merge($cids, explode(',', $product['cid']));
                $givescore += $guige['givescore'] * $sdata[2];
            }
            //会员折扣
            $leveldk_money = 0;
            if ($userlevel && $userlevel['discount'] > 0 && $userlevel['discount'] < 10) {
                $leveldk_money = $needzkproduct_price * (1 - $userlevel['discount'] * 0.1);
            }
            $totalprice = $product_price - $leveldk_money;

            //满减活动
            $mjset = Db::name('manjian_set')->where('aid', aid)->find();
            if ($mjset && $mjset['status'] == 1) {
                $mjdata = json_decode($mjset['mjdata'], true);
            } else {
                $mjdata = array();
            }
            $manjian_money = 0;
            $moneyduan = 0;
            if ($mjdata) {
                foreach ($mjdata as $give) {
                    if ($totalprice * 1 >= $give['money'] * 1 && $give['money'] * 1 > $moneyduan) {
                        $moneyduan = $give['money'] * 1;
                        $manjian_money = $give['jian'] * 1;
                    }
                }
            }
            if ($manjian_money <= 0) $manjian_money = 0;
            $totalprice = $totalprice - $manjian_money;
            if ($totalprice < 0) $totalprice = 0;

            //优惠券
            if ($data['couponrid'] > 0) {
                $couponrid = $data['couponrid'];
                $couponrecord = Db::name('coupon_record')->where("bid=-1 or bid=" . $data['bid'])->where('aid', aid)->where('mid', mid)->where('id', $couponrid)->find();
                if (!$couponrecord) {
                    return json(['status' => 0, 'msg' => '该' . t('优惠券') . '不存在']);
                } elseif ($couponrecord['status'] != 0) {
                    return json(['status' => 0, 'msg' => '该' . t('优惠券') . '已使用过了']);
                } elseif ($couponrecord['starttime'] > time()) {
                    return json(['status' => 0, 'msg' => '该' . t('优惠券') . '尚未开始使用']);
                } elseif ($couponrecord['endtime'] < time()) {
                    return json(['status' => 0, 'msg' => '该' . t('优惠券') . '已过期']);
                } elseif ($couponrecord['minprice'] > $totalprice) {
                    return json(['status' => 0, 'msg' => '该' . t('优惠券') . '不符合条件']);
                } elseif ($couponrecord['type'] != 1 && $couponrecord['type'] != 4 && $couponrecord['type'] != 5) {
                    return json(['status' => 0, 'msg' => '该' . t('优惠券') . '不符合条件']);
                }

                $couponinfo = Db::name('coupon')->where('aid', aid)->where('id', $couponrecord['couponid'])->find();
                if ($couponinfo['fwtype'] == 2) {//指定商品可用
                    $productids = explode(',', $couponinfo['productids']);
                    if (!array_intersect($proids, $productids)) {
                        return json(['status' => 0, 'msg' => '该' . t('优惠券') . '指定商品可用']);
                    }
                }
                if ($couponinfo['fwtype'] == 1) {//指定类目可用
                    $categoryids = explode(',', $couponinfo['categoryids']);
                    $clist = Db::name('restaurant_product_category')->where('pid', 'in', $categoryids)->select()->toArray();
                    foreach ($clist as $kc => $vc) {
                        $categoryids[] = $vc['id'];
                        $cate2 = Db::name('restaurant_product_category')->where('pid', $vc['id'])->find();
                        $categoryids[] = $cate2['id'];
                    }
                    if (!array_intersect($cids, $categoryids)) {
                        return json(['status' => 0, 'msg' => '该' . t('优惠券') . '指定分类可用']);
                    }
                }

                Db::name('coupon_record')->where('id', $couponrid)->update(['status' => 1, 'usetime' => time()]);
                if ($couponrecord['type'] == 4) {//运费抵扣券
                    $coupon_money = 0;
                } else {
                    $coupon_money = $couponrecord['money'];
                    if ($coupon_money > $totalprice) $coupon_money = $totalprice;
                }
            } else {
                $coupon_money = 0;
            }
            //促销活动
            if ($data['cuxiaoid'] > 0) {
                $cuxiaoid = $data['cuxiaoid'];
                $cuxiaoinfo = Db::name('restaurant_cuxiao')->where("bid=-1 or bid=" . $data['bid'])->where('aid', aid)->where('id', $cuxiaoid)->find();
                if (!$cuxiaoinfo) {
                    return json(['status' => 0, 'msg' => '该促销活动不存在']);
                } elseif ($cuxiaoinfo['starttime'] > time()) {
                    return json(['status' => 0, 'msg' => '该促销活动尚未开始']);
                } elseif ($cuxiaoinfo['endtime'] < time()) {
                    return json(['status' => 0, 'msg' => '该促销活动已结束']);
                } elseif ($cuxiaoinfo['type'] != 5 && $cuxiaoinfo['type'] != 6 && $cuxiaoinfo['minprice'] > $totalprice) {
                    return json(['status' => 0, 'msg' => '该促销活动不符合条件']);
                } elseif (($cuxiaoinfo['type'] == 5 || $cuxiaoinfo['type'] == 6) && $cuxiaoinfo['minnum'] > $totalnum) {
                    return json(['status' => 0, 'msg' => '该促销活动不符合条件']);
                }
                if ($cuxiaoinfo['fwtype'] == 2) {//指定商品可用
                    $productids = explode(',', $cuxiaoinfo['productids']);
                    if (!array_intersect($proids, $productids)) {
                        return json(['status' => 0, 'msg' => '该促销活动指定商品可用']);
                    }
                }
                if ($cuxiaoinfo['fwtype'] == 1) {//指定类目可用
                    $categoryids = explode(',', $cuxiaoinfo['categoryids']);
                    $clist = Db::name('restaurant_product_category')->where('pid', 'in', $categoryids)->select()->toArray();
                    foreach ($clist as $kc => $vc) {
                        $categoryids[] = $vc['id'];
                        $cate2 = Db::name('restaurant_product_category')->where('pid', $vc['id'])->find();
                        $categoryids[] = $cate2['id'];
                    }
                    if (!array_intersect($cids, $categoryids)) {
                        return json(['status' => 0, 'msg' => '该促销活动指定分类可用']);
                    }
                }
                if ($cuxiaoinfo['type'] == 1 || $cuxiaoinfo['type'] == 6) {//满额立减 满件立减
                    $manjian_money = $manjian_money + $cuxiaoinfo['money'];
                    $cuxiaomoney = $cuxiaoinfo['money'] * -1;
                } elseif ($cuxiaoinfo['type'] == 2) {//满额赠送
                    $cuxiaomoney = 0;
                    $product = Db::name('restaurant_product')->where('aid', aid)->where('id', $cuxiaoinfo['proid'])->find();
                    $guige = Db::name('restaurant_product_guige')->where('aid', aid)->where('id', $cuxiaoinfo['ggid'])->find();
                    if (!$product) return json(['status' => 0, 'msg' => '赠送产品不存在']);
                    if (!$guige) return json(['status' => 0, 'msg' => '赠送产品规格不存在']);
                    if ($guige['stock'] < 1) {
                        return json(['status' => 0, 'msg' => '赠送产品库存不足']);
                    }
                    $prolist[] = ['product' => $product, 'guige' => $guige, 'num' => 1, 'isSeckill' => 0];
                } elseif ($cuxiaoinfo['type'] == 3) {//加价换购
                    $cuxiaomoney = $cuxiaoinfo['money'];
                    $product = Db::name('restaurant_product')->where('aid', aid)->where('id', $cuxiaoinfo['proid'])->find();
                    $guige = Db::name('restaurant_product_guige')->where('aid', aid)->where('id', $cuxiaoinfo['ggid'])->find();
                    if (!$product) return json(['status' => 0, 'msg' => '换购产品不存在']);
                    if (!$guige) return json(['status' => 0, 'msg' => '换购产品规格不存在']);
                    if ($guige['stock'] < 1) {
                        return json(['status' => 0, 'msg' => '换购产品库存不足']);
                    }
                    $prolist[] = ['product' => $product, 'guige' => $guige, 'num' => 1, 'isSeckill' => 0];
                } elseif ($cuxiaoinfo['type'] == 4 || $cuxiaoinfo['type'] == 5) {//满额打折 满件打折
                    $cuxiaomoney = $totalprice * (1 - $cuxiaoinfo['zhekou'] * 0.1);
                    $manjian_money = $manjian_money + $cuxiaomoney;
                    $cuxiaomoney = $cuxiaomoney * -1;
                } else {
                    $cuxiaomoney = 0;
                }
            } else {
                $cuxiaomoney = 0;
            }
            $totalprice = $totalprice - $coupon_money + $cuxiaomoney;
            $tea_fee = ($shop_set['tea_fee_status'] == 1 && $shop_set['tea_fee'] > 0 ? $shop_set['tea_fee'] * $data['renshu'] : 0);
            //$totalprice = $totalprice + $tea_fee;

            //积分抵扣
            $scoredkscore = 0;
            $scoredk_money = 0;
            if ($post['usescore'] == 1) {
                $adminset = Db::name('admin_set')->where('aid', aid)->find();
                $score2money = $adminset['score2money'];
                $scoredkmaxpercent = $adminset['scoredkmaxpercent'];
                $scorebdkyf = $adminset['scorebdkyf'];
                //$scoredk_money = $this->member['score'] * $score2money;
                if ($scorebdkyf == 1) {//积分不抵扣运费
                    if ($scoredk_money > $totalprice - $freight_price) $scoredk_money = $totalprice - $freight_price;
                } else {
                    if ($scoredk_money > $totalprice) $scoredk_money = $totalprice;
                }
                if ($scoremaxtype == 0) {
                    if ($scoredkmaxpercent > 0 && $scoredkmaxpercent < 100 && $scoredk_money > 0 && $scoredk_money > $totalprice * $scoredkmaxpercent * 0.01) {
                        $scoredk_money = $totalprice * $scoredkmaxpercent * 0.01;
                    }
                } else {
                    if ($scoredk_money > $scoredkmaxmoney) $scoredk_money = $scoredkmaxmoney;
                }
                $totalprice = $totalprice - $scoredk_money;
                $totalprice = round($totalprice * 100) / 100;
                if ($scoredk_money > 0) {
                    $scoredkscore = intval($scoredk_money / $score2money);
                }
            }

            $orderdata = [];
//			dump($table);
//            if(!$table['orderid']) {
            $orderdata['aid'] = aid;
            $orderdata['mid'] = mid;
            $orderdata['bid'] = $data['bid'];
            $orderdata['tableid'] = input('param.tableid');
//            }
            if (count($buydata) > 1) {
                $orderdata['ordernum'] = $ordernum . '_' . $i;
            } else {
                $orderdata['ordernum'] = $ordernum;
            }
            $orderdata['title'] = $title . (count($prodata) > 1 ? '等' : '');

//			$orderdata['linkman'] = $address['name'];
//			$orderdata['tel'] = $address['tel'];
//			$orderdata['area'] = $address['area'];
//			$orderdata['address'] = $address['address'];
//			$orderdata['longitude'] = $address['longitude'];
//			$orderdata['latitude'] = $address['latitude'];
//			$orderdata['area2'] = $address['province'].','.$address['city'].','.$address['district'];
            $orderdata['totalprice'] = $totalprice;
            $orderdata['product_price'] = $product_price;
            $orderdata['renshu'] = $data['renshu'];
            $orderdata['tea_fee'] = $tea_fee;
            $orderdata['leveldk_money'] = $leveldk_money;  //会员折扣
            $orderdata['manjian_money'] = $manjian_money;    //满减活动
            $orderdata['scoredk_money'] = $scoredk_money;    //积分抵扣
            $orderdata['coupon_money'] = $coupon_money;        //优惠券抵扣
            $orderdata['scoredkscore'] = $scoredkscore;    //抵扣掉的积分
            $orderdata['coupon_rid'] = $couponrid;
            $orderdata['freight_price'] = $freight_price; //运费
            $orderdata['givescore'] = $givescore;
            $orderdata['message'] = $data['message'];

            $orderdata['createtime'] = time();
            $orderdata['platform'] = platform;
            $orderdata['hexiao_code'] = random(16);
            $orderdata['hexiao_qr'] = createqrcode(m_url('admin/hexiao/hexiao?type=restaurant_shop&co=' . $orderdata['hexiao_code']));
            $orderdata['field1'] = $data['field1'];
            $orderdata['field2'] = $data['field2'];
            $orderdata['field3'] = $data['field3'];
            $orderdata['field4'] = $data['field4'];
            $orderdata['field5'] = $data['field5'];
            
            $shop_sysset = Db::name('restaurant_shop_sysset')->where('aid',aid)->where('bid',$data['bid'])->find();
            
            $taxes_fee = 0;
            $money = 0;
            if($shop_sysset){
                $money = $shop_sysset['server_fee_status']?$shop_sysset['person_num']*$data['renshu']:(round($totalprice,2)*($shop_sysset['order_rado']*0.01));
                $taxes_fee = $shop_sysset['taxes_fee'];
            }
            $taxes = round($totalprice,2)*($taxes_fee*0.01);
            $taxes = round($taxes,2);
            $money = round($money,2);
            
            $orderdata['server_fee'] = $money;
            $orderdata['taxes'] = $taxes;
           // $orderdata['totalprice'] = $totalprice+$money+$taxes;
			
            if ($table['orderid'] && ($order['status'] == 0 || $post['frompage'] == 'admin')) {
                Db::name('restaurant_shop_order')->where('id', $table['orderid'])->update($orderdata);
                $orderid = $table['orderid'];
            } else {
                $orderid = Db::name('restaurant_shop_order')->insertGetId($orderdata);
            }
            $orderdata = Db::name('restaurant_shop_order')->where('id', $orderid)->find();

            $payorderid = \app\model\Payorder::createorder(aid, $orderdata['bid'], $orderdata['mid'], 'restaurant_shop', $orderid, $orderdata['ordernum'], $orderdata['title'], $orderdata['totalprice'], $orderdata['scoredkscore']);
            //更新餐桌状态
            Db::name('restaurant_table')->where('aid', aid)->where('id', $table['id'])->update(['status' => 2, 'orderid' => $orderid]);

            $alltotalprice += $orderdata['totalprice'];
            $alltotalscore += $orderdata['scoredkscore'];

            $istc1 = 0; //设置了按单固定提成时 只将佣金计算到第一个商品里
            $istc2 = 0;
            $istc3 = 0;
            foreach ($prolist as $key => $v) {
                $product = $v['product'];
                $guige = $v['guige'];
                $num = $v['num'];
                $ogdata = [];
                $ogdata['aid'] = aid;
                $ogdata['bid'] = $product['bid'];
                $ogdata['mid'] = mid;
                $ogdata['orderid'] = $orderid;
                $ogdata['ordernum'] = $orderdata['ordernum'];
                $ogdata['proid'] = $product['id'];
                $ogdata['name'] = $product['name'];
                $ogdata['pic'] = $product['pic'];
                $ogdata['procode'] = $product['procode'];
                $ogdata['ggid'] = $guige['id'];
                $ogdata['ggname'] = $guige['name'];
                //$ogdata['cid'] = $product['cid'];
                $ogdata['num'] = $num;
                $ogdata['cost_price'] = $guige['cost_price'];
                $ogdata['sell_price'] = $guige['sell_price'];
                $ogdata['totalprice'] = $num * $guige['sell_price'];
                $ogdata['status'] = 0;
                $ogdata['createtime'] = time();

                $og_totalprice = $ogdata['totalprice'];

                //计算商品实际金额  商品金额 - 会员折扣 - 积分抵扣 - 满减抵扣 - 优惠券抵扣
                if ($sysset['fxjiesuantype'] == 1 || $sysset['fxjiesuantype'] == 2) {
                    $allproduct_price = $product_price;
                    $og_leveldk_money = 0;
                    $og_coupon_money = 0;
                    $og_scoredk_money = 0;
                    $og_manjian_money = 0;
                    if ($allproduct_price > 0 && $og_totalprice > 0) {
                        if ($leveldk_money) {
                            $og_leveldk_money = $og_totalprice / $allproduct_price * $leveldk_money;
                        }
                        if ($coupon_money) {
                            $og_coupon_money = $og_totalprice / $allproduct_price * $coupon_money;
                        }
                        if ($scoredk_money) {
                            $og_scoredk_money = $og_totalprice / $allproduct_price * $scoredk_money;
                        }
                        if ($manjian_money) {
                            $og_manjian_money = $og_totalprice / $allproduct_price * $manjian_money;
                        }
                    }
                    $og_totalprice = round($og_totalprice - $og_leveldk_money - $og_coupon_money - $og_scoredk_money - $og_manjian_money, 2);
                    if ($og_totalprice < 0) $og_totalprice = 0;
                }
                $ogdata['real_totalprice'] = $og_totalprice; //实际商品销售金额

                //计算佣金的商品金额
                $commission_totalprice = $ogdata['totalprice'];
                if ($sysset['fxjiesuantype'] == 1) { //按成交价格
                    $commission_totalprice = $ogdata['real_totalprice'];
                    if ($commission_totalprice < 0) $commission_totalprice = 0;
                }
                if ($sysset['fxjiesuantype'] == 2) { //按利润提成
                    $commission_totalprice = $ogdata['real_totalprice'] - $guige['cost_price'] * $num;
                    if ($commission_totalprice < 0) $commission_totalprice = 0;
                }
                if ($commission_totalprice < 0) $commission_totalprice = 0;

                /*$agleveldata = Db::name('member_level')->where('aid', aid)->where('id', $this->member['levelid'])->find();
                if ($agleveldata['can_agent'] > 0 && $agleveldata['commission1own'] == 1) {
                    $this->member['pid'] = mid;
                }*/
                if ($product['commissionset'] != -1) {
                    /*if ($this->member['pid']) {
                        $parent1 = \app\custom\Restaurant::getParentWithLevel(aid, $this->member['pid']);
                        if ($parent1 && $parent1['levelData']['can_agent'] != 0) {
                            $ogdata['parent1'] = $parent1['id'];
                        }
                    }*/
                    if ($parent1['pid']) {
                        $parent2 = \app\custom\Restaurant::getParentWithLevel(aid, $parent1['pid']);
                        if ($parent2 && $parent2['levelData']['can_agent'] > 1) {
                            $ogdata['parent2'] = $parent2['id'];
                        }
                    }
                    if ($parent2['pid']) {
                        $parent3 = \app\custom\Restaurant::getParentWithLevel(aid, $parent2['pid']);
                        if ($parent3 && $parent3['levelData']['can_agent'] > 2) {
                            $ogdata['parent3'] = $parent3['id'];
                        }
                    }
                    if ($product['commissionset'] == 1) {//按商品设置的分销比例
                        $commissiondata = json_decode($product['commissiondata1'], true);
                        if ($commissiondata) {
                            if ($ogdata['parent1']) $ogdata['parent1commission'] = $commissiondata[$parent1['levelData']['id']]['commission1'] * $commission_totalprice * 0.01;
                            if ($ogdata['parent2']) $ogdata['parent2commission'] = $commissiondata[$parent2['levelData']['id']]['commission2'] * $commission_totalprice * 0.01;
                            if ($ogdata['parent3']) $ogdata['parent3commission'] = $commissiondata[$parent3['levelData']['id']]['commission3'] * $commission_totalprice * 0.01;
                        }
                    } elseif ($product['commissionset'] == 2) {//按固定金额
                        $commissiondata = json_decode($product['commissiondata2'], true);
                        if ($commissiondata) {
                            if ($ogdata['parent1']) $ogdata['parent1commission'] = $commissiondata[$parent1['levelData']['id']]['commission1'] * $num;
                            if ($ogdata['parent2']) $ogdata['parent2commission'] = $commissiondata[$parent2['levelData']['id']]['commission2'] * $num;
                            if ($ogdata['parent3']) $ogdata['parent3commission'] = $commissiondata[$parent3['levelData']['id']]['commission3'] * $num;
                        }
                    } elseif ($product['commissionset'] == 3) {//提成是积分
                        $commissiondata = json_decode($product['commissiondata3'], true);
                        if ($commissiondata) {
                            if ($ogdata['parent1']) $ogdata['parent1score'] = $commissiondata[$parent1['levelData']['id']]['commission1'] * $num;
                            if ($ogdata['parent2']) $ogdata['parent2score'] = $commissiondata[$parent2['levelData']['id']]['commission2'] * $num;
                            if ($ogdata['parent3']) $ogdata['parent3score'] = $commissiondata[$parent3['levelData']['id']]['commission3'] * $num;
                        }
                    } else { //按会员等级设置的分销比例
                        if ($ogdata['parent1']) {
                            if ($parent1['levelData']['commissiontype'] == 1) { //固定金额按单
                                if ($istc1 == 0) {
                                    $ogdata['parent1commission'] = $parent1['levelData']['commission1'];
                                    $istc1 = 1;
                                }
                            } else {
                                $ogdata['parent1commission'] = $parent1['levelData']['commission1'] * $commission_totalprice * 0.01;
                            }
                        }
                        if ($ogdata['parent2']) {
                            if ($parent2['levelData']['commissiontype'] == 1) {
                                if ($istc2 == 0) {
                                    $ogdata['parent2commission'] = $parent2['levelData']['commission2'];
                                    $istc2 = 1;
                                }
                            } else {
                                $ogdata['parent2commission'] = $parent2['levelData']['commission2'] * $commission_totalprice * 0.01;
                            }
                        }
                        if ($ogdata['parent3']) {
                            if ($parent3['levelData']['commissiontype'] == 1) {
                                if ($istc3 == 0) {
                                    $ogdata['parent3commission'] = $parent3['levelData']['commission3'];
                                    $istc3 = 1;
                                }
                            } else {
                                $ogdata['parent3commission'] = $parent3['levelData']['commission3'] * $commission_totalprice * 0.01;
                            }
                        }
                    }
                }

                $ogid = Db::name('restaurant_shop_order_goods')->insertGetId($ogdata);
                $ogids[] = $ogid;
                if ($ogdata['parent1'] && ($ogdata['parent1commission'] > 0 || $ogdata['parent1score'] > 0)) {
                    Db::name('member_commission_record')->insert(['aid' => aid, 'mid' => $ogdata['parent1'], 'frommid' => mid, 'orderid' => $orderid, 'ogid' => $ogid, 'type' => 'restaurant_shop', 'commission' => $ogdata['parent1commission'], 'score' => $ogdata['parent1score'], 'remark' => '下级购买菜品奖励', 'createtime' => time()]);
                }
                if ($ogdata['parent2'] && ($ogdata['parent2commission'] || $ogdata['parent2score'])) {
                    Db::name('member_commission_record')->insert(['aid' => aid, 'mid' => $ogdata['parent2'], 'frommid' => mid, 'orderid' => $orderid, 'ogid' => $ogid, 'type' => 'restaurant_shop', 'commission' => $ogdata['parent2commission'], 'score' => $ogdata['parent2score'], 'remark' => '下二级购买菜品奖励', 'createtime' => time()]);
                }
                if ($ogdata['parent3'] && ($ogdata['parent3commission'] || $ogdata['parent3score'])) {
                    Db::name('member_commission_record')->insert(['aid' => aid, 'mid' => $ogdata['parent3'], 'frommid' => mid, 'orderid' => $orderid, 'ogid' => $ogid, 'type' => 'restaurant_shop', 'commission' => $ogdata['parent3commission'], 'score' => $ogdata['parent3score'], 'remark' => '下三级购买菜品奖励', 'createtime' => time()]);
                }

                //删除购物车
                Db::name('restaurant_shop_cart')->where('aid', aid)->where('mid', mid)->where('ggid', $guige['id'])->where('proid', $product['id'])->delete();
                Db::name('restaurant_product_guige')->where('aid', aid)->where('id', $guige['id'])->update(['stock' => Db::raw("stock-$num"), 'sales' => Db::raw("sales+$num"), 'sales_daily' => Db::raw("sales_daily+$num")]);
                Db::name('restaurant_product')->where('aid', aid)->where('id', $product['id'])->update(['stock' => Db::raw("stock-$num"), 'sales' => Db::raw("sales+$num"), 'sales_daily' => Db::raw("sales_daily+$num")]);
            }

            //根据餐后付款设置，开启时下单后打印小票，关闭时付款后打印小票
            $restaurant_shop_sysset = Db::name('restaurant_shop_sysset')->where('aid', aid)->where('bid', $data['bid'])->find();
            if ($restaurant_shop_sysset['pay_after'] == 1) {
                \app\custom\Restaurant::print('restaurant_shop', $orderdata);
            }

            //公众号通知 订单提交成功
            $tmplcontent = [];
            $tmplcontent['first'] = '有新点餐订单提交成功';
            $tmplcontent['remark'] = '点击进入查看~';
            $tmplcontent['keyword1'] = Db::name('admin_set')->where('aid', aid)->value('name'); //店铺
            $tmplcontent['keyword2'] = date('Y-m-d H:i:s', $orderdata['createtime']);//下单时间
            $tmplcontent['keyword3'] = $orderdata['title'];//商品
            $tmplcontent['keyword4'] = $orderdata['totalprice'] . '元';//金额
            \app\common\Wechat::sendhttmpl(aid, $orderdata['bid'], 'tmpl_orderconfirm', $tmplcontent, m_url('admin/restaurant/shoporder'), $orderdata['mdid']);
        }
        if (count($buydata) > 1) { //创建合并支付单
            $payorderid = \app\model\Payorder::createorder(aid, 0, mid, 'restaurant_shop_hb', $orderid, $ordernum, $orderdata['title'], $alltotalprice, $alltotalscore);
        }

        return json(['status' => 1, 'payorderid' => $payorderid, 'msg' => '提交成功', 'pay_after' => $shop_sysset['pay_after']]);
    }

    //服务员确认支付
    public function payconfirm()
    {
        if(!session('ADMIN_LOGIN')||!session('ADMIN_UID')){
            return json(['status'=>0,'msg'=>'请登录后操作']);
        }
        define('aid',session('ADMIN_AID'));
        define('bid',session('ADMIN_BID'));
        
        if (!input('param.info')) {
            return json(['status' => 0, 'msg' => '参数错误']);
        }
        $info = input('param.info');
        $tableid = $info['tableId'];
        $discount = $info['discount'];
        $zhekou = $info['rebate'];
        $paytype = $info['paytype'];
        $mid = $info['mid'];
        $usescore = $info['usescore'];
        
        $info = Db::name('restaurant_table')->where('aid', aid)->where('bid', bid)->where('id', $tableid)->find();
        if (empty($info)) {
            return json(['status' => 0, 'msg' => '餐桌不存在']);
        }
        if ($info['status'] == 0 || $info['orderid'] == 0) {
            return json(['status' => 0, 'msg' => '未找到待结算订单']);
        }
        if($mid){
           $member = Db::name('member')->where('aid', aid)->where('id', $mid)->find();
            if (!$member) {
                return json(['status' => 0, 'msg' => '会员不存在']);
            } 
        }
        
//        Db::name('restaurant_shop_order')->where('aid',aid)->where('bid',bid)->where('tableid',$tableid)
//            ->where('id', $info['orderid'])->update(['status' => 3]);
        $order = Db::name('restaurant_shop_order')->where('aid', aid)->where('bid', bid)->where('id', $info['orderid'])
            ->find();
        if ($order['totalprice'] < $discount) {
            return json(['status' => 0, 'msg' => '优惠金额不正确']);
        }
        $discount_money = 0;
        if($zhekou){
            $discount_money = round($order['totalprice'],2)*(1-($zhekou*0.1));
            $discount_money = round($discount_money,2);
        }
        
        $updateData = ['discount_money' => $discount, 'totalprice' => $order['totalprice'] - $discount - $discount_money];
        if (empty($order['paytypeid'])) {
            $updateData['paytypeid'] = 4;
            $updateData['paytype'] = '货到付款';
        }
        if ($paytype) {
            $updateData['paytype'] = $paytype;
        }
        if ($zhekou) {
            $updateData['rebate'] = $zhekou;
        }
        $updateData['paytime'] = time();
        if($mid){
           $updateData['mid'] = $mid;
           if($usescore>0 && $member['score']>0 && $member['score']<=$updateData['totalprice']){
                $adminset = Db::name('admin_set')->where('aid', aid)->find();
                $updateData['scoredk_money'] = round($member['score'] * $adminset['score2money'], 2);
                /*$userinfo['scoredkmaxpercent'] = $adminset['scoredkmaxpercent'];
                $userinfo['scoremaxtype'] = 0; //0最大百分比 1最大抵扣金额*/
                $updateData['scoredkscore'] = $member['score'];
                $updateData['totalprice'] = $updateData['totalprice']-$updateData['scoredk_money'];
          }
        }
        
        
        Db::name('restaurant_shop_order')->where('aid', aid)->where('bid', bid)->where('id', $info['orderid'])
            ->update($updateData);
        $rs = \app\custom\Restaurant::shop_orderconfirm($info['orderid']);
        if ($rs['status'] == 0) return json($rs);
        
        $restaurant_shop_sysset = Db::name('restaurant_shop_sysset')->where('aid', aid)->where('bid', bid)->find();
        if ($restaurant_shop_sysset['pay_after'] == 1) {
            $orderGoods = Db::name('restaurant_shop_order_goods')->alias('og')->where('orderid', $info['orderid'])->leftJoin('restaurant_product p', 'p.id=og.proid')->fieldRaw('og.*,p.area_id')->select()->toArray();
            $orderdata = Db::name('restaurant_shop_order')->where('id',$info['orderid'])->find();
            \app\custom\Restaurant::print('restaurant_shop',$orderdata, $orderGoods,0,0,1);
        }

        return json(['status' => 1, 'msg' => '结算完成，请清理桌台']);
    }
    
    //清台
    public function clean()
    {
        if(!session('ADMIN_LOGIN')||!session('ADMIN_UID')){
            return json(['status'=>0,'msg'=>'请登录后操作']);
        }
        define('aid',session('ADMIN_AID'));
        define('bid',session('ADMIN_BID'));
        
        if (!input('param.tableId/d')) {
            return json(['status' => 0, 'msg' => '参数错误']);
        }
        $tableid = input('param.tableId/d');
        $info = Db::name('restaurant_table')->where('aid', aid)->where('bid', bid)->where('id', $tableid)->find();
        if (empty($info)) {
            return json(['status' => 0, 'msg' => '餐桌不存在']);
        }
        if ($info['status'] == 0) {
            return json(['status' => 0, 'msg' => '当前无需清台']);
        }
        if ($info['orderid']) {
            $order = Db::name('restaurant_shop_order')->where('aid', aid)->where('bid', bid)->where('tableid', $tableid)
                ->where('id', $info['orderid'])->find();
            if ($order && $order['status'] != 3 && $order['totalprice'] > 0)
                return json(['status' => 0, 'msg' => '请先完成订单结算']);
        }
        Db::name('restaurant_table')->where('aid', aid)->where('bid', bid)->where('id', $tableid)->update(['status' => 3, 'orderid' => 0]);

        return json(['status' => 1, 'msg' => '清理完后请切换餐桌状态']);
    }

    //清台完成 设为空闲中
    public function cleanOver()
    {
        if(!session('ADMIN_LOGIN')||!session('ADMIN_UID')){
            return json(['status'=>0,'msg'=>'请登录后操作']);
        }
        define('aid',session('ADMIN_AID'));
        define('bid',session('ADMIN_BID'));
        
        if (!input('param.tableId/d')) {
            return json(['status' => 0, 'msg' => '参数错误']);
        }
        $tableid = input('param.tableId/d');
        $info = Db::name('restaurant_table')->where('aid', aid)->where('bid', bid)->where('id', $tableid)->find();
        if (empty($info)) {
            return json(['status' => 0, 'msg' => '餐桌不存在']);
        }
        if ($info['status'] == 2) {
            return json(['status' => 0, 'msg' => '就餐中，请先结算然后清台']);
        }
        Db::name('restaurant_table')->where('aid', aid)->where('bid', bid)->where('id', $tableid)->update(['status' => 0, 'orderid' => 0]);

        return json(['status' => 1, 'msg' => '设置成功']);
    }
    
    public function detail()
    {
        if(!session('ADMIN_LOGIN')||!session('ADMIN_UID')){
            return json(['status'=>0,'msg'=>'请登录后操作']);
        }
        define('aid',session('ADMIN_AID'));
        define('bid',session('ADMIN_BID'));
        define('mid',session('ADMIN_UID'));
        
        if (!input('param.id')) {
            return json(['status' => 0, 'msg' => '参数错误']);
        }
        $order_goods_sum = 0;
        $info = Db::name('restaurant_table')->where('aid', aid)->where('bid', bid)->where('id', input('param.id/d'))->find();
        //关联订单，已点菜品
        if ($info['orderid']) {
            $order = Db::name('restaurant_shop_order')->where('aid', aid)->where('bid', bid)->where('tableid', $info['id'])->where('id', $info['orderid'])->find();
            $order_goods = Db::name('restaurant_shop_order_goods')->where('aid', aid)->where('bid', bid)->where('orderid', $info['orderid'])->select();
            $order_goods_sum = array_sum($order_goods->column('num'));
        }
        $rdata = [];
        $info['create_time'] = date('Y-m-d H:i', $info['create_time']);
        $rdata['info'] = $info;
        $rdata['order'] = $order ? $order : [];
        $rdata['order_goods'] = $order_goods ? $order_goods : [];
        $rdata['order_goods_sum'] = $order_goods_sum;
        return json($rdata);
    }
    
    public function cartdelete()
    {
        if(!session('ADMIN_LOGIN')||!session('ADMIN_UID')){
            return json(['status'=>0,'msg'=>'请登录后操作']);
        }
        define('bid',session('ADMIN_BID'));
        define('mid',session('ADMIN_UID'));
        
        $id = input('post.id/d');
        if (!$id) {
            Db::name('restaurant_shop_cart')->where('bid', bid)->where('mid', mid)->delete();
            return json(['status' => 1, 'msg' => '删除成功']);
        }
        Db::name('restaurant_shop_cart')->where('id', $id)->where('mid', mid)->delete();
        return json(['status' => 1, 'msg' => '删除成功']);
    }

    public function cartclear()
    {
        if(!session('ADMIN_LOGIN')||!session('ADMIN_UID')){
            return json(['status'=>0,'msg'=>'请登录后操作']);
        }
        define('aid',session('ADMIN_AID'));
        define('bid',session('ADMIN_BID'));
        define('mid',session('ADMIN_UID'));
        $tableId = input('post.tableId/d');
        if (!$tableId) {
            Db::name('restaurant_shop_cart')->where('aid', aid)->where('bid', bid)->where('mid', mid)->delete();
            return json(['status' => 1, 'msg' => '删除成功']);
        }
        Db::name('restaurant_shop_cart')->where('aid', aid)->where('bid', bid)->where('mid', mid)->where('tid', $tableId)->delete();
        return json(['status' => 1, 'msg' => '删除成功']);
    }
    
     //换桌
    public function change()
    {
        if(!session('ADMIN_LOGIN')||!session('ADMIN_UID')){
            return json(['status'=>0,'msg'=>'请登录后操作']);
        }
        define('aid',session('ADMIN_AID'));
        define('bid',session('ADMIN_BID'));
        
        if (!input('param.new/d') || !input('param.origin/d')) {
            return json(['status' => 0, 'msg' => '参数错误']);
        }
        $info = Db::name('restaurant_table')->where('aid', aid)->where('bid', bid)->where('id', input('param.origin/d'))->find();
        if (empty($info)) {
            return json(['status' => 0, 'msg' => '餐桌不存在']);
        }
        $new_table = Db::name('restaurant_table')->where('aid', aid)->where('bid', bid)->where('id', input('param.new/d'))->find();
        if (empty($new_table)) {
            return json(['status' => 0, 'msg' => '餐桌不存在']);
        }
        if ($new_table['status'] !== 0 || $new_table['orderid']) {
            return json(['status' => 0, 'msg' => '餐桌状态不可用']);
        }
        Db::name('restaurant_table')->where('aid', aid)->where('bid', bid)->where('id', input('param.origin/d'))->update(['status' => 0, 'orderid' => 0]);
        Db::name('restaurant_table')->where('aid', aid)->where('bid', bid)->where('id', input('param.new/d'))->update(['status' => 2, 'orderid' => $info['orderid']]);
        if ($info['orderid']) {
            Db::name('restaurant_shop_order')->where('aid', aid)->where('bid', bid)->where('tableid', input('param.origin/d'))
                ->where('id', $info['orderid'])->update(['tableid' => $new_table['id']]);
        }

        return json(['status' => 1, 'msg' => '换桌成功']);
    }
}
