<?php


// +----------------------------------------------------------------------
// | 餐桌
// +----------------------------------------------------------------------
namespace app\controller;

use app\model\RestaurantTableCategoryModel as Category;
use app\model\RestaurantTableModel;
use app\service\RestaurantTableService;
use think\facade\Db;
use think\facade\View;

class RestaurantTable extends Common
{
    public function initialize()
    {
        parent::initialize();
    }

    //餐桌
    public function index()
    {
        if (request()->isAjax()) {
            $page = input('param.page');
            $limit = input('param.limit');

            if (input('param.field') && input('param.order')) {
                $order = input('param.field') . ' ' . input('param.order');
            } else {
                $order = 'sort desc,id desc';
            }
            $where[] = ['aid', '=', aid];
            $where[] = ['bid', '=', bid];
            $data = [];
            $data = (new RestaurantTableModel())->getList($where, $page, $limit, $order);

            return json(['code' => 0, 'msg' => '查询成功', 'count' => $data['count'], 'data' => $data['list']]);
        }

        View::assign('bid', bid);

        return View::fetch();
    }

    public function edit()
    {

        if (input('param.id')) {
            $info = RestaurantTableModel::with('category')->where('aid', aid)->where('bid', bid)->where('id', input('param.id/d'))->find();
        } else {
            $info = array('id' => '', 'canbook' => 1);
        }

        View::assign('info', $info);
        //分类
        $where[] = ['aid', '=', aid];
        $where[] = ['bid', '=', bid];
        $tableCategory = (new Category())->getList($where, 1, 'all');
        View::assign('tableCategory', $tableCategory);

        $printArr = Db::name('wifiprint_set')->where('aid', aid)->where('bid', bid)->order('id')->column('name', 'id');
        View::assign('printArr', $printArr);

        return View::fetch();
    }

    //保存
    public function save()
    {
        $info = input('post.info/a');
        $info['print_ids'] = implode(',', $info['print_ids']);
        if ($info['id']) {
            $table = Db::name('restaurant_table')->where('aid', aid)->where('bid', bid)->where('id', $info['id'])->find();
            if (empty($table)) {
                return json(['status' => 0, 'msg' => '不存在的信息']);
            }
            if ($info['status'] == 3) {
                $info['orderid'] = 0;
            }

//            if(empty($table['qrcode'])) {
//                //二维码 参数格式：id_1-cid_2
//                $path = 'pages/restaurant/fastbuy';
//                $scene = ['tableId' => $info['id']];
//                $qrcode = \app\common\Wechat::getQRCode(aid,'wx',$path,$scene);
//                if ($qrcode['status'] == 1 && $qrcode['url']) {
//                    $info['qrcode'] = $qrcode['url'];
//                } else {
//                    Db::name('restaurant_table')->where('aid',aid)->where('id',$info['id'])->update($info);
//                    return json(['status'=>0,'msg'=>$qrcode['msg']]);
//                }
//            }
            Db::name('restaurant_table')->where('aid', aid)->where('bid', bid)->where('id', $info['id'])->update($info);
            \app\common\System::plog(sprintf('编辑餐饮餐桌:%s[%s]', $info['name'], $info['id']));
        } else {
            $info['aid'] = aid;
            $info['bid'] = bid;
            $table = new RestaurantTableModel();
            $table->save($info);
            //二维码 参数格式：id_1-cid_2
//            $path = 'pages/restaurant/fastbuy';
//            $scene = ['tableId' => $info['id']];
//            $qrcode = \app\common\Wechat::getQRCode(aid,'wx',$path,$scene);
//            if ($qrcode['status'] == 1 && $qrcode['url']) {
//                $table = new RestaurantTableModel();
//                $table->where('id',$info['id'])->where('aid',aid)->save(['qrcode' => $qrcode['url']]);
//            } else {
//                return json(['status'=>0,'msg'=>$qrcode['msg']]);
//            }

            \app\common\System::plog(sprintf('添加餐饮餐桌:%s[%s]', $table->name, $table->id));
        }
        return json(['status' => 1, 'msg' => '操作成功', 'url' => (string)url('index')]);
    }

    //删除
    public function del()
    {
        $ids = input('post.ids/a');

        Db::name('restaurant_table')->where('aid', aid)->where('bid', bid)->where('id', 'in', $ids)->delete();
        \app\common\System::plog('删除餐饮餐桌' . implode(',', $ids));
        return json(['status' => 1, 'msg' => '删除成功']);
    }


}