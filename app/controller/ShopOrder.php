<?php


// +----------------------------------------------------------------------
// | 商城-商品订单
// +----------------------------------------------------------------------
namespace app\controller;

use think\facade\Db;
use think\facade\View;

class ShopOrder extends Common
{
    //订单列表
    public function index()
    {
        if (getcustom('plug_xiongmao')) {
            $admin = Db::name('admin')->where('id', aid)->find();
            if (in_array('order_show_member_apply', explode(',', $admin['remark']))) {
                $order_show_member_apply = true;
                View::assign('order_show_member_apply', $order_show_member_apply);
            }
        }
        if (request()->isAjax()) {
            $page = input('param.page');
            $limit = input('param.limit');
            if (input('param.field') && input('param.order')) {
                $order = 'order.' . input('param.field') . ' ' . input('param.order');
            } else {
                $order = 'order.id desc';
            }
            $where = [];
            $where[] = ['order.aid', '=', aid];
            if (bid == 0) {
                if (input('param.bid')) {
                    $where[] = ['order.bid', '=', input('param.bid')];
                } elseif (input('param.showtype') == 2) {
                    $where[] = ['order.bid', '<>', 0];
                } else {
                    $where[] = ['order.bid', '=', 0];
                }
            } else {
                $where[] = ['order.bid', '=', bid];
            }
            if ($this->mdid) {
                $where[] = ['order.mdid', '=', $this->mdid];
            }
            if (input('?param.ogid')) {
                if (input('param.ogid') == '') {
                    $where[] = ['1', '=', 0];
                } else {
                    $ids = Db::name('shop_order_goods')->where('id', 'in', input('param.ogid'))->column('orderid');
                    $where[] = ['order.id', 'in', $ids];
                }
            }
            if (input('param.mid')) $where[] = ['order.mid', '=', input('param.mid')];
            if (input('param.freight_id')) $where[] = ['order.freight_id', '=', input('param.freight_id')];
            if (input('param.proname')) $where[] = ['order.proname', 'like', '%' . input('param.proname') . '%'];
            if (input('param.ordernum')) $where[] = ['order.ordernum', 'like', '%' . input('param.ordernum') . '%'];
            if (input('param.nickname')) $where[] = ['member.nickname|member.realname', 'like', '%' . input('param.nickname') . '%'];
            if (input('param.linkman')) $where[] = ['order.linkman|order.tel', 'like', '%' . input('param.linkman') . '%'];
            if (input('param.tel')) $where[] = ['order.tel', 'like', '%' . input('param.tel') . '%'];
            if (input('param.proid')) {
                $orderids = Db::name('shop_order_goods')->where('aid', aid)->where('proid', input('param.proid'))->column('orderid');
                $where[] = ['order.id', 'in', $orderids];
            }
            if (input('param.ctime')) {
                $ctime = explode(' ~ ', input('param.ctime'));
                if (input('param.time_type') == 1) { //下单时间
                    $where[] = ['order.createtime', '>=', strtotime($ctime[0])];
                    $where[] = ['order.createtime', '<', strtotime($ctime[1]) + 86400];
                } elseif (input('param.time_type') == 2) { //付款时间
                    $where[] = ['order.paytime', '>=', strtotime($ctime[0])];
                    $where[] = ['order.paytime', '<', strtotime($ctime[1]) + 86400];
                } elseif (input('param.time_type') == 3) { //发货时间
                    $where[] = ['order.send_time', '>=', strtotime($ctime[0])];
                    $where[] = ['order.send_time', '<', strtotime($ctime[1]) + 86400];
                } elseif (input('param.time_type') == 4) { //完成时间
                    $where[] = ['order.collect_time', '>=', strtotime($ctime[0])];
                    $where[] = ['order.collect_time', '<', strtotime($ctime[1]) + 86400];
                }
            }
            if (input('param.keyword')) {
                $keyword = input('param.keyword');
                $keyword_type = input('param.keyword_type');
                if ($keyword_type == 1) { //订单号
                    $where[] = ['order.ordernum', 'like', '%' . $keyword . '%'];
                } elseif ($keyword_type == 2) { //会员ID
                    $where[] = ['order.mid', '=', $keyword];
                } elseif ($keyword_type == 3) { //会员信息
                    $where[] = ['member.nickname|member.realname', 'like', '%' . $keyword . '%'];
                } elseif ($keyword_type == 4) { //收货信息
                    $where[] = ['order.linkman|order.tel|order.area|order.address', 'like', '%' . $keyword . '%'];
                } elseif ($keyword_type == 5) { //快递单号
                    $where[] = ['order.express_no', 'like', '%' . $keyword . '%'];
                } elseif ($keyword_type == 6) { //商品ID
                    $orderids = Db::name('shop_order_goods')->where('aid', aid)->where('proid', $keyword)->column('orderid');
                    $where[] = ['order.id', 'in', $orderids];
                } elseif ($keyword_type == 7) { //商品名称
                    $orderids = Db::name('shop_order_goods')->where('aid', aid)->where('name', 'like', '%' . $keyword . '%')->column('orderid');
                    $where[] = ['order.id', 'in', $orderids];
                } elseif ($keyword_type == 8) { //商品编码
                    $orderids = Db::name('shop_order_goods')->where('aid', aid)->where('procode', 'like', '%' . $keyword . '%')->column('orderid');
                    $where[] = ['order.id', 'in', $orderids];
                } elseif ($keyword_type == 9) { //核销员
                    $orderids = Db::name('hexiao_order')->where('aid', aid)->where('type', 'shop')->where('remark', 'like', '%' . $keyword . '%')->column('orderid');
                    $where[] = ['order.id', 'in', $orderids];
                } elseif ($keyword_type == 10) { //所属门店
                    $mdids = Db::name('mendian')->where('aid', aid)->where('name', 'like', '%' . $keyword . '%')->column('id');
                    $where[] = ['order.mdid', 'in', $mdids];
                }
            }
            if (input('?param.status') && input('param.status') !== '') {
                if (input('param.status') == 5) {
                    $where[] = ['order.refund_status', '=', 1];
                } elseif (input('param.status') == 6) {
                    $where[] = ['order.refund_status', '=', 2];
                } elseif (input('param.status') == 7) {
                    $where[] = ['order.refund_status', '=', 3];
                } else {
                    $where[] = ['order.status', '=', input('param.status')];
                }
            }
            $count = 0 + Db::name('shop_order')->alias('order')->leftJoin('member member', 'member.id=order.mid')->where($where)->count();
            //echo M()->_sql();
            $list = Db::name('shop_order')->alias('order')->field('order.*')->leftJoin('member member', 'member.id=order.mid')->where($where)->page($page, $limit)->order($order)->select()->toArray();

            foreach ($list as $k => $vo) {
                $member = Db::name('member')->where('id', $vo['mid'])->find();
                $oglist = Db::name('shop_order_goods')->where('aid', aid)->where('orderid', $vo['id'])->select()->toArray();
                $goodsdata = array();
                foreach ($oglist as $og) {
                    switch ($og['currency']) {
                        case '1':
                            $og['sell_price'] = '¥ '.$og['sell_price'];                            
                            break;
                        case '2':
                            $og['sell_price'] = 'NT$ '.$og['sell_price'];                           
                            break;
                        case '3':
                            $og['sell_price'] = '$ '.$og['sell_price'];
                            break;
                        case '4':
                            $og['sell_price'] = '₫ '.$og['sell_price'];
                            break;
                        case '5':
                            $og['sell_price'] = '฿ '.$og['sell_price'];
                            break;
                        case '6':
                            $og['sell_price'] = '₹ '.$og['sell_price'];
                            break;
                        case '7':
                            $og['sell_price'] = 'RM '.$og['sell_price'];
                            break;
                        default:
                            $og['sell_price'] = '¥ '.$og['sell_price'];
                            break;
                    }
                    $goodsdata[] = '<div style="font-size:12px;float:left;clear:both;margin:1px 0">' .
                        '<img src="' . $og['pic'] . '" style="max-width:60px;float:left">' .
                        '<div style="float: left;width:160px;margin-left: 10px;white-space:normal;line-height:16px;">' .
                        '<div style="width:100%;min-height:25px;max-height:32px;overflow:hidden">' . $og['name'] . '</div>' .
                        '<div style="padding-top:0px;color:#f60"><span style="color:#888">' . $og['ggname'] . '</span></div>' .
                        '<div style="padding-top:0px;color:#f60;">' . $og['sell_price'] . ' × ' . $og['num'] . '</div>' .
                        '</div>' .
                        '</div>';
                }
                $list[$k]['goodsdata'] = implode('', $goodsdata);
                if ($vo['bid'] > 0) {
                    $list[$k]['bname'] = Db::name('business')->where('aid', aid)->where('id', $vo['bid'])->value('name');
                } else {
                    $list[$k]['bname'] = '平台自营';
                }
                $refundOrder = Db::name('shop_refund_order')->where('refund_status', '>', 0)->where('aid', aid)->where('orderid', $vo['id'])->count();
                $list[$k]['refundCount'] = $refundOrder;
                $list[$k]['payorder'] = [];
                if ($vo['paytypeid'] == 5) {
                    $list[$k]['payorder'] = Db::name('payorder')->where('id', $vo['payorderid'])->where('aid', aid)->find();
                }
                $list[$k]['nickname'] = $member['nickname'];
                $list[$k]['headimg'] = $member['headimg'];
                $list[$k]['m_remark'] = $member['remark'];
                $list[$k]['platform'] = getplatformname($vo['platform']);
                switch ($list[$k]['currency']) {
                    case '1':
                        $list[$k]['product_price'] = '¥ '.$list[$k]['product_price'];
                        $list[$k]['totalprice'] = '¥ '.$list[$k]['totalprice'];
                        break;
                    case '2':
                        $list[$k]['product_price'] = 'NT$ '.$list[$k]['product_price'];
                        $list[$k]['totalprice'] = 'NT$ '.$list[$k]['totalprice'];
                        break;
                    case '3':
                        $list[$k]['product_price'] = '$ '.$list[$k]['product_price'];
                        $list[$k]['totalprice'] = '$ '.$list[$k]['totalprice'];
                        break;
                    case '4':
                        $list[$k]['product_price'] = '₫ '.$list[$k]['product_price'];
                        $list[$k]['totalprice'] = '₫ '.$list[$k]['totalprice'];
                        break;
                    case '5':
                        $list[$k]['product_price'] = '฿ '.$list[$k]['product_price'];
                        $list[$k]['totalprice'] = '฿ '.$list[$k]['totalprice'];
                        break;
                    case '6':
                        $list[$k]['product_price'] = '₹ '.$list[$k]['product_price'];
                        $list[$k]['totalprice'] = '₹ '.$list[$k]['totalprice'];
                        break;
                    case '7':
                        $list[$k]['product_price'] = 'RM '.$list[$k]['product_price'];
                        $list[$k]['totalprice'] = 'RM '.$list[$k]['totalprice'];
                        break;
                    default:
                        $list[$k]['product_price'] = '¥ '.$list[$k]['product_price'];
                        $list[$k]['totalprice'] = '¥ '.$list[$k]['totalprice'];
                        break;
                }
                if ($order_show_member_apply) {
                    $member_level = Db::name('member_level')->where('id', $member['levelid'])->find();
                    $list[$k]['member_apply_info'] = $member['realname'] . '<br>' . $member['tel'] . '<br>' . $member_level['name'];
                }
                $list[$k]['member_address'] = Db::name('member_address')->where('id', $vo['address_id'])->find();
            }
            return json(['code' => 0, 'msg' => '查询成功', 'count' => $count, 'data' => $list]);
        }
        $machinelist = Db::name('wifiprint_set')->where('aid', aid)->where('status', 1)->where('bid', bid)->select()->toArray();
        $hasprint = 0;
        if ($machinelist) {
            $hasprint = 1;
        }
        $peisong_set = Db::name('peisong_set')->where('aid', aid)->find();
        if ($peisong_set['status'] == 1 && bid > 0 && $peisong_set['businessst'] == 0 && $peisong_set['make_status'] == 0) $peisong_set['status'] = 0;
        $freight = Db::name('freight')->where('aid', aid)->where('bid', bid)->select()->toArray();

        View::assign('freight', $freight);
        View::assign('peisong_set', $peisong_set);
        View::assign('hasprint', $hasprint);
        View::assign('express_data', express_data());
        return View::fetch();
    }

    //导出
    public function excel()
    {
        set_time_limit(0);
        ini_set('memory_limit', '2000M');
        if (input('param.field') && input('param.order')) {
            $order = input('param.field') . ' ' . input('param.order');
        } else {
            $order = 'id desc';
        }
        $where = [];
        $where[] = ['order.aid', '=', aid];
        if (bid == 0) {
            if (input('param.bid')) {
                $where[] = ['order.bid', '=', input('param.bid')];
            } elseif (input('param.showtype') == 2) {
                $where[] = ['order.bid', '<>', 0];
            } else {
                $where[] = ['order.bid', '=', 0];
            }
        } else {
            $where[] = ['order.bid', '=', bid];
        }
        if ($this->mdid) {
            $where[] = ['order.mdid', '=', $this->mdid];
        }
        if (input('param.mid')) $where[] = ['order.mid', '=', input('param.mid')];
        if (input('param.proname')) $where[] = ['order.proname', 'like', '%' . input('param.proname') . '%'];
        if (input('param.ordernum')) $where[] = ['order.ordernum', 'like', '%' . input('param.ordernum') . '%'];
        if (input('param.nickname')) $where[] = ['member.nickname|member.realname', 'like', '%' . input('param.nickname') . '%'];
        if (input('param.linkman')) $where[] = ['order.linkman', 'like', '%' . input('param.linkman') . '%'];
        if (input('param.tel')) $where[] = ['order.tel', 'like', '%' . input('param.tel') . '%'];

        if (input('?param.status') && input('param.status') !== '') {
            if (input('param.status') == 5) {
                $where[] = ['order.refund_status', '=', 1];
            } elseif (input('param.status') == 6) {
                $where[] = ['order.refund_status', '=', 2];
            } elseif (input('param.status') == 7) {
                $where[] = ['order.refund_status', '=', 3];
            } else {
                $where[] = ['order.status', '=', input('param.status')];
            }
        }
        if (input('param.ctime')) {
            $ctime = explode(' ~ ', input('param.ctime'));
            if (input('param.time_type') == 1) { //下单时间
                $where[] = ['order.createtime', '>=', strtotime($ctime[0])];
                $where[] = ['order.createtime', '<', strtotime($ctime[1]) + 86400];
            } elseif (input('param.time_type') == 2) { //付款时间
                $where[] = ['order.paytime', '>=', strtotime($ctime[0])];
                $where[] = ['order.paytime', '<', strtotime($ctime[1]) + 86400];
            } elseif (input('param.time_type') == 3) { //发货时间
                $where[] = ['order.send_time', '>=', strtotime($ctime[0])];
                $where[] = ['order.send_time', '<', strtotime($ctime[1]) + 86400];
            } elseif (input('param.time_type') == 4) { //完成时间
                $where[] = ['order.collect_time', '>=', strtotime($ctime[0])];
                $where[] = ['order.collect_time', '<', strtotime($ctime[1]) + 86400];
            }
        }
        if (input('param.keyword')) {
            $keyword = input('param.keyword');
            $keyword_type = input('param.keyword_type');
            if ($keyword_type == 1) { //订单号
                $where[] = ['order.ordernum', 'like', '%' . $keyword . '%'];
            } elseif ($keyword_type == 2) { //会员ID
                $where[] = ['order.mid', '=', $keyword];
            } elseif ($keyword_type == 3) { //会员信息
                $where[] = ['member.nickname|member.realname', 'like', '%' . $keyword . '%'];
            } elseif ($keyword_type == 4) { //收货信息
                $where[] = ['order.linkman|order.tel|order.area|order.address', 'like', '%' . $keyword . '%'];
            } elseif ($keyword_type == 5) { //快递单号
                $where[] = ['order.express_no', 'like', '%' . $keyword . '%'];
            } elseif ($keyword_type == 6) { //商品ID
                $orderids = Db::name('shop_order_goods')->where('aid', aid)->where('proid', $keyword)->column('orderid');
                $where[] = ['order.id', 'in', $orderids];
            } elseif ($keyword_type == 7) { //商品名称
                $orderids = Db::name('shop_order_goods')->where('aid', aid)->where('name', 'like', '%' . $keyword . '%')->column('orderid');
                $where[] = ['order.id', 'in', $orderids];
            } elseif ($keyword_type == 8) { //商品编码
                $orderids = Db::name('shop_order_goods')->where('aid', aid)->where('procode', 'like', '%' . $keyword . '%')->column('orderid');
                $where[] = ['order.id', 'in', $orderids];
            } elseif ($keyword_type == 9) { //核销员
                $orderids = Db::name('hexiao_order')->where('aid', aid)->where('type', 'shop')->where('remark', 'like', '%' . $keyword . '%')->column('orderid');
                $where[] = ['order.id', 'in', $orderids];
            } elseif ($keyword_type == 10) { //所属门店
                $mdids = Db::name('mendian')->where('aid', aid)->where('name', 'like', '%' . $keyword . '%')->column('id');
                $where[] = ['order.mdid', 'in', $mdids];
            }
        }

        $list = Db::name('shop_order')->alias('order')->field('order.*')->leftJoin('member member', 'member.id=order.mid')->where($where)->order($order)->select()->toArray();
        if (getcustom('plug_xiongmao')) {
            $this->excel2($list);
        } else {
            $bArr = Db::name('business')->where('aid', aid)->column('name', 'id');
            if (!$bArr) $bArr = [];
            $bArr['0'] = '自营';
            $title = array('订单号', 'openid', '来源', '所属商家', '下单人', '姓名', '电话', '收货地址', '商品名称', '规格', '数量', '总价', '单价', '成本', '折扣金额', '实付款', '支付方式', '配送方式', '卡密', '配送/提货时间', '运费', '积分抵扣', '满额立减', '优惠券优惠', '状态', '下单时间', '付款时间', '发货时间', '完成时间', '快递信息', '表单信息', '商家备注', '核销员', '核销门店', '表单信息', '佣金总额', '一级佣金', '会员信息', '二级佣金', '会员信息', '三级佣金', '会员信息');
            $data = [];
            foreach ($list as $k => $vo) {

                $status = '';
                if ($vo['status'] == 0) {
                    $status = '未支付';
                } elseif ($vo['status'] == 2) {
                    $status = '已发货';
                } elseif ($vo['status'] == 1) {
                    $status = '已支付';
                } elseif ($vo['status'] == 3) {
                    $status = '已收货';
                } elseif ($vo['status'] == 4) {
                    $status = '已关闭';
                }

                $member = Db::name('member')->where('id', $vo['mid'])->find();
                $oglist = Db::name('shop_order_goods')->where('orderid', $vo['id'])->select()->toArray();
                //$xm=array();
                foreach ($oglist as $k2 => $og) {
                    $barcode = '';
                    if ($og['barcode']) $barcode = "(" . $og['barcode'] . ")";
                    //$xm[] = $og['name'].$barcode."/".$og['ggname']." × ".$og['num']."";

                    $parent1commission = $og['parent1'] ? $og['parent1commission'] : 0;
                    $parent2commission = $og['parent2'] ? $og['parent2commission'] : 0;
                    $parent3commission = $og['parent3'] ? $og['parent3commission'] : 0;
                    $totalcommission = $parent1commission + $parent2commission + $parent3commission;
                    if ($og['parent1']) {
                        $parent1 = Db::name('member')->where('id', $og['parent1'])->find();
                        $parent1str = $parent1['nickname'] . '(会员ID:' . $parent1['id'] . ')';
                    } else {
                        $parent1str = '';
                    }
                    if ($og['parent2']) {
                        $parent2 = Db::name('member')->where('id', $og['parent2'])->find();
                        $parent2str = $parent2['nickname'] . '(会员ID:' . $parent2['id'] . ')';
                    } else {
                        $parent2str = '';
                    }
                    if ($og['parent3']) {
                        $parent3 = Db::name('member')->where('id', $og['parent3'])->find();
                        $parent3str = $parent3['nickname'] . '(会员ID:' . $parent3['id'] . ')';
                    } else {
                        $parent3str = '';
                    }

                    if ($k2 == 0) {

                        //配送自定义表单
                        $vo['formdata'] = \app\model\Freight::getformdata($vo['id'], 'shop_order');
                        $formdataArr = [];
                        $message = '';
                        if ($vo['formdata']) {
                            foreach ($vo['formdata'] as $formdata) {
                                if ($formdata[2] != 'upload') {
                                    if ($formdata[0] == '备注') {
                                        $message = $formdata[1];
                                    } else {
                                        $formdataArr[] = $formdata[0] . ':' . $formdata[1];
                                    }
                                }
                            }
                        }
                        $formdatastr = implode("\r\n", $formdataArr);

                        if ($vo['freight_type'] == 1 && $vo['status'] == 3) {
                            $hexiao_order = Db::name('hexiao_order')->where('aid', aid)->where('orderid', $vo['id'])->where('type', 'shop')->find();
                            if ($hexiao_order) {
                                $hexiao_order['uname'] = Db::name('admin_user')->where('id', $hexiao_order['uid'])->value('un');
                                $hexiao_order['mendian'] = Db::name('mendian')->where('id', $vo['mdid'])->value('name');
                            }
                        }


                        $data[] = [
                            ' ' . $vo['ordernum'],
                            $member[$vo['platform'] . 'openid'],
                            getplatformname($vo['platform']),
                            $bArr[$vo['bid']],
                            $member['nickname'],
                            $vo['linkman'],
                            $vo['tel'],
                            $vo['area'] . ' ' . $vo['address'],
                            $og['name'],
                            $og['ggname'] . $barcode,
                            $og['num'],
                            $og['totalprice'],
                            $og['sell_price'],
                            $og['cost_price'],
                            $vo['leveldk_money'],
                            $vo['totalprice'],
                            $vo['paytype'],
                            $vo['freight_text'],
                            ' ' . $vo['freight_content'],
                            $vo['freight_time'],
                            $vo['freight_price'],
                            $vo['scoredk_money'],
                            $vo['manjian_money'],
                            $vo['coupon_money'],
                            $status,
                            date('Y-m-d H:i:s', $vo['createtime']),
                            $vo['paytime'] ? date('Y-m-d H:i:s', $vo['paytime']) : '',
                            $vo['send_time'] ? date('Y-m-d H:i:s', $vo['send_time']) : '',
                            $vo['collect_time'] ? date('Y-m-d H:i:s', $vo['collect_time']) : '',
                            ($vo['express_com'] ? $vo['express_com'] . '(' . $vo['express_no'] . ')' : ''),
                            $vo['message'],
                            $vo['remark'],
                            $hexiao_order['uname'],
                            $hexiao_order['mendian'],
                            $formdatastr,
                            $totalcommission,
                            $parent1commission,
                            $parent1str,
                            $parent2commission,
                            $parent2str,
                            $parent3commission,
                            $parent3str,
                        ];
                    } else {
                        $data[] = [
                            '',
                            '',
                            '',
                            '',
                            '',
                            '',
                            '',
                            '',
                            $og['name'],
                            $og['ggname'] . $barcode,
                            $og['num'],
                            $og['totalprice'],
                            $og['sell_price'],
                            $og['cost_price'],
                            '',
                            '',
                            '',
                            '',
                            '',
                            '',
                            '',
                            '',
                            '',
                            '',
                            '',
                            '',
                            '',
                            '',
                            '',
                            '',
                            '',
                            '',
                            '',
                            '',
                            '',
                            $totalcommission,
                            $parent1commission,
                            $parent1str,
                            $parent2commission,
                            $parent2str,
                            $parent3commission,
                            $parent3str,
                        ];
                    }

                }
            }
            $this->export_excel($title, $data);
        }
    }

    //订单详情

    private function excel2($list)
    {
        $title = array('订单号', '客户名称', '业务员', '业务员手机号码', '产品代码', '产品条码', '商品名称', '规格', '数量', '单价', '总价', '已付金额', '支付方式', '收货信息', '配送方式', '备注', '其他');
        $data = [];
        foreach ($list as $k => $vo) {
            $member = Db::name('member')->where('id', $vo['mid'])->find();
            $oglist = Db::name('shop_order_goods')->where('orderid', $vo['id'])->select()->toArray();
            $invoice = Db::name('invoice')->where('aid', aid)->where('order_type', 'shop')->where('orderid', $vo['id'])->find();
            if ($vo['bid'] != 0) {
                $business = Db::name('business')->where('id', $vo['bid'])->find();
            } else {
                $business = [];
                if ($member['levelid'] != 3) {
                    if ($member['realname']) $business['linkman'] = $member['realname'];
                    if ($member['tel']) $business['linktel'] = $member['tel'];
                }
            }
            $xm = array();
            foreach ($oglist as $og) {
                $xm[] = $og['name'] . "/" . $og['ggname'] . " × " . $og['num'] . "";
                $status = '';
                if ($vo['status'] == 0) {
                    $status = '未支付';
                } elseif ($vo['status'] == 2) {
                    $status = '已发货';
                } elseif ($vo['status'] == 1) {
                    $status = '已支付';
                } elseif ($vo['status'] == 3) {
                    $status = '已收货';
                } elseif ($vo['status'] == 4) {
                    $status = '已关闭';
                }
                $remark = '';
                if ($vo['freight_time']) $remark .= $vo['freight_time'] . "\r\n";
                /*
				if($invoice){
					if($remark) $remark .= "\r\n";
					if($invoice['type'] == 1){
						$remark.= $invoice['invoice_name'].' '.$invoice['mobile'].' '.$invoice['emall'];
					}else{
						if($invoice['name_type'] == 1){
							$remark.= $invoice['invoice_name'].' '.$invoice['tax_no'].' '.$invoice['mobile'].' '.$invoice['emall'];
						}else{
							$remark.= $invoice['invoice_name'].' '.$invoice['tax_no'].' '.$invoice['address'].' '.$invoice['tel'].' '.$invoice['bank_name'].' '.$invoice['bank_account'];
						}
					}
				}
				*/
                //配送自定义表单
                $vo['formdata'] = \app\model\Freight::getformdata($vo['id'], 'shop_order');
                if ($vo['formdata']) {
                    foreach ($vo['formdata'] as $formdata) {
                        if ($formdata[2] != 'upload') {
                            $remark .= $formdata[0] . ':' . $formdata[1] . "\r\n";
                        }
                    }
                }
                $data[] = [
                    ' ' . $vo['ordernum'],
                    $invoice ? $invoice['invoice_name'] : '',
                    $business['linkman'],
                    $business['linktel'],
                    $og['procode'],
                    $og['barcode'],
                    $og['name'],
                    $og['ggname'],
                    $og['num'],
                    $og['sell_price'],
                    $og['totalprice'],
                    in_array($vo['status'], [1, 2, 3]) ? $og['totalprice'] : 0,
                    $vo['paytype'],
                    $vo['area'] . ' ' . $vo['address'] . ',' . $vo['linkman'] . ',' . $vo['company'] . ',' . $vo['tel'],
                    $vo['freight_text'],
                    $remark
                ];
            }
        }
        $this->export_excel($title, $data);
    }


    //设置备注

    public function getdetail()
    {
        $orderid = input('param.orderid');
        if (bid != 0) {
            $order = Db::name('shop_order')->where('aid', aid)->where('bid', bid)->where('id', $orderid)->find();
        } else {
            $order = Db::name('shop_order')->where('aid', aid)->where('id', $orderid)->find();
        }
        if ($order['coupon_rid']) {
            $couponrecord = Db::name('coupon_record')->where('id', $order['coupon_rid'])->find();
        } else {
            $couponrecord = false;
        }
        $oglist = Db::name('shop_order_goods')->where('aid', aid)->where('orderid', $orderid)->select()->toArray();
        $member = Db::name('member')->field('id,nickname,headimg,realname,tel,wxopenid,unionid')->where('id', $order['mid'])->find();
        if (!$member) $member = ['id' => $order['mid'], 'nickname' => '', 'headimg' => ''];
        $comdata = array();
        $comdata['parent1'] = ['mid' => '', 'nickname' => '', 'headimg' => '', 'money' => 0, 'score' => 0];
        $comdata['parent2'] = ['mid' => '', 'nickname' => '', 'headimg' => '', 'money' => 0, 'score' => 0];
        $comdata['parent3'] = ['mid' => '', 'nickname' => '', 'headimg' => '', 'money' => 0, 'score' => 0];
        foreach ($oglist as $k=>$v) {
            if ($v['parent1']) {
                $parent1 = Db::name('member')->where('id', $v['parent1'])->find();
                $comdata['parent1']['mid'] = $v['parent1'];
                $comdata['parent1']['nickname'] = $parent1['nickname'];
                $comdata['parent1']['headimg'] = $parent1['headimg'];
                $comdata['parent1']['money'] += $v['parent1commission'];
                $comdata['parent1']['score'] += $v['parent1score'];
            }
            if ($v['parent2']) {
                $parent2 = Db::name('member')->where('id', $v['parent2'])->find();
                $comdata['parent2']['mid'] = $v['parent2'];
                $comdata['parent2']['nickname'] = $parent2['nickname'];
                $comdata['parent2']['headimg'] = $parent2['headimg'];
                $comdata['parent2']['money'] += $v['parent2commission'];
                $comdata['parent2']['score'] += $v['parent2score'];
            }
            if ($v['parent3']) {
                $parent3 = Db::name('member')->where('id', $v['parent3'])->find();
                $comdata['parent3']['mid'] = $v['parent3'];
                $comdata['parent3']['nickname'] = $parent3['nickname'];
                $comdata['parent3']['headimg'] = $parent3['headimg'];
                $comdata['parent3']['money'] += $v['parent3commission'];
                $comdata['parent3']['score'] += $v['parent3score'];
            }
           switch ($oglist[$k]['currency']) {
                case '1':
                    $oglist[$k]['sell_price'] = '¥ '.$oglist[$k]['sell_price'];
                    break;
                case '2':
                    $oglist[$k]['sell_price'] = 'NT$ '.$oglist[$k]['sell_price'];
                    break;
                case '3':
                    $oglist[$k]['sell_price'] = '$ '.$oglist[$k]['sell_price'];
                    break;
                case '4':
                    $oglist[$k]['sell_price'] = '₫ '.$oglist[$k]['sell_price'];
                    break;
                case '5':
                    $oglist[$k]['sell_price'] = '฿ '.$oglist[$k]['sell_price'];
                    break;
                case '6':
                    $oglist[$k]['sell_price'] = '₹ '.$oglist[$k]['sell_price'];
                    break;
                case '7':
                    $oglist[$k]['sell_price'] = 'RM '.$oglist[$k]['sell_price'];
                    break;
                default:
                    $oglist[$k]['sell_price'] = '¥ '.$oglist[$k]['sell_price'];
                    break;
            }
        }
        $comdata['parent1']['money'] = round($comdata['parent1']['money'], 2);
        $comdata['parent2']['money'] = round($comdata['parent2']['money'], 2);
        $comdata['parent3']['money'] = round($comdata['parent3']['money'], 2);

        $order['formdata'] = \app\model\Freight::getformdata($order['id'], 'shop_order');
        //弃用
        if ($order['field1']) {
            $order['field1data'] = explode('^_^', $order['field1']);
        }
        if ($order['field2']) {
            $order['field2data'] = explode('^_^', $order['field2']);
        }
        if ($order['field3']) {
            $order['field3data'] = explode('^_^', $order['field3']);
        }
        if ($order['field4']) {
            $order['field4data'] = explode('^_^', $order['field4']);
        }
        if ($order['field5']) {
            $order['field5data'] = explode('^_^', $order['field5']);
        }
        if ($order['freight_type'] == 11) {
            $order['freight_content'] = json_decode($order['freight_content'], true);
        }
        $miandanst = Db::name('admin_set')->where('aid', aid)->value('miandanst');
        if (bid == 0 && $miandanst == 1 && in_array('wx', $this->platform) && ($member['wxopenid'] || $member['unionid'])) { //可以使用小程序物流助手发货
            $canmiandan = 1;
        } else {
            $canmiandan = 0;
        }
        if ($order['checkmemid']) {
            $checkmember = Db::name('member')->field('id,nickname,headimg,realname,tel')->where('id', $order['checkmemid'])->find();
        } else {
            $checkmember = [];
        }

        $payorder = [];
        if ($order['paytypeid'] == 5) {
            $payorder = Db::name('payorder')->where('id', $order['payorderid'])->where('aid', aid)->find();
            if ($payorder) {
                if ($payorder['check_status'] === 0) {
                    $payorder['check_status_label'] = '待审核';
                } elseif ($payorder['check_status'] == 1) {
                    $payorder['check_status_label'] = '通过';
                } elseif ($payorder['check_status'] == 2) {
                    $payorder['check_status_label'] = '驳回';
                } else {
                    $payorder['check_status_label'] = '未上传';
                }
                if ($payorder['paypics']) {
                    $payorder['paypics'] = explode(',', $payorder['paypics']);
                    foreach ($payorder['paypics'] as $item) {
                        $payorder['paypics_html'] .= '<img src="' . $item . '" width="200" onclick="preview(this)"/>';
                    }
                }
            }
        }
        switch ($order['currency']) {
            case '1':
                $order['product_price'] = '¥ '.$order['product_price'];
                $order['totalprice'] = '¥ '.$order['totalprice'];
                break;
            case '2':
                $order['product_price'] = 'NT$ '.$order['product_price'];
                $order['totalprice'] = 'NT$ '.$order['totalprice'];
                break;
            case '3':
                $order['product_price'] = '$ '.$order['product_price'];
                $order['totalprice'] = '$ '.$order['totalprice'];
                break;
            case '4':
                $order['product_price'] = '₫ '.$order['product_price'];
                $order['totalprice'] = '₫ '.$order['totalprice'];
                break;
            case '5':
                $order['product_price'] = '฿ '.$order['product_price'];
                $order['totalprice'] = '฿ '.$order['totalprice'];
                break;
            case '6':
                $order['product_price'] = '₹ '.$order['product_price'];
                $order['totalprice'] = '₹ '.$order['totalprice'];
                break;
            case '7':
                $order['product_price'] = 'RM '.$order['product_price'];
                $order['totalprice'] = 'RM '.$order['totalprice'];
                break;
            default:
                $order['product_price'] = '¥ '.$order['product_price'];
                $order['totalprice'] = '¥ '.$order['totalprice'];
                break;
        }
        $order['member_address'] = Db::name('member_address')->where('id',$order['address_id'])->find();
        if ($order['express_content']) $order['express_content'] = json_decode($order['express_content'], true);
        return json(['order' => $order, 'couponrecord' => $couponrecord, 'oglist' => $oglist, 'member' => $member, 'comdata' => $comdata, 'canmiandan' => $canmiandan, 'checkmember' => $checkmember, 'payorder' => $payorder]);
    }
    public function changeUserAddress(){
        $orderid = input('post.orderid/d');
        if (bid == 0) {
           $order =  Db::name('shop_order')->where('aid', aid)->where('id', $orderid)->find();
        } else {
            $order = Db::name('shop_order')->where('aid', aid)->where('bid', bid)->where('id', $orderid)->find();
        }
        if ($order['address_id']>0) {
            Db::name('member_address')->where('id',$order['address_id'])->update([
                'name'=>input('user_name'), 
                'tel'=>input('user_tel'), 
                'province'=>input('provice'), 
                'city'=>input('city'), 
                'district'=>input('district'), 
                'area'=>input('area'), 
                'address'=>input('address'), 
            ]);
        }else{
            $insertId = Db::name('member_address')->insertGetId([
                'name'=>input('user_name'), 
                'tel'=>input('user_tel'), 
                'province'=>input('provice'), 
                'city'=>input('city'), 
                'district'=>input('district'), 
                'area'=>input('area'), 
                'address'=>input('address'), 
                'mid' => $order['mid'],
                'notes'=>'',
                'aid'=>aid
            ]);
            if (bid == 0) {
                $order =  Db::name('shop_order')->where('aid', aid)->where('id', $orderid)->update([
                    'address_id'=>$insertId
                ]);
             } else {
                 $order = Db::name('shop_order')->where('aid', aid)->where('bid', bid)->where('id', $orderid)->update([
                    'address_id'=>$insertId
                ]);
             }
        }
        \app\common\System::plog('商城订单修改收货地址' . $orderid);
        return json(['status' => 1, 'msg' => '设置完成']);
    }
    //改备注

    public function setremark()
    {
        $orderid = input('post.orderid/d');
        $content = input('post.content');
        if (bid == 0) {
            Db::name('shop_order')->where('aid', aid)->where('id', $orderid)->update(['remark' => $content]);
        } else {
            Db::name('shop_order')->where('aid', aid)->where('bid', bid)->where('id', $orderid)->update(['remark' => $content]);
        }
        \app\common\System::plog('商城订单设置备注' . $orderid);
        return json(['status' => 1, 'msg' => '设置完成']);
    }
    
    public function setwuliuremark()
    {
        $orderid = input('post.orderid/d');
        $content = input('post.content');
        $mid = Db::name('shop_order')->where('id', $orderid)->value('mid');
        $remark['aid'] = aid;
        $remark['bid'] = bid;
        $remark['mid'] = $mid;
        $remark['orderid'] = $orderid;
        $remark['remark'] = $content;
        $remark['createtime'] = time();
        Db::name('wuliu_remark')->insert($remark);
        \app\common\System::plog('商城订单设置物流备注' . $orderid);
        return json(['status' => 1, 'msg' => '设置完成']);
    }
    
    public function setpackremark()
    {
        $packnum = input('post.packnum');
        $content = input('post.content');
        $name = input('post.name');
        
        $id = Db::name('wuliu_remark')->where('packnum', $packnum)->value('id');
        if(!$id){
            return json(['status' => 0, 'msg' => '该单号的物流信息不存在']);
        }
        
        $remark['aid'] = aid;
        $remark['bid'] = bid;
        $remark['packnum'] = $packnum;
        $remark['remark'] = $content;
        $remark['name'] = $name;
        $remark['createtime'] = time();
        Db::name('wuliu_remark')->insert($remark);
        \app\common\System::plog('商城订单设置物流备注' . $packnum);
        return json(['status' => 1, 'msg' => '设置完成']);
    }
    
    public function setgoodstype()
    {
        $orderid = input('post.orderid/d');
        $data['goodstype'] = input('post.content');
        $data['weight'] = input('post.weight');
        $data['length'] = input('post.length');
        $data['width'] = input('post.width');
        $data['height'] = input('post.height');
        if (bid == 0) {
            Db::name('shop_order')->where('aid', aid)->where('id', $orderid)->update($data);
        } else {
            Db::name('shop_order')->where('aid', aid)->where('bid', bid)->where('id', $orderid)->update($data);
        }
        $order = Db::name('shop_order')->where('id',$orderid)->find();
        //计算价格
        $total = 0;
        $res = Db::name('mendian')->where('id',$order['mdid'])->find();
        if($res && $res['specs']){
            $items = [];
            $specs = json_decode($res['specs'], true);
            foreach($specs as $k=>$v){
              array_push($items,$v['items'][0]);
            }
            $volumeprice = 0;
            $weightprice = 0;
            $minprice = 0;
            $transport = Db::name('shop_order_goods')->where('orderid',$order['id'])->value('name');
            foreach ($items as $key => $value) {
              if($value['transport']==$transport&&$value['goodstype']==$data['goodstype']){
                $volumeprice = $value['volumeprice'];
                $weightprice = $value['weightprice'];
                $minprice = $value['minprice'];
              }
            }
          if($volumeprice!=0&&$weightprice!=0){
            if($transport=='海运公斤'||$transport=='陆运公斤'){
                $wprice = $weightprice * $data['weight'];
                $vprice = (($data['length'] * $data['width'] * $data['height'])/6000)*$volumeprice;
                $final = $wprice > $vprice ? $wprice : $vprice;
                $total = $final<$minprice?$minprice:$final;
            } else {
              //立方计算
              $total = (($data['length'] * $data['width'] * $data['height'])/1000000)*$volumeprice;
            }
            $total = number_format($total, 3);
          }          
        }
        Db::name('shop_order_goods')->where('orderid',$order['id'])->update(['sell_price'=>$total]);
        $orderdata['product_price'] = $total;
        $orderdata['totalprice'] = $total;
        // dd($order['id']);
        Db::name('shop_order')->where('id', $order['id'])->update($orderdata);
        Db::name('payorder')->where('orderid',$order['id'])->update([
            'money'=>$total
        ]);
        \app\common\System::plog('商城订单设置' . $orderid);
        return json(['status' => 1, 'msg' => '设置完成']);
    }
    public function changeAddress()
    {
        $orderid = input('post.orderid/d');
        $area = input('post.area');
        $address = input('post.address');
        if (bid == 0) {
            Db::name('shop_order')->where('aid', aid)->where('id', $orderid)->update(['area' => $area, 'address' => $address]);
        } else {
            Db::name('shop_order')->where('aid', aid)->where('bid', bid)->where('id', $orderid)->update(['area' => $area, 'address' => $address]);
        }
        return json(['status' => 1, 'msg' => '修改完成']);
    }
    //关闭订单

    public function changeprice()
    {
        $orderid = input('post.orderid/d');
        $newPrice = input('post.newprice');
        $newprice = str_replace('¥ ','',$newPrice);

        $newordernum = date('ymdHis') . rand(100000, 999999);
        if (bid == 0) {
            Db::name('shop_order')->where('aid', aid)->where('id', $orderid)->update(['totalprice' => $newprice, 'ordernum' => $newordernum]);
            Db::name('shop_order_goods')->where('aid', aid)->where('orderid', $orderid)->update(['ordernum' => $newordernum]);
        } else {
            Db::name('shop_order')->where('aid', aid)->where('bid', bid)->where('id', $orderid)->update(['totalprice' => $newprice, 'ordernum' => $newordernum]);
            Db::name('shop_order_goods')->where('aid', aid)->where('bid', bid)->where('orderid', $orderid)->update(['ordernum' => $newordernum]);
        }

        $payorderid = Db::name('shop_order')->where('aid', aid)->where('id', $orderid)->value('payorderid');

        \app\model\Payorder::updateorder($payorderid, $newordernum, $newprice);
        \app\common\System::plog('商城订单改价格' . $orderid);
        return json(['status' => 1, 'msg' => '修改完成']);
    }

    //改为已支付

    public function closeOrder()
    {
        $orderid = input('post.orderid/d');
        if (bid == 0) {
            $order = Db::name('shop_order')->where('id', $orderid)->where('aid', aid)->find();
        } else {
            $order = Db::name('shop_order')->where('id', $orderid)->where('aid', aid)->where('bid', bid)->find();
        }
        if (!$order || $order['status'] != 0) {
            return json(['status' => 0, 'msg' => '关闭失败,订单状态错误']);
        }
        //加库存
        $oglist = Db::name('shop_order_goods')->where('aid', aid)->where('orderid', $orderid)->select()->toArray();
        foreach ($oglist as $og) {
            Db::name('shop_guige')->where('aid', aid)->where('id', $og['ggid'])->update(['stock' => Db::raw("stock+" . $og['num']), 'sales' => Db::raw("sales-" . $og['num'])]);
            Db::name('shop_product')->where('aid', aid)->where('id', $og['proid'])->update(['stock' => Db::raw("stock+" . $og['num']), 'sales' => Db::raw("sales-" . $og['num'])]);
            if (getcustom('guige_split')) {
                \app\model\ShopProduct::addlinkstock($og['proid'], $og['ggid'], $og['num']);
            }
        }

        //优惠券抵扣的返还
        if ($order['coupon_rid'] > 0) {
            Db::name('coupon_record')->where('aid', aid)->where(['mid' => $order['mid'], 'id' => $order['coupon_rid']])->update(['status' => 0, 'usetime' => '']);
        }
        $rs = Db::name('shop_order')->where('id', $orderid)->where('aid', aid)->update(['status' => 4]);
        Db::name('shop_order_goods')->where('orderid', $orderid)->where('aid', aid)->update(['status' => 4]);
        \app\common\System::plog('商城订单关闭订单' . $orderid);
        return json(['status' => 1, 'msg' => '操作成功']);
    }

    //发货

    public function ispay()
    {
        if (bid > 0) showmsg('无权限操作');
        $orderid = input('post.orderid/d');
        if (bid == 0) {
            Db::name('shop_order')->where('aid', aid)->where('id', $orderid)->update(['status' => 1, 'paytime' => time(), 'paytype' => '后台支付']);
        } else {
            Db::name('shop_order')->where('aid', aid)->where('bid', bid)->where('id', $orderid)->update(['status' => 1, 'paytime' => time(), 'paytype' => '后台支付']);
        }
        \app\model\Payorder::shop_pay($orderid);

        //Db::name('shop_order_goods')->where('orderid',$orderid)->where('aid',aid)->where('bid',bid)->update(['status'=>1]);
        ////奖励积分
        //$order = Db::name('shop_order')->where('aid',aid)->where('bid',bid)->where('id',$orderid)->find();
        //if($order['givescore'] > 0){
        //	\app\common\Member::addscore(aid,$order['mid'],$order['givescore'],'购买产品奖励'.t('积分'));
        //}
        \app\common\System::plog('商城订单改为已支付' . $orderid);
        return json(['status' => 1, 'msg' => '操作成功']);
    }

    //批量发货

    public function sendExpress()
    {
        $orderid = input('post.orderid/d');
        if (bid == 0) {
            $order = Db::name('shop_order')->where('aid', aid)->where('id', $orderid)->find();
        } else {
            $order = Db::name('shop_order')->where('aid', aid)->where('bid', bid)->where('id', $orderid)->find();
        }

        //如果选择了配送时间，未到配送时间内不可以进行配送
        if (getcustom('business_withdraw')) {
            if ($order['freight_time']) {
                $freight_time = explode('~', $order['freight_time']);
                $begin_time = strtotime($freight_time[0]);
                $date = explode(' ', $freight_time[0]);
                $end_time = strtotime($date[0] . ' ' . $freight_time[1]);
                if (time() < $begin_time || (time > $end_time)) {
                    return json(['status' => 0, 'msg' => '未在配送时间范围内']);
                }
            }
        }

        if ($order['status'] != 1 && $order['status'] != 2) {
            return json(['status' => 0, 'msg' => '该订单状态不允许发货']);
        }
        $expres_content = '';
        if ($order['freight_type'] == 10) {
            $pic = input('post.pic');
            $fhname = input('post.fhname');
            $fhaddress = input('post.fhaddress');
            $shname = input('post.shname');
            $shaddress = input('post.shaddress');
            $remark = input('post.remark');
            $data = [];
            $data['aid'] = aid;
            $data['pic'] = $pic;
            $data['fhname'] = $fhname;
            $data['fhaddress'] = $fhaddress;
            $data['shname'] = $shname;
            $data['shaddress'] = $shaddress;
            $data['remark'] = $remark;
            $data['createtime'] = time();
            $id = Db::name('freight_type10_record')->insertGetId($data);
            $express_com = '货运托运';
            $express_no = $id;
        } else {
            $express_comArr = input('post.express_com/a');
            $express_noArr = input('post.express_no/a');
            if (count($express_comArr) > 1) {
                $express_com = '多单发货';
                $express_no = '';
                $express_content = [];
                foreach ($express_comArr as $k => $v) {
                    $express_content[] = ['express_com' => $v, 'express_no' => $express_noArr[$k]];
                }
                $express_content = jsonEncode($express_content);
            } else {
                $express_com = $express_comArr[0];
                $express_no = $express_noArr[0];
            }
        }

        if ($order['status'] != 1) { //修改物流信息
            Db::name('shop_order')->where('aid', aid)->where('id', $orderid)->update(['express_com' => $express_com, 'express_no' => $express_no, 'express_content' => $express_content]);
            return json(['status' => 1, 'msg' => '操作成功']);
        }

        Db::name('shop_order')->where('aid', aid)->where('id', $orderid)->update(['express_com' => $express_com, 'express_no' => $express_no, 'express_content' => $express_content, 'send_time' => time(), 'status' => 2]);
        Db::name('shop_order_goods')->where('orderid', $orderid)->where('aid', aid)->update(['status' => 2]);

        if ($order['fromwxvideo'] == 1) {
            \app\common\Wxvideo::deliverysend($orderid);
        }


        if (getcustom('cefang') && aid == 2) { //定制1 订单对接 同步到策方
            $order['status'] = 2;
            \app\custom\Cefang::api($order);
        }

        //订单发货通知
        $tmplcontent = [];
        $tmplcontent['first'] = '您的订单已发货';
        $tmplcontent['remark'] = '请点击查看详情~';
        $tmplcontent['keyword1'] = $order['title'];
        $tmplcontent['keyword2'] = $express_com;
        $tmplcontent['keyword3'] = $express_no;
        $tmplcontent['keyword4'] = $order['linkman'] . ' ' . $order['tel'];
        \app\common\Wechat::sendtmpl(aid, $order['mid'], 'tmpl_orderfahuo', $tmplcontent, m_url('pages/my/usercenter'));
        //订阅消息
        $tmplcontent = [];
        $tmplcontent['thing2'] = $order['title'];
        $tmplcontent['thing7'] = $express_com;
        $tmplcontent['character_string4'] = $express_no;
        $tmplcontent['thing11'] = $order['address'];

        $tmplcontentnew = [];
        $tmplcontentnew['thing29'] = $order['title'];
        $tmplcontentnew['thing1'] = $express_com;
        $tmplcontentnew['character_string2'] = $express_no;
        $tmplcontentnew['thing9'] = $order['address'];
        \app\common\Wechat::sendwxtmpl(aid, $order['mid'], 'tmpl_orderfahuo', $tmplcontentnew, 'pages/my/usercenter', $tmplcontent);

        //短信通知
        $member = Db::name('member')->where('id', $order['mid'])->find();
        if ($member['tel']) {
            $tel = $member['tel'];
        } else {
            $tel = $order['tel'];
        }
        $rs = \app\common\Sms::send(aid, $tel, 'tmpl_orderfahuo', ['ordernum' => $order['ordernum'], 'express_com' => $express_com, 'express_no' => $express_no]);

        \app\common\System::plog('商城订单发货' . $orderid);
        return json(['status' => 1, 'msg' => '操作成功']);
    }

    //批量发货-云仓

    public function plfh()
    {
        $express_com = input('post.plfh_express');
        $file = input('post.plfh_file');
        $exceldata = $this->import_excel($file);
        //dump($exceldata);
        $countnum = count($exceldata);
        $successnum = 0;
        $errornum = 0;
        foreach ($exceldata as $v) {
            $ordernum = trim($v[0]);
            $express_no = $v[1];
            $order = Db::name('shop_order')->where('aid', aid)->where('bid', bid)->where('ordernum', $ordernum)->find();
            if (!$order || $order['status'] != 1 && $order['status'] != 2) {
                $errornum++;
                continue;
            }
            $orderid = $order['id'];
            Db::name('shop_order')->where('aid', aid)->where('bid', bid)->where('id', $orderid)->update(['express_com' => $express_com, 'express_no' => $express_no, 'send_time' => time(), 'status' => 2]);
            Db::name('shop_order_goods')->where('orderid', $orderid)->where('aid', aid)->where('bid', bid)->update(['status' => 2]);

            if ($order['fromwxvideo'] == 1) {
                \app\common\Wxvideo::deliverysend($orderid);
            }


            if (getcustom('cefang') && aid == 2) { //定制1 订单对接 同步到策方
                $order['status'] = 2;
                \app\custom\Cefang::api($order);
            }
            //订单发货通知
            $tmplcontent = [];
            $tmplcontent['first'] = '您的订单已发货';
            $tmplcontent['remark'] = '请点击查看详情~';
            $tmplcontent['keyword1'] = $order['title'];
            $tmplcontent['keyword2'] = $express_com;
            $tmplcontent['keyword3'] = $express_no;
            $tmplcontent['keyword4'] = $order['linkman'] . ' ' . $order['tel'];
            \app\common\Wechat::sendtmpl(aid, $order['mid'], 'tmpl_orderfahuo', $tmplcontent, m_url('pages/my/usercenter'));
            //订阅消息
            $tmplcontent = [];
            $tmplcontent['thing2'] = $order['title'];
            $tmplcontent['thing7'] = $express_com;
            $tmplcontent['character_string4'] = $express_no;
            $tmplcontent['thing11'] = $order['address'];

            $tmplcontentnew = [];
            $tmplcontentnew['thing29'] = $order['title'];
            $tmplcontentnew['thing1'] = $express_com;
            $tmplcontentnew['character_string2'] = $express_no;
            $tmplcontentnew['thing9'] = $order['address'];
            \app\common\Wechat::sendwxtmpl(aid, $order['mid'], 'tmpl_orderfahuo', $tmplcontentnew, 'pages/my/usercenter', $tmplcontent);

            //短信通知
            $member = Db::name('member')->where('id', $order['mid'])->find();
            if ($member['tel']) {
                $tel = $member['tel'];
            } else {
                $tel = $order['tel'];
            }
            $rs = \app\common\Sms::send(aid, $tel, 'tmpl_orderfahuo', ['ordernum' => $order['ordernum'], 'express_com' => $express_com, 'express_no' => $express_no]);
            $successnum++;
        }
        \app\common\System::plog('商城订单批量发货');
        return json(['status' => 1, 'msg' => '共导入 ' . $countnum . ' 条数据，成功发货 ' . $successnum . ' 条，失败 ' . $errornum . ' 条']);
    }

    //查物流

    public function plfhyc()
    {
        $file = input('post.plfhyc_file');
        $exceldata = $this->import_excel($file);
//        dd($exceldata);
        $countnum = count($exceldata);
        $successnum = 0;
        $errornum = 0;
        foreach ($exceldata as $v) {
            $ordernum = $v[1];
            $express_com = $v[4];
            $express_no = $v[5];
            $order = Db::name('shop_order')->where('aid', aid)->where('bid', bid)->where('ordernum', $ordernum)->find();
            if (!$order || $order['status'] != 1 && $order['status'] != 2) {
                $errornum++;
                continue;
            }
            $orderid = $order['id'];
            Db::name('shop_order')->where('aid', aid)->where('bid', bid)->where('id', $orderid)->update(['express_com' => $express_com, 'express_no' => $express_no, 'send_time' => time(), 'status' => 2]);
            Db::name('shop_order_goods')->where('orderid', $orderid)->where('aid', aid)->where('bid', bid)->update(['status' => 2]);

            if ($order['fromwxvideo'] == 1) {
                \app\common\Wxvideo::deliverysend($orderid);
            }

            //订单发货通知
            $tmplcontent = [];
            $tmplcontent['first'] = '您的订单已发货';
            $tmplcontent['remark'] = '请点击查看详情~';
            $tmplcontent['keyword1'] = $order['title'];
            $tmplcontent['keyword2'] = $express_com;
            $tmplcontent['keyword3'] = $express_no;
            $tmplcontent['keyword4'] = $order['linkman'] . ' ' . $order['tel'];
            \app\common\Wechat::sendtmpl(aid, $order['mid'], 'tmpl_orderfahuo', $tmplcontent, m_url('pages/my/usercenter'));
            //订阅消息
            $tmplcontent = [];
            $tmplcontent['thing2'] = $order['title'];
            $tmplcontent['thing7'] = $express_com;
            $tmplcontent['character_string4'] = $express_no;
            $tmplcontent['thing11'] = $order['address'];

            $tmplcontentnew = [];
            $tmplcontentnew['thing29'] = $order['title'];
            $tmplcontentnew['thing1'] = $express_com;
            $tmplcontentnew['character_string2'] = $express_no;
            $tmplcontentnew['thing9'] = $order['address'];
            \app\common\Wechat::sendwxtmpl(aid, $order['mid'], 'tmpl_orderfahuo', $tmplcontentnew, 'pages/my/usercenter', $tmplcontent);

            //短信通知
            $member = Db::name('member')->where('id', $order['mid'])->find();
            if ($member['tel']) {
                $tel = $member['tel'];
            } else {
                $tel = $order['tel'];
            }
            $rs = \app\common\Sms::send(aid, $tel, 'tmpl_orderfahuo', ['ordernum' => $order['ordernum'], 'express_com' => $express_com, 'express_no' => $express_no]);
            $successnum++;
        }
        \app\common\System::plog('商城订单批量发货');
        return json(['status' => 1, 'msg' => '共导入 ' . $countnum . ' 条数据，成功发货 ' . $successnum . ' 条，失败 ' . $errornum . ' 条']);
    }

    //付款审核

    public function getExpress()
    {
        $orderid = input('post.orderid/d');
        $order = Db::name('shop_order')->where('id', $orderid)->where('aid', aid)->find();
        if ($order['freight_type'] == '10') {
            $data = Db::name('freight_type10_record')->where('id', $order['express_no'])->find();
            return json(['status' => 1, 'data' => $data]);
        }
        if ($order['express_content']) {
            $expressArr = json_decode($order['express_content'], true);
            foreach ($expressArr as $order) {
                if ($order['express_com'] == '顺丰速运') {
                    $totel = $order['tel'];
                    $order['express_no'] = $order['express_no'] . ":" . substr($totel, -4);
                }
                $list[] = [
                    'express_no' => $order['express_no'],
                    'express_com' => $order['express_com'],
                    'express_data' => \app\common\Common::getwuliu($order['express_no'], $order['express_com'], $order['express_type'])
                ];
            }

        } else {
            if ($order['express_com'] == '顺丰速运') {
                $totel = $order['tel'];
                $order['express_no'] = $order['express_no'] . ":" . substr($totel, -4);
            }
            $list[] = [
                'express_no' => $order['express_no'],
                'express_com' => $order['express_com'],
                'express_data' => \app\common\Common::getwuliu($order['express_no'], $order['express_com'], $order['express_type'])
            ];
        }

        return json(['status' => 1, 'data' => $list]);
    }

    //退款审核

    public function payCheck()
    {
        $orderid = input('post.orderid/d');
        $st = input('post.st/d');
        $remark = input('post.remark');
        $order = Db::name('shop_order')->where('id', $orderid)->where('aid', aid)->where('bid', bid)->find();
        if ($st == 2) {
            Db::name('payorder')->where('id', $order['payorderid'])->where('aid', aid)->where('bid', bid)->update(['check_status' => 2, 'check_remark' => $remark]);

            \app\common\System::plog('商城订单付款审核驳回' . $orderid);
            return json(['status' => 1, 'msg' => '付款已驳回']);
        } elseif ($st == 1) {
            if ($order['status'] != 0) {
                return json(['status' => 0, 'msg' => '该订单状态不允许审核付款']);
            }

            \app\model\Payorder::payorder($order['payorderid'], '转账汇款', 5, '');
            Db::name('payorder')->where('id', $order['payorderid'])->where('aid', aid)->where('bid', bid)->update(['check_status' => 1, 'check_remark' => $remark]);

            Db::name('shop_order')->where('id', $orderid)->where('aid', aid)->where('bid', bid)->update(['status' => 1, 'paytime' => time()]);

            \app\common\System::plog('商城订单付款审核通过' . $orderid);
            return json(['status' => 1, 'msg' => '审核通过']);
        }
    }

    //退款

    public function refundCheck()
    {
        $orderid = input('post.orderid/d');
        $st = input('post.st/d');
        $remark = input('post.remark');
        if (bid == 0) {
            $order = Db::name('shop_order')->where('id', $orderid)->where('aid', aid)->find();
        } else {
            $order = Db::name('shop_order')->where('id', $orderid)->where('aid', aid)->where('bid', bid)->find();
        }
        if (!$order) return json(['status' => 1, 'msg' => '订单不存在']);
        if ($st == 2) {
            Db::name('shop_order')->where('id', $orderid)->where('aid', aid)->update(['refund_status' => 3, 'refund_checkremark' => $remark]);
            //退款申请驳回通知
            $tmplcontent = [];
            $tmplcontent['first'] = '您的退款申请被商家驳回，可与商家协商沟通。';
            $tmplcontent['remark'] = $remark . '，请点击查看详情~';
            $tmplcontent['orderProductPrice'] = $order['refund_money'] . '元';
            $tmplcontent['orderProductName'] = $order['title'];
            $tmplcontent['orderName'] = $order['ordernum'];
            \app\common\Wechat::sendtmpl(aid, $order['mid'], 'tmpl_tuierror', $tmplcontent, m_url('pages/my/usercenter'));
            //订阅消息
            $tmplcontent = [];
            $tmplcontent['amount3'] = $order['refund_money'];
            $tmplcontent['thing2'] = $order['title'];
            $tmplcontent['character_string1'] = $order['ordernum'];

            $tmplcontentnew = [];
            $tmplcontentnew['amount3'] = $order['refund_money'];
            $tmplcontentnew['thing8'] = $order['title'];
            $tmplcontentnew['character_string4'] = $order['ordernum'];
            \app\common\Wechat::sendwxtmpl(aid, $order['mid'], 'tmpl_tuierror', $tmplcontentnew, 'pages/my/usercenter', $tmplcontent);
            //短信通知
            $member = Db::name('member')->where('id', $order['mid'])->find();
            if ($member['tel']) {
                $tel = $member['tel'];
            } else {
                $tel = $order['tel'];
            }
            $rs = \app\common\Sms::send(aid, $tel, 'tmpl_tuierror', ['ordernum' => $order['ordernum'], 'reason' => $remark]);
            \app\common\System::plog('商城订单退款驳回' . $orderid);
            return json(['status' => 1, 'msg' => '退款已驳回']);
        } elseif ($st == 1) {
            if ($order['status'] != 1 && $order['status'] != 2) {
                return json(['status' => 0, 'msg' => '该订单状态不允许退款']);
            }
            if ($order['refund_money'] > 0) {
                $rs = \app\common\Order::refund($order, $order['refund_money'], $order['refund_reason']);
                if ($rs['status'] == 0) {
                    return json(['status' => 0, 'msg' => $rs['msg']]);
                }
            }

            Db::name('shop_order')->where('id', $orderid)->where('aid', aid)->update(['status' => 4, 'refund_status' => 2, 'refund_checkremark' => $remark]);
            Db::name('shop_order_goods')->where('orderid', $orderid)->where('aid', aid)->update(['status' => 4]);

            //积分抵扣的返还
            if ($order['scoredkscore'] > 0) {
                \app\common\Member::addscore(aid, $order['mid'], $order['scoredkscore'], '订单退款返还');
            }
            //优惠券抵扣的返还
            if ($order['coupon_rid'] > 0) {
                Db::name('coupon_record')->where('aid', aid)->where(['mid' => $order['mid'], 'id' => $order['coupon_rid']])->update(['status' => 0, 'usetime' => '']);
            }

            if (getcustom('cefang') && aid == 2) { //定制1 订单对接 同步到策方
                $order['status'] = 4;
                \app\custom\Cefang::api($order);
            }

            if (getcustom('hmy_yuyue')) {
                if ($order['sysOrderNo']) {
                    $rs = \app\custom\Yuyue::refund($order);
                }
            }

            //退款成功通知
            $tmplcontent = [];
            $tmplcontent['first'] = '您的订单已经完成退款，¥' . $order['refund_money'] . '已经退回您的付款账户，请留意查收。';
            $tmplcontent['remark'] = $remark . '，请点击查看详情~';
            $tmplcontent['orderProductPrice'] = $order['refund_money'] . '元';
            $tmplcontent['orderProductName'] = $order['title'];
            $tmplcontent['orderName'] = $order['ordernum'];
            \app\common\Wechat::sendtmpl(aid, $order['mid'], 'tmpl_tuisuccess', $tmplcontent, m_url('pages/my/usercenter'));
            //订阅消息
            $tmplcontent = [];
            $tmplcontent['amount6'] = $order['refund_money'];
            $tmplcontent['thing3'] = $order['title'];
            $tmplcontent['character_string2'] = $order['ordernum'];

            $tmplcontentnew = [];
            $tmplcontentnew['amount3'] = $order['refund_money'];
            $tmplcontentnew['thing6'] = $order['title'];
            $tmplcontentnew['character_string4'] = $order['ordernum'];
            \app\common\Wechat::sendwxtmpl(aid, $order['mid'], 'tmpl_tuisuccess', $tmplcontentnew, 'pages/my/usercenter', $tmplcontent);

            //短信通知
            $member = Db::name('member')->where('id', $order['mid'])->find();
            if ($member['tel']) {
                $tel = $member['tel'];
            } else {
                $tel = $order['tel'];
            }
            $rs = \app\common\Sms::send(aid, $tel, 'tmpl_tuisuccess', ['ordernum' => $order['ordernum'], 'money' => $order['refund_money']]);

            \app\common\System::plog('商城订单退款审核通过并退款' . $orderid);
            return json(['status' => 1, 'msg' => '已退款成功']);
        }
    }

    //退款

    public function refund()
    {
        $orderid = input('post.orderid/d');
        $reason = input('post.reason');
        if (bid == 0) {
            $order = Db::name('shop_order')->where('id', $orderid)->where('aid', aid)->find();
        } else {
            $order = Db::name('shop_order')->where('id', $orderid)->where('aid', aid)->where('bid', bid)->find();
        }
        if (!$order) return json(['status' => 0, 'msg' => '订单不存在']);
        if ($order['status'] != 1 && $order['status'] != 2) {
            return json(['status' => 0, 'msg' => '该订单状态不允许退款']);
        }
        $refundingMoney = Db::name('shop_refund_order')->where('orderid', $order['id'])->where('aid', aid)->whereIn('refund_status', [1, 4])->sum('refund_money');
        if ($refundingMoney) {
            return json(['status' => 0, 'msg' => '请先处理完进行中的退款单']);
        }
//        $refundedMoney = Db::name('shop_refund_order')->where('orderid',$order['id'])->where('aid',aid)->where('bid',bid)->where('refund_status',2)->sum('refund_money');
//        $refund_money -= $refundedMoney;

        //新退款 202108
        $post = input('post.');
        $orderid = intval($post['orderid']);
        $money = floatval($post['money']);
        $refundNum = $post['refundNum'];
        $order = Db::name('shop_order')->where('aid', aid)->where('id', $orderid)->find();
        if (!$order || ($order['status'] != 1 && $order['status'] != 2)) {
            return json(['status' => 0, 'msg' => '订单状态不符合退款要求']);
        }
        if ($money < 0 || $money > $order['totalprice']) {
            return json(['status' => 0, 'msg' => '退款金额有误']);
        }
        if (empty($refundNum)) {
            return json(['status' => 0, 'msg' => '请选择退款的商品']);
        }

        $totalRefundNum = 0;
        $returnTotalprice = 0;
        $prolist = Db::name('shop_order_goods')->where('orderid', $orderid)->select();
        $newKey = 'id';
        $prolist = $prolist->dictionary(null, $newKey);
        $ogids = array_keys($prolist);

        $refundMoneySum = Db::name('shop_refund_order')->where('orderid', $orderid)->where('aid', aid)->whereIn('refund_status', [1, 2, 4])->sum('refund_money');

        $canRefundNum = 0;
        $totalNum = 0;
        $canRefundProductPrice = 0;
        $canRefundTotalprice = 0;
        foreach ($prolist as $key => $item) {
            $prolist[$key]['canRefundNum'] = $item['num'] - $item['refund_num'];
            $totalNum += $item['num'];
            $canRefundNum += $item['num'] - $item['refund_num'];
            $canRefundProductPrice += $item['real_totalprice'] / $item['num'] * ($item['num'] - $item['refund_num']);
        }

        foreach ($refundNum as $item) {
            if (!in_array($item['ogid'], $ogids)) {
                return json(['status' => 0, 'msg' => '退款商品不存在']);
            }
            if ($item['num'] > $prolist[$item['ogid']]['num'] - $prolist[$item['ogid']]['refund_num']) {
                return json(['status' => 0, 'msg' => $prolist[$item['ogid']]['name'] . '退款数量超出范围']);
            }
            $totalRefundNum += $item['num'];
//            $returnTotalprice += $prolist[$item['ogid']]['real_totalprice'] / $prolist[$item['ogid']]['num'] * $item['num'];
        }
        if ($totalRefundNum == 0) {
            return json(['status' => 0, 'msg' => '请选择退款的商品']);
        }
        if ($canRefundNum == $totalNum && $totalNum == $totalRefundNum) {
            $canRefundTotalprice = $order['totalprice'];
        } else {
            $canRefundTotalprice = $order['totalprice'] - $refundMoneySum;
        }

        if ($money > $canRefundTotalprice) {
            return json(['status' => 0, 'msg' => '退款金额超出范围']);
        }

        $data = [
            'aid' => $order['aid'],
            'bid' => $order['bid'],
            'mdid' => $order['mdid'],
            'mid' => $order['mid'],
            'orderid' => $order['id'],
            'ordernum' => $order['ordernum'],
            'refund_type' => 'refund',
            'refund_ordernum' => '' . date('ymdHis') . rand(100000, 999999),
            'refund_money' => $money,
            'refund_reason' => '后台退款：' . $post['reason'],
            'refund_pics' => '',
            'createtime' => time(),
            'refund_time' => time(),
            'refund_status' => 2,
            'platform' => platform,
        ];
        $refund_id = Db::name('shop_refund_order')->insertGetId($data);


        if ($order['fromwxvideo'] == 1) {
            \app\common\Wxvideo::aftersaleadd($order['id'], $refund_id);
        }
        if ($data['refund_money'] > 0) {
            $rs = \app\common\Order::refund($order, $data['refund_money'], $reason);
            if ($rs['status'] == 0) {
                return json(['status' => 0, 'msg' => $rs['msg']]);
            }
        }

        foreach ($refundNum as $item) {
            if ($item['num'] < 1) continue;
            $od = [
                'aid' => $order['aid'],
                'bid' => $order['bid'],
                'mid' => $order['mid'],
                'orderid' => $order['id'],
                'ordernum' => $order['ordernum'],
                'refund_orderid' => $refund_id,
                'refund_ordernum' => $data['refund_ordernum'],
                'refund_num' => $item['num'],
                'refund_money' => $item['num'] * $prolist[$item['ogid']]['real_totalprice'] / $prolist[$item['ogid']]['num'],
                'ogid' => $item['ogid'],
                'proid' => $prolist[$item['ogid']]['proid'],
                'name' => $prolist[$item['ogid']]['name'],
                'pic' => $prolist[$item['ogid']]['pic'],
                'procode' => $prolist[$item['ogid']]['procode'],
                'ggid' => $prolist[$item['ogid']]['ggid'],
                'ggname' => $prolist[$item['ogid']]['ggname'],
                'cid' => $prolist[$item['ogid']]['cid'],
                'cost_price' => $prolist[$item['ogid']]['cost_price'],
                'sell_price' => $prolist[$item['ogid']]['sell_price'],
                'createtime' => time()
            ];
            Db::name('shop_refund_order_goods')->insertGetId($od);
            Db::name('shop_order_goods')->where('aid', aid)->where('id', $item['ogid'])->inc('refund_num', $item['num'])->update();
        }

//        $order_goods = Db::name('shop_order_goods')->where('orderid',$orderid)->where('aid',aid)->where('bid',bid)->fieldRaw('ggid,proid,num,refund_num, num-refund_num as true_num')->select()->toArray();
//		Db::name('shop_order')->where('id',$orderid)->where('aid',aid)->where('bid',bid)->update(['status'=>4,'refund_status'=>2,'refund_money'=>$refund_money,'refund_reason'=>$reason]);
//		Db::name('shop_order_goods')->where('orderid',$orderid)->where('aid',aid)->where('bid',bid)->update(['status'=>4,'refund_num' => Db::raw('num')]);

        //恢复库存 删除销量
        foreach ($refundNum as $item) {
            Db::name('shop_guige')->where('aid', aid)->where('id', $prolist[$item['ogid']]['ggid'])->update(['stock' => Db::raw("stock+" . $item['num']), 'sales' => Db::raw("sales-" . $item['num'])]);
            Db::name('shop_product')->where('aid', aid)->where('id', $prolist[$item['ogid']]['proid'])->update(['stock' => Db::raw("stock+" . $item['num']), 'sales' => Db::raw("sales-" . $item['num'])]);

            if (getcustom('guige_split')) {
                \app\model\ShopProduct::addlinkstock($prolist[$item['ogid']]['proid'], $prolist[$item['ogid']]['ggid'], $item['num']);
            }
        }

        //整单全部退时 返还积分和优惠券
        if ($totalRefundNum == $canRefundNum && $money == $canRefundTotalprice) {
            Db::name('shop_order')->where('id', $order['id'])->where('aid', aid)->update(['status' => 4, 'refund_status' => 2, 'refund_money' => $refundMoneySum + $data['refund_money']]);
            Db::name('shop_order_goods')->where('orderid', $order['id'])->where('aid', aid)->update(['status' => 4]);
            //积分抵扣的返还
            if ($order['scoredkscore'] > 0) {
                \app\common\Member::addscore(aid, $order['mid'], $order['scoredkscore'], '订单退款返还');
            }
            //优惠券抵扣的返还
            if ($order['coupon_rid'] > 0) {
                Db::name('coupon_record')->where('aid', aid)->where(['mid' => $order['mid'], 'id' => $order['coupon_rid']])->update(['status' => 0, 'usetime' => '']);
            }
        } else {
            //部分退款
            Db::name('shop_order')->where('id', $order['id'])->where('aid', aid)->inc('refund_money', $data['refund_money'])->update(['refund_status' => 2]);
            //重新计算佣金
            $prolist = Db::name('shop_order_goods')->where('orderid', $order['id'])->where('aid', aid)->select();
            $reog = Db::name('shop_refund_order_goods')->where('refund_orderid', $refund_id)->select();
            \app\common\Order::updateCommission($prolist, $reog);
        }

//		//积分抵扣的返还
//		if($order['scoredkscore'] > 0){
//			\app\common\Member::addscore(aid,$order['mid'],$order['scoredkscore'],'订单退款返还');
//		}
//		//优惠券抵扣的返还
//		if($order['coupon_rid'] > 0){
//			Db::name('coupon_record')->where('aid',aid)->where(['mid'=>$order['mid'],'id'=>$order['coupon_rid']])->update(['status'=>0,'usetime'=>'']);
//		}

        if (getcustom('cefang') && aid == 2) { //定制1 订单对接 同步到策方
            $order['status'] = 4;
            \app\custom\Cefang::api($order);
        }

        $refund_money = $data['refund_money'];
        //退款成功通知
        $tmplcontent = [];
        $tmplcontent['first'] = '您的订单已经完成退款，¥' . $refund_money . '已经退回您的付款账户，请留意查收。';
        $tmplcontent['remark'] = $reason . '，请点击查看详情~';
        $tmplcontent['orderProductPrice'] = $refund_money . '元';
        $tmplcontent['orderProductName'] = $order['title'];
        $tmplcontent['orderName'] = $order['ordernum'];
        \app\common\Wechat::sendtmpl(aid, $order['mid'], 'tmpl_tuisuccess', $tmplcontent, m_url('pages/my/usercenter'));
        //订阅消息
        $tmplcontent = [];
        $tmplcontent['amount6'] = $refund_money;
        $tmplcontent['thing3'] = $order['title'];
        $tmplcontent['character_string2'] = $order['ordernum'];

        $tmplcontentnew = [];
        $tmplcontentnew['amount3'] = $refund_money;
        $tmplcontentnew['thing6'] = $order['title'];
        $tmplcontentnew['character_string4'] = $order['ordernum'];
        \app\common\Wechat::sendwxtmpl(aid, $order['mid'], 'tmpl_tuisuccess', $tmplcontentnew, 'pages/my/usercenter', $tmplcontent);

        //短信通知
        $member = Db::name('member')->where('id', $order['mid'])->find();
        if ($member['tel']) {
            $tel = $member['tel'];
        } else {
            $tel = $order['tel'];
        }
        $rs = \app\common\Sms::send(aid, $tel, 'tmpl_tuisuccess', ['ordernum' => $order['ordernum'], 'money' => $refund_money]);

        \app\common\System::plog('商城订单退款' . $orderid);
        return json(['status' => 1, 'msg' => '已退款成功']);
    }

    //核销并确认收货

    public function refundinit()
    {
        //查询订单信息
        $detail = Db::name('shop_order')->where('id', input('param.orderid/d'))->where('aid', aid)->find();
        if (!$detail)
            return json(['status' => 0, 'msg' => '订单不存在']);
        $detail['createtime'] = $detail['createtime'] ? date('Y-m-d H:i:s', $detail['createtime']) : '';
        $detail['collect_time'] = $detail['collect_time'] ? date('Y-m-d H:i:s', $detail['collect_time']) : '';
        $detail['paytime'] = $detail['paytime'] ? date('Y-m-d H:i:s', $detail['paytime']) : '';
        $detail['refund_time'] = $detail['refund_time'] ? date('Y-m-d H:i:s', $detail['refund_time']) : '';
        $detail['send_time'] = $detail['send_time'] ? date('Y-m-d H:i:s', $detail['send_time']) : '';

        $refundMoneySum = Db::name('shop_refund_order')->where('orderid', $detail['id'])->where('aid', aid)->whereIn('refund_status', [1, 2, 4])->sum('refund_money');

        $canRefundNum = 0;
        $totalNum = 0;
        $returnTotalprice = 0;
        $prolist = Db::name('shop_order_goods')->where('orderid', $detail['id'])->select()->toArray();
        foreach ($prolist as $key => $item) {
            $prolist[$key]['canRefundNum'] = $item['num'] - $item['refund_num'];
            $totalNum += $item['num'];
            $canRefundNum += $item['num'] - $item['refund_num'];
//            $returnTotalprice += $item['real_totalprice'] / $item['num'] * ($item['num'] - $item['refund_num']);
        }
        if ($canRefundNum == $totalNum) {
            $returnTotalprice = $detail['totalprice'];
        } else {
            $returnTotalprice = $detail['totalprice'] - $refundMoneySum;
        }
        //可退款金额=总金额-审核中-已退款
        $detail['canRefundNum'] = $canRefundNum;
        $detail['totalNum'] = $totalNum;
        $detail['returnTotalprice'] = $returnTotalprice;
//        if($canRefundNum == 0) {
//            return $this->json(['status'=>0,'msg'=>'当前订单没有可退款的商品']);
//        }
        //todo 确认收货后的退款

        $rdata = [];
        $rdata['status'] = 1;
        $rdata['detail'] = $detail;
        $rdata['prolist'] = $prolist;

        return json($rdata);
    }

    function orderHexiao()
    {
        $post = input('post.');
        $orderid = intval($post['orderid']);
        if (bid == 0) {
            $order = Db::name('shop_order')->where('aid', aid)->where('id', $orderid)->find();
        } else {
            $order = Db::name('shop_order')->where('aid', aid)->where('bid', bid)->where('id', $orderid)->find();
        }
        if (!$order || !in_array($order['status'], [1, 2]) || $order['freight_type'] != 1) {
            return json(['status' => 0, 'msg' => '订单状态不符合核销收货要求']);
        }

        $refundOrder = Db::name('shop_refund_order')->where('refund_status', 'in', [1, 4])->where('aid', aid)->where('orderid', $orderid)->count();
        if ($refundOrder) {
            return json(['status' => 0, 'msg' => '有正在进行的退款，无法核销']);
        }

        $data = array();
        $data['aid'] = aid;
        $data['bid'] = $order['bid'];
        $data['uid'] = $this->uid;
        $data['mid'] = $order['mid'];
        $data['orderid'] = $order['id'];
        $data['ordernum'] = $order['ordernum'];
        $data['title'] = $order['title'];
        $data['type'] = 'shop';
        $data['createtime'] = time();
        $data['remark'] = '核销员[' . $this->user['un'] . ']核销';
        Db::name('hexiao_order')->insert($data);
        $rs = \app\common\Order::collect($order, 'shop', $this->user['mid']);
        if ($rs['status'] == 0) return $rs;
        Db::name('shop_order')->where('aid', aid)->where('id', $orderid)->update(['status' => 3, 'collect_time' => time()]);
        Db::name('shop_order_goods')->where('aid', aid)->where('orderid', $orderid)->update(['status' => 3, 'endtime' => time()]);
        \app\common\Member::uplv(aid, $order['mid']);
        \app\common\System::plog('商城订单核销确认收货' . $orderid);
        return json(['status' => 1, 'msg' => '核销成功']);
    }

    //打印小票

    function orderCollect()
    { //确认收货
        $post = input('post.');
        $orderid = intval($post['orderid']);
        $order = Db::name('shop_order')->where('aid', aid)->where('id', $orderid)->find();
        if (bid != 0) {
            return json(['status' => 0, 'msg' => '无操作权限']);
        }
        if (!$order || ($order['status'] != 2)) {
            return json(['status' => 0, 'msg' => '订单状态不符合收货要求']);
        }

        $refundOrder = Db::name('shop_refund_order')->where('refund_status', 'in', [1, 4])->where('aid', aid)->where('orderid', $orderid)->count();
        if ($refundOrder) {
            return json(['status' => 0, 'msg' => '有正在进行的退款，无法确认收货']);
        }
        $rs = \app\common\Order::collect($order, 'shop', $this->user['mid']);
        if ($rs['status'] == 0) return $rs;
        Db::name('shop_order')->where('aid', aid)->where('id', $orderid)->update(['status' => 3, 'collect_time' => time()]);
        Db::name('shop_order_goods')->where('aid', aid)->where('orderid', $orderid)->update(['status' => 3, 'endtime' => time()]);
        \app\common\Member::uplv(aid, $order['mid']);
        \app\common\System::plog('商城订单确认收货' . $orderid);
        return json(['status' => 1, 'msg' => '确认收货成功']);
    }

    //删除

    public function wifiprint()
    {
        $id = input('post.id/d');
        $rs = \app\common\Wifiprint::print(aid, 'shop', $id, 0);
        return json($rs);
    }

    //编辑

    public function del()
    {
        $id = input('post.id/d');
        if (bid == 0) {
            Db::name('shop_order')->where('aid', aid)->where('id', $id)->delete();
            Db::name('shop_order_goods')->where('aid', aid)->where('orderid', $id)->delete();

            Db::name('shop_refund_order')->where('aid', aid)->where('orderid', $id)->delete();
            Db::name('shop_refund_order_goods')->where('aid', aid)->where('orderid', $id)->delete();
        } else {
            Db::name('shop_order')->where('aid', aid)->where('bid', bid)->where('id', $id)->delete();
            Db::name('shop_order_goods')->where('aid', aid)->where('bid', bid)->where('orderid', $id)->delete();

            Db::name('shop_refund_order')->where('aid', aid)->where('bid', bid)->where('orderid', $id)->delete();
            Db::name('shop_refund_order_goods')->where('aid', aid)->where('bid', bid)->where('orderid', $id)->delete();
        }
        Db::name('invoice')->where('aid', aid)->where('bid', bid)->where('order_type', 'shop')->where('orderid', $id)->delete();
        \app\common\System::plog('商城订单删除' . $id);
        return json(['status' => 1, 'msg' => '删除成功']);
    }

    //送货单

    public function edit()
    {
        $orderid = input('param.id/d');
        $info = Db::name('shop_order')->where('aid', aid)->where('bid', bid)->where('id', $orderid)->find();
        $order_goods = Db::name('shop_order_goods')->where('aid', aid)->where('bid', bid)->where('orderid', $orderid)->select()->toArray();
        foreach ($order_goods as $k => $v) {
            $order_goods[$k]['lvprice'] = Db::name('shop_product')->where('id', $v['proid'])->value('lvprice'); //是否开启会员价
        }
        $member = Db::name('member')->where('id', $info['mid'])->find();
        $userlevel = Db::name('member_level')->where('aid', aid)->where('id', $member['levelid'])->find();
        if ($userlevel && $userlevel['discount'] > 0 && $userlevel['discount'] < 10) {
            $discount = $userlevel['discount'] * 0.1; //会员折扣
        } else {
            $discount = 1;
        }

        if (request()->isAjax()) {
            $postinfo = input('post.info/a');
            Db::name('shop_order')->where('id', $orderid)->update($postinfo);
            $order = Db::name('shop_order')->where('id', $orderid)->find();
            $goods_id = input('post.goods_id/a');
            $goods_ggname = input('post.goods_ggname/a');
            $goods_sell_price = input('post.goods_sell_price/a');
            $goods_num = input('post.goods_num/a');
            foreach ($goods_id as $k => $ogid) {
                $oginfo = Db::name('shop_order_goods')->where('id', $ogid)->find();
                $ogdata = [];
                $ogdata['ggname'] = $goods_ggname[$k];
                $ogdata['sell_price'] = $goods_sell_price[$k];
                $ogdata['num'] = $goods_num[$k];
                $ogdata['totalprice'] = $ogdata['sell_price'] * $ogdata['num'];

                $product = Db::name('shop_product')->where('id', $oginfo['proid'])->find();
                $ogtotalprice = $ogdata['totalprice'];
                $commissiontype = Db::name('admin_set')->where('aid', aid)->value('commissiontype');
                if ($commissiontype == 1) {
                    $allgoodsprice = $order['goodsprice'] - $order['disprice'];
                    $couponmoney = $order['couponmoney'];
                    $scoredk = $order['scoredk'];
                    $disprice = 0;
                    $ogcouponmoney = 0;
                    $ogscoredk = 0;
                    if ($product['lvprice'] == 0 && $userlevel && $userlevel['discount'] > 0 && $userlevel['discount'] < 10) { //未开启会员价
                        $disprice = $ogtotalprice * (1 - $userlevel['discount'] * 0.1);
                        $ogtotalprice = $ogtotalprice - $disprice;
                    }
                    if ($couponmoney) {
                        $ogcouponmoney = $ogtotalprice / $allgoodsprice * $couponmoney;
                    }
                    if ($scoredk) {
                        $ogscoredk = $ogtotalprice / $allgoodsprice * $scoredk;
                    }
                    $ogtotalprice = round($ogtotalprice - $ogcouponmoney - $ogscoredk, 2);
                    if ($ogtotalprice < 0) $ogtotalprice = 0;
                }
                $agleveldata = Db::name('member_level')->where('aid', aid)->where('id', $member['levelid'])->find();

                if ($product['commissionset'] != -1) {
                    if ($member['pid']) {
                        $parent1 = Db::name('member')->where('aid', aid)->where('id', $member['pid'])->find();

                        if ($parent1) {
                            $agleveldata1 = Db::name('member_level')->where('aid', aid)->where('id', $parent1['levelid'])->find();
                            if ($agleveldata1['can_agent'] != 0) {
                                $ogdata['parent1'] = $parent1['id'];
                            }
                        }
                        //return json(['status'=>0,'msg'=>'11','data'=>$parent1,'data2'=>$agleveldata1]);
                    }
                    if ($parent1['pid']) {
                        $parent2 = Db::name('member')->where('aid', aid)->where('id', $parent1['pid'])->find();
                        if ($parent2) {
                            $agleveldata2 = Db::name('member_level')->where('aid', aid)->where('id', $parent2['levelid'])->find();
                            if ($agleveldata2['can_agent'] > 1) {
                                $ogdata['parent2'] = $parent2['id'];
                            }
                        }
                    }
                    if ($parent2['pid']) {
                        $parent3 = Db::name('member')->where('aid', aid)->where('id', $parent2['pid'])->find();
                        if ($parent3) {
                            $agleveldata3 = Db::name('member_level')->where('aid', aid)->where('id', $parent3['levelid'])->find();
                            if ($agleveldata3['can_agent'] > 2) {
                                $ogdata['parent3'] = $parent3['id'];
                            }
                        }
                    }
                    if ($product['commissionset'] == 1) {//按比例
                        $commissiondata = json_decode($product['commissiondata1'], true);
                        if ($commissiondata) {
                            $ogdata['parent1commission'] = $commissiondata[$agleveldata1['id']]['commission1'] * $ogtotalprice * 0.01;
                            $ogdata['parent2commission'] = $commissiondata[$agleveldata2['id']]['commission2'] * $ogtotalprice * 0.01;
                            $ogdata['parent3commission'] = $commissiondata[$agleveldata3['id']]['commission3'] * $ogtotalprice * 0.01;
                        }
                    } elseif ($product['commissionset'] == 2) {//按固定金额
                        $commissiondata = json_decode($product['commissiondata2'], true);
                        if ($commissiondata) {
                            $ogdata['parent1commission'] = $commissiondata[$agleveldata1['id']]['commission1'] * $ogdata['num'];
                            $ogdata['parent2commission'] = $commissiondata[$agleveldata2['id']]['commission2'] * $ogdata['num'];
                            $ogdata['parent3commission'] = $commissiondata[$agleveldata3['id']]['commission3'] * $ogdata['num'];
                        }
                    } else {
                        $ogdata['parent1commission'] = $agleveldata1['commission1'] * $ogtotalprice * 0.01;
                        $ogdata['parent2commission'] = $agleveldata2['commission2'] * $ogtotalprice * 0.01;
                        $ogdata['parent3commission'] = $agleveldata3['commission3'] * $ogtotalprice * 0.01;
                    }
                }

                Db::name('shop_order_goods')->where('aid', aid)->where('bid', bid)->where('id', $ogid)->update($ogdata);
            }

            $newordernum = date('ymdHis') . rand(100000, 999999);
            Db::name('shop_order')->where('aid', aid)->where('id', $orderid)->update(['ordernum' => $newordernum]);
            Db::name('shop_order_goods')->where('aid', aid)->where('orderid', $orderid)->update(['ordernum' => $newordernum]);

            $payorderid = Db::name('shop_order')->where('aid', aid)->where('id', $orderid)->value('payorderid');
            \app\model\Payorder::updateorder($payorderid, $newordernum, $postinfo['totalprice']);

            \app\common\System::plog('商城订单编辑' . $orderid);
            return json(['status' => 1, 'msg' => '修改成功']);
        }
        View::assign('info', $info);
        View::assign('order_goods', $order_goods);
        View::assign('discount', $discount);
        View::assign('express_data', express_data());
        return View::fetch();
    }

    //订单统计

    public function shd()
    {
        $orderid = input('param.id/d');
        $info = Db::name('shop_order')->where('aid', aid)->where('id', $orderid)->find();
        if (!$info || (bid != 0 && $info['bid'] != bid)) showmsg('订单不存在');
        $order_goods = Db::name('shop_order_goods')->where('aid', aid)->where('orderid', $orderid)->select()->toArray();

        $totalnum = 0;
        foreach ($order_goods as $k => $v) {
            $order_goods[$k]['lvprice'] = Db::name('shop_product')->where('id', $v['proid'])->value('lvprice'); //是否开启会员价
            $totalnum += $v['num'];
        }
        $member = Db::name('member')->where('id', $info['mid'])->find();
        $userlevel = Db::name('member_level')->where('aid', aid)->where('id', $member['levelid'])->find();
        if ($userlevel && $userlevel['discount'] > 0 && $userlevel['discount'] < 10) {
            $discount = $userlevel['discount'] * 0.1; //会员折扣
        } else {
            $discount = 1;
        }
        $order_goods2 = [];
        if (count($order_goods) < 10) {
            for ($i = 0; $i < 10; $i++) {
                $order_goods2[] = $order_goods[$i];
            }
        } else {
            $order_goods2 = $order_goods;
        }
        if (!getcustom('baikangxie')) {
            $order_goods2[] = ['type' => 'yf'];
            $order_goods2[] = ['type' => 'totalprice'];
            $order_goods2[] = ['type' => 'totalprice2'];
        } else {
            $order_goods2[] = ['name' => '合计', 'num' => $totalnum];
        }
        $order_goods3 = array_chunk($order_goods2, 13);
        $info['totalprice2'] = num_to_rmb($info['totalprice']);
        if ($info['freight_type'] == 11) {
            $info['freight_content'] = json_decode($info['freight_content'], true);
        }
        if ($info['bid'] == 0) {
            $bname = Db::name('admin_set')->where('aid', 0)->value('name');
        } else {
            $bname = Db::name('business')->where('id', $info['bid'])->value('name');
        }
        $info['member_address'] = '';
        if($info['address_id']){
            $info['member_address'] = Db::name('member_address')->where('id',$info['address_id'])->find();
        }
        
        View::assign('bname', $bname);
        View::assign('info', $info);
        View::assign('order_goods3', $order_goods3);
        View::assign('discount', $discount);
        View::assign('express_data', express_data());
        return View::fetch();
    }
    
    
    public function tags()
    {
        $orderid = input('param.id/d');
        $info = Db::name('shop_order')->where('aid', aid)->where('id', $orderid)->find();
        if (!$info || (bid != 0 && $info['bid'] != bid)) showmsg('订单不存在');
        $order_goods = Db::name('shop_order_goods')->where('aid', aid)->where('orderid', $orderid)->select()->toArray();

        $totalnum = 0;
        foreach ($order_goods as $k => $v) {
            $order_goods[$k]['lvprice'] = Db::name('shop_product')->where('id', $v['proid'])->value('lvprice'); //是否开启会员价
            $totalnum += $v['num'];
        }
        $member = Db::name('member')->where('id', $info['mid'])->find();
        $userlevel = Db::name('member_level')->where('aid', aid)->where('id', $member['levelid'])->find();
        if ($userlevel && $userlevel['discount'] > 0 && $userlevel['discount'] < 10) {
            $discount = $userlevel['discount'] * 0.1; //会员折扣
        } else {
            $discount = 1;
        }
        $order_goods2 = [];
        if (count($order_goods) < 10) {
            for ($i = 0; $i < 10; $i++) {
                $order_goods2[] = $order_goods[$i];
            }
        } else {
            $order_goods2 = $order_goods;
        }
        if (!getcustom('baikangxie')) {
            $order_goods2[] = ['type' => 'yf'];
            $order_goods2[] = ['type' => 'totalprice'];
            $order_goods2[] = ['type' => 'totalprice2'];
        } else {
            $order_goods2[] = ['name' => '合计', 'num' => $totalnum];
        }
        $order_goods3 = array_chunk($order_goods2, 13);
        $info['totalprice2'] = num_to_rmb($info['totalprice']);
        if ($info['freight_type'] == 11) {
            $info['freight_content'] = json_decode($info['freight_content'], true);
        }
        if ($info['bid'] == 0) {
            $bname = Db::name('admin_set')->where('aid', 0)->value('name');
        } else {
            $bname = Db::name('business')->where('id', $info['bid'])->value('name');
        }
        $info['member_address'] = '';
        if($info['address_id']){
            $info['member_address'] = Db::name('member_address')->where('id',$info['address_id'])->find();
        }
        
        View::assign('bname', $bname);
        View::assign('info', $info);
        View::assign('order_goods3', $order_goods3);
        View::assign('discount', $discount);
        View::assign('express_data', express_data());
        return View::fetch();
    }
    
    
    public function wuliushd()
    {
        $packid = input('param.id');
        $info = Db::name('wuliu_pack')->where('aid', aid)->where('packnum', $packid)->find();
        if (!$info) showmsg('订单不存在');
       
        $bname = Db::name('business')->where('id', 33)->value('name');
        $cname = Db::name('mendian')->where('id', $info['cangku'])->value('name');
        
        $otherpack = [];
        if($info['otherpack']){
            $otherpack = json_decode($info['otherpack'],true);
        }
        
        View::assign('bname', $bname);
        View::assign('cname', $cname);
        View::assign('info', $info);
        View::assign('otherpack', $otherpack);
        
        return View::fetch('');
    }

    //导出

    public function tongji()
    {
        if (request()->isAjax()) {
            $page = input('param.page');
            $limit = input('param.limit');
            if (input('param.field') && input('param.order')) {
                $order = input('param.field') . ' ' . input('param.order');
            } else {
                $order = 'totalprice desc';
            }
            $where = [];
            $where[] = ['og.aid', '=', aid];
            $where[] = ['og.bid', '=', bid];
            $where[] = ['og.status', 'in', '1,2,3'];
            if ($this->mdid) {
                $where[] = ['mdid', '=', $this->mdid];
            }
            if (input('param.ctime')) {
                $ctime = explode(' ~ ', input('param.ctime'));
                $where[] = ['og.createtime', '>=', strtotime($ctime[0])];
                $where[] = ['og.createtime', '<', strtotime($ctime[1]) + 86400];
            }
            if (input('param.paytime')) {
                $ctime = explode(' ~ ', input('param.paytime'));
                $where[] = ['shop_order.paytime', '>=', strtotime($ctime[0])];
                $where[] = ['shop_order.paytime', '<', strtotime($ctime[1]) + 86400];
            }
            if (input('param.proname')) {
                $where[] = ['og.name', 'like', '%' . input('param.proname') . '%'];
            }
            if (input('param.cid')) {
                $where[] = ['og.cid', '=', input('param.cid')];
            }
            if (input('param.type') == 2) {
                $count = 0 + Db::name('shop_order_goods')->alias('og')->join('shop_order', 'shop_order.id=og.orderid')->fieldRaw('og.proid')->where($where)->group('ggid')->count();
                $list = Db::name('shop_order_goods')->alias('og')->join('shop_order', 'shop_order.id=og.orderid')->fieldRaw('og.proid,og.name,og.pic,og.ggname,sum(og.num) num,sum(og.totalprice) totalprice,sum(og.totalprice)/sum(og.num) as avgprice,sum(og.cost_price*og.num) as chengben,sum(og.totalprice-og.cost_price*og.num) lirun')->where($where)->group('ggid')->page($page, $limit)->order($order)->select()->toArray();
            } else {
                $count = 0 + Db::name('shop_order_goods')->alias('og')->join('shop_order', 'shop_order.id=og.orderid')->fieldRaw('og.proid')->where($where)->group('proid')->count();
                $list = Db::name('shop_order_goods')->alias('og')->join('shop_order', 'shop_order.id=og.orderid')->fieldRaw('og.proid,og.name,og.pic,og.ggname,sum(og.num) num,sum(og.totalprice) totalprice,sum(og.totalprice)/sum(og.num) as avgprice,sum(og.cost_price*og.num) as chengben,sum(og.totalprice-og.cost_price*og.num) lirun')->where($where)->group('proid')->page($page, $limit)->order($order)->select()->toArray();
            }
            foreach ($list as $k => $v) {
                $list[$k]['ph'] = ($k + 1) + ($page - 1) * $limit;
                $list[$k]['avgprice'] = number_format($v['avgprice'], 2, '.', '');
            }

            return json(['code' => 0, 'msg' => '查询成功', 'count' => $count, 'data' => $list]);
        }
        return View::fetch();
    }

    public function tjexcel()
    {
        set_time_limit(0);
        ini_set('memory_limit', '2000M');
        if (input('param.field') && input('param.order')) {
            $order = input('param.field') . ' ' . input('param.order');
        } else {
            $order = 'totalprice desc';
        }
        $where = [];
        $where[] = ['og.aid', '=', aid];
        $where[] = ['og.bid', '=', bid];
        $where[] = ['og.status', 'in', '1,2,3'];
        if ($this->mdid) {
            $where[] = ['mdid', '=', $this->mdid];
        }
        if (input('param.ctime')) {
            $ctime = explode(' ~ ', input('param.ctime'));
            $where[] = ['og.createtime', '>=', strtotime($ctime[0])];
            $where[] = ['og.createtime', '<', strtotime($ctime[1]) + 86400];
        }
        if (input('param.paytime')) {
            $ctime = explode(' ~ ', input('param.paytime'));
            $where[] = ['shop_order.paytime', '>=', strtotime($ctime[0])];
            $where[] = ['shop_order.paytime', '<', strtotime($ctime[1]) + 86400];
        }
        if (input('param.proname')) {
            $where[] = ['og.name', 'like', '%' . input('param.proname') . '%'];
        }
        if (input('param.cid')) {
            $where[] = ['og.cid', '=', input('param.cid')];
        }
        if (input('param.type') == 2) {
            $list = Db::name('shop_order_goods')->alias('og')->join('shop_order', 'shop_order.id=og.orderid')->field('og.proid,og.name,og.pic,og.ggname,sum(og.num) num,sum(og.totalprice) totalprice,sum(og.totalprice)/sum(og.num) as avgprice')->where($where)->group('ggid')->order($order)->select()->toArray();
        } else {
            $list = Db::name('shop_order_goods')->alias('og')->join('shop_order', 'shop_order.id=og.orderid')->field('og.proid,og.name,og.pic,og.ggname,sum(og.num) num,sum(og.totalprice) totalprice,sum(og.totalprice)/sum(og.num) as avgprice')->where($where)->group('proid')->order($order)->select()->toArray();
        }
        foreach ($list as $k => $v) {
            $list[$k]['ph'] = ($k + 1);
            $list[$k]['avgprice'] = number_format($v['avgprice'], 2, '.', '');
        }
        if (input('param.type') == 2) {
            $title = array('排名', '商品名称', '商品规格', '销售数量', '销售金额', '平均单价');
            $data = [];
            foreach ($list as $k => $vo) {
                $data[] = [
                    $vo['ph'],
                    $vo['name'],
                    $vo['ggname'],
                    $vo['num'],
                    $vo['totalprice'],
                    $vo['avgprice'],
                ];
            }
        } else {
            $title = array('排名', '商品名称', '销售数量', '销售金额', '平均单价');
            $data = [];
            foreach ($list as $k => $vo) {
                $data[] = [
                    $vo['ph'],
                    $vo['name'],
                    $vo['num'],
                    $vo['totalprice'],
                    $vo['avgprice'],
                ];
            }
        }
        $this->export_excel($title, $data);
    }
    
    public function neworder()
    {
        if (request()->isAjax()) {
            $where = [];
            $where[] = ['order.aid', '=', aid];
            $where[] = ['order.bid', '=', bid];

            $res = Db::name('shop_order')->alias('order')->field('order.*')->leftJoin('member member', 'member.id=order.mid')->where($where)->order('order.id desc')->find();

        
                $member = Db::name('member')->where('id', $res['mid'])->find();
                $res['goodsdata'] = Db::name('shop_order_goods')->where('aid', aid)->where('orderid', $res['id'])->find();

                if ($res['bid'] > 0) {
                    $res['bname'] = Db::name('business')->where('aid', aid)->where('id', $res['bid'])->value('name');
                } else {
                    $res['bname'] = '平台自营';
                }
                
                $res['nickname'] = $member['nickname'];
                $res['headimg'] = $member['headimg'];
                $res['m_remark'] = $member['remark'];
                $res['platform'] = getplatformname($res['platform']);
            
                return json(['code' => 0, 'msg' => '查询成功',  'data' => $res]);
        }
    }
    public function createOrder()
    {
        $ordernum = \app\common\Common::generateOrderNo(2);
        $orderdata = [];
        $orderdata['aid'] = 2;
        $orderdata['mid'] = 0;
        $orderdata['bid'] = 33;      
        $orderdata['title'] = 'E速物流,泰国专运';
        $orderdata['totalprice'] = 0;
        $orderdata['freight_price'] = 0; //运费
        $orderdata['scoredkscore'] = 0;
        $orderdata['freight_id'] = 66;      
        $orderdata['platform'] = 'wx';
        
        $orderdata['ocrimage'] = input('ocrimage');
        $orderdata['createtime'] = time();
        $orderdata['ordernum'] = $ordernum;
        $orderdata['newview'] = 0;
        


        $wuliu = input('wuliu');
        $orderdata['mid'] = substr(substr(input('user_id'),2),0,-1);
        $member_user = Db::name('member')->where('id', $orderdata['mid'])->find();
        if(!$member_user) return json(['code' => 1, 'msg' => "用户不存在"]);
        
        $orderdata['linkman'] = input('user_id');
        $orderdata['tel'] = input('tel');
        $orderdata['area'] = input('area');
        $orderdata['address'] = input('address');
        $orderdata['expressnum'] = input('expressnum');
     
        $mendian = Db::name('mendian')->where('id', input('store_id'))->find();
        if(!$mendian){
          $mendian['name'] = '默认仓库';
          $mendian['area'] = '';
          $mendian['id'] = 0;           
        }
        $orderdata['mdid'] = $mendian['id'];
        $orderdata['freight_text'] = '到店自提' . '[' . $mendian['name'] . ']';
        $orderdata['area2'] = $mendian['area'];
        $orderdata['freight_type'] = 1;
     
        $orderid = Db::name('shop_order')->insertGetId($orderdata);
        $payorderid = \app\model\Payorder::createorder($orderdata['aid'], $orderdata['bid'],
         $orderdata['mid'], 'shop', $orderid, $orderdata['ordernum'], $orderdata['title'], 
         $orderdata['totalprice'], $orderdata['scoredkscore']);
      
        $ogdata = [];
        $ogdata['aid'] = $orderdata['aid'];
        $ogdata['bid'] = $orderdata['bid'];
        $ogdata['mid'] = $orderdata['mid'];
        $ogdata['orderid'] = $orderid;
        $ogdata['ordernum'] = $orderdata['ordernum'];
        $ogdata['proid'] = 0;
        $ogdata['name'] = $wuliu?$wuliu:'未知运输方式';
        $ogdata['num'] = 1;
        $ogdata['cost_price'] = 0;
        $ogdata['sell_price'] = 0;
        $ogdata['totalprice'] = $ogdata['num'] * $ogdata['sell_price'];
        $ogdata['total_weight'] = 0;
        $ogdata['status'] = 0;
        $ogdata['createtime'] = time();
        $ogdata['isfg'] = 0;
        $ogdata['currency'] = 1;

        $ogdata['real_totalprice'] = $ogdata['totalprice']; //实际商品销售金额
        $ogid = Db::name('shop_order_goods')->insertGetId($ogdata);

        $ogupdate = [];
        if ($ogupdate) {
          Db::name('shop_order_goods')->where('id', $ogid)->update($ogupdate);
        }
        $remark['aid'] = $orderdata['aid'];
        $remark['bid'] = $orderdata['bid'];
        $remark['mid'] = $orderdata['mid'];
        $remark['orderid'] = $orderid;
        $remark['remark'] = '快递已入库';        
        $wuliuinfo = Db::name('wuliu_remark')->where($remark)->find();
        if (!$wuliuinfo) {
          $remark['createtime'] = time();
          Db::name('wuliu_remark')->insert($remark);
        }
        $data['goodstype'] = input('post.goodstype');
        $data['weight'] = input('post.weight');
        $data['length'] = input('post.length');
        $data['width'] = input('post.width');
        $data['height'] = input('post.height');
        if (bid == 0) {
            Db::name('shop_order')->where('aid', aid)->where('id', $orderid)->update($data);
        } else {
            Db::name('shop_order')->where('aid', aid)->where('bid', bid)->where('id', $orderid)->update($data);
        }
        $order = Db::name('shop_order')->where('id',$orderid)->find();
        //计算价格
        $total = 0;
        $res = Db::name('mendian')->where('id',$order['mdid'])->find();
        if($res && $res['specs']){
            $items = [];
            $specs = json_decode($res['specs'], true);
            foreach($specs as $k=>$v){
              array_push($items,$v['items'][0]);
            }
            $volumeprice = 0;
            $weightprice = 0;
            $minprice = 0;
            $transport = Db::name('shop_order_goods')->where('orderid',$order['id'])->value('name');
            foreach ($items as $key => $value) {
              if($value['transport']==$transport&&$value['goodstype']==$data['goodstype']){
                $volumeprice = $value['volumeprice'];
                $weightprice = $value['weightprice'];
                $minprice = $value['minprice'];
              }
            }
          if($volumeprice!=0&&$weightprice!=0){
            if($transport=='海运公斤'||$transport=='陆运公斤'){
                $wprice = $weightprice * $data['weight'];
                $vprice = (($data['length'] * $data['width'] * $data['height'])/6000)*$volumeprice;
                $final = $wprice > $vprice ? $wprice : $vprice;
                $total = $final<$minprice?$minprice:$final;
            } else {
              //立方计算
              $total = (($data['length'] * $data['width'] * $data['height'])/1000000)*$volumeprice;
            }
            $total = number_format($total, 3);
          }          
        }
        Db::name('shop_order_goods')->where('orderid',$order['id'])->update(['sell_price'=>$total]);
        $orderdata['product_price'] = $total;
        $orderdata['totalprice'] = $total;
        // dd($order['id']);
        Db::name('shop_order')->where('id', $order['id'])->update($orderdata);
        Db::name('payorder')->where('orderid',$order['id'])->update([
            'money'=>$total
        ]);
        return json(['code' => 0, 'msg' => \ccphp\Lang::get("提交成功")]);
    }
  public function createcodeorder()
  {
    if (request()->isPost()) {
      $ordernum = \app\common\Common::generateOrderNo(2);
      $orderdata = [];
      $orderdata['aid'] = 2;
      $orderdata['mid'] = 0;
      $orderdata['bid'] = 33;      
      $orderdata['title'] = 'E速物流,泰国专运';
      $orderdata['linkman'] = '';
      $orderdata['tel'] = '';
      $orderdata['area'] = '';
      $orderdata['address'] = '';
      $orderdata['totalprice'] = 0;
      $orderdata['freight_price'] = 0; //运费
      $orderdata['scoredkscore'] = 0;
 
      $orderdata['freight_id'] = 66;      
      $orderdata['platform'] = 'wx';
      
      $orderdata['ocrimage'] = '';
      $orderdata['createtime'] = time();
      $orderdata['ordernum'] = $ordernum;
      $orderdata['newview'] = 0;

          $content = input('post.code');
          $type = strtoupper(substr($content,-1));
          if($type=='L'){
            $wuliu = '陆运公斤';
          } else if($type=='H'){
            $wuliu = '海运公斤';
          } else if($type=='Y'){
            $wuliu = '海运立方';
          } else if($type=='Z'){
            $wuliu = '陆运立方';
          } else {
            $wuliu = '未知运输';
          }
          $orderdata['mid'] = substr(substr($content,2),0,-1);
          $member_user = Db::name('member')->where('id', $orderdata['mid'])->find();
          if(!$member_user) return json(['code' => 1, 'msg' => "用户不存在"]);
          
          $orderdata['linkman'] = $content;
          $orderdata['tel'] = '';
          $orderdata['area'] = '';
          $orderdata['address'] = '';
          $orderdata['expressnum'] = '';

          
          $code = strtoupper(substr($content, 0, 2));
          $mendian = Db::name('mendian')->where('code', $code)->find();
          if(!$mendian){
            $mendian['name'] = '默认仓库';
            $mendian['area'] = '';
            $mendian['id'] = 0;           
          }
          $orderdata['mdid'] = $mendian['id'];
          $orderdata['freight_text'] = '到店自提' . '[' . $mendian['name'] . ']';
          $orderdata['area2'] = $mendian['area'];
          $orderdata['freight_type'] = 1;
          $orderid = Db::name('shop_order')->insertGetId($orderdata);
      

      $payorderid = \app\model\Payorder::createorder($orderdata['aid'], $orderdata['bid'], $orderdata['mid'], 'shop', $orderid, $orderdata['ordernum'], $orderdata['title'], $orderdata['totalprice'], $orderdata['scoredkscore']);

        $ogdata = [];
        $ogdata['aid'] = $orderdata['aid'];
        $ogdata['bid'] = $orderdata['bid'];
        $ogdata['mid'] = $orderdata['mid'];
        $ogdata['orderid'] = $orderid;
        $ogdata['ordernum'] = $orderdata['ordernum'];
        $ogdata['proid'] = 0;
        $ogdata['name'] = $wuliu?$wuliu:'未知运输方式';
        $ogdata['num'] = 1;
        $ogdata['cost_price'] = 0;
        $ogdata['sell_price'] = 0;
        $ogdata['totalprice'] = $ogdata['num'] * $ogdata['sell_price'];
        $ogdata['total_weight'] = 0;
        $ogdata['status'] = 0;
        $ogdata['createtime'] = time();
        $ogdata['isfg'] = 0;
        $ogdata['currency'] = 1;

        $ogdata['real_totalprice'] = $ogdata['totalprice']; //实际商品销售金额

        $ogid = Db::name('shop_order_goods')->insertGetId($ogdata);

        $ogupdate = [];
        if ($ogupdate) {
          Db::name('shop_order_goods')->where('id', $ogid)->update($ogupdate);
        }
        $remark['aid'] = $orderdata['aid'];
        $remark['bid'] = $orderdata['bid'];
        $remark['mid'] = $orderdata['mid'];
        $remark['orderid'] = $orderid;
        $remark['remark'] = '快递已入库';        
        $wuliuinfo = Db::name('wuliu_remark')->where($remark)->find();
        if (!$wuliuinfo) {
          $remark['createtime'] = time();
          Db::name('wuliu_remark')->insert($remark);
        }
        return json(['code' => 0, 'msg' => \ccphp\Lang::get("提交成功")]);
    }
  }
  
  public function mendianlist()
  {
      $mendian = Db::name('mendian')->where('aid', aid)->where('bid', 33)->where('status', 1)->select();
      return json(['code' => 0, 'msg' => '查询成功',  'data' => $mendian]);
  }
  
  public function delivergoods()
  {
      if (request()->isPost()) {
          $code = input('post.code');
          $cangid = input('post.cang');
          $type = input('post.type');
          $num = substr($type,-1);
          
          $data['aid'] = aid;
          $data['bid'] = bid;
          $data['packnum'] = $code;
          $data['packweight'] = input('post.weight');
          $data['packlength'] = input('post.length');
          $data['packwidth'] = input('post.width');
          $data['packheight'] = input('post.height');
          $data['packname'] = input('post.packname');
          $data['cangku'] = $cangid;
          $data['otherpack'] = input('post.other');
          
          $shoplist = Db::name('shop_order')->where('aid', aid)->where('bid', 33)->where('mdid', $cangid)->where(function ($query){$query->where('status', 0)->whereOr('status', 1);})->select()->toArray();
          
          foreach ($shoplist as $v){
              if(substr($v['linkman'],-1)==$num){
                 $status = Db::name('shop_order_goods')->where('orderid', $v['id'])->update(['status'=>2]);
                 if(!$status)continue;
                 Db::name('shop_order')->where('id', $v['id'])->update(['status'=>2,'shippingnum'=>$code]);
                 Db::name('wuliu_remark')->where('orderid',$v['id'])->where('remark','快递已入库')->update(['packnum'=>$code]);
                    $remark['aid'] = aid;
                    $remark['bid'] = $v['bid'];
                    $remark['mid'] = $v['mid'];
                    $remark['orderid'] = $v['id'];
                    $remark['packnum'] = $code;
                    $remark['remark'] = '一键出库';       
                    $wuliuinfo = Db::name('wuliu_remark')->where($remark)->find();
                    if (!$wuliuinfo) {
                      $remark['createtime'] = time();
                      Db::name('wuliu_remark')->insert($remark);
                      $wid = Db::name('wuliu_pack')->where('packnum',$data['packnum'])->value('id');
                      if(!$wid){
                            $data['createtime'] = time();
                            Db::name('wuliu_pack')->insert($data);
                      }
                    }
              }
          }
          return json(['code' => 0, 'msg' => '操作成功']);
      }
  }
}
