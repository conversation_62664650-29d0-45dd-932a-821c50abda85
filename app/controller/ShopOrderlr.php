<?php


// +----------------------------------------------------------------------
// | 录入订单
// +----------------------------------------------------------------------
namespace app\controller;

use think\facade\Db;
use think\facade\View;

class ShopOrderlr extends Common
{
    public function initialize()
    {
        parent::initialize();
        if (bid > 0) showmsg('无访问权限');
    }

    public function index()
    {
        $sysset = Db::name('seckill_sysset')->where('aid', aid)->find();
        $systimeset = explode(',', $sysset['timeset']);
        View::assign('sysset', $sysset);
        View::assign('systimeset', $systimeset);
        return View::fetch();
    }

    public function save()
    {
        $data = input('post.');
        $prodata = explode('-', $data['prodata']);
        $member = Db::name('member')->where('aid', aid)->where('id', $data['mid'])->find();
        if (!$member) return json(['status' => 0, 'msg' => '未找到该' . t('会员')]);
        $prolist = [];
        $pstype = 0;
        foreach ($prodata as $key => $pro) {
            $sdata = explode(',', $pro);
            $product = Db::name('shop_product')->where('aid', aid)->where('id', $sdata[0])->find();
            if (!$product) return json(['status' => 0, 'msg' => '产品不存在或已下架']);
            $guige = Db::name('shop_guige')->where('aid', aid)->where('id', $sdata[1])->find();
            if (!$guige) return json(['status' => 0, 'msg' => '产品规格不存在或已下架']);
            if ($guige['stock'] < $sdata[2]) {
                return json(['status' => 0, 'msg' => '库存不足']);
            }
            if ($key == 0) $title = $product['name'];
            $prolist[] = ['product' => $product, 'guige' => $guige, 'num' => $sdata[2]];

            if ($product['freighttype'] == 3) {
                $pstype = 3;
            } elseif ($product['freighttype'] == 4) {
                $pstype = 4;
            }
        }
        if ($pstype != 0 && count($prolist) > 1) {
            return json(['status' => 0, 'msg' => ($pstype == 3 ? '自动发货' : '在线卡密') . '商品需要单独录入']);
        }
        $sysset = Db::name('admin_set')->where('aid', aid)->find();

        $ordernum = \app\common\Common::generateOrderNo(aid);

        $orderdata = [];
        $orderdata['aid'] = aid;
        $orderdata['mid'] = $data['mid'];
        $orderdata['bid'] = bid;
        $orderdata['ordernum'] = $ordernum;
        $orderdata['title'] = $title . (count($prodata) > 1 ? '等' : '');

        $orderdata['linkman'] = $data['linkman'];
        $orderdata['tel'] = $data['tel'];
        $orderdata['area'] = '';
        $orderdata['address'] = $data['address'];
        $orderdata['totalprice'] = $data['totalprice'];
        $orderdata['product_price'] = $data['goodsprice'];
        $orderdata['leveldk_money'] = 0;  //会员折扣
        $orderdata['scoredk_money'] = 0;    //积分抵扣
        $orderdata['scoredkscore'] = 0;    //抵扣掉的积分
        $orderdata['freight_price'] = $data['freightprice']; //运费
        $orderdata['message'] = '';
        $orderdata['freight_text'] = $data['freight'];
        $orderdata['freight_id'] = 0;
        $orderdata['freight_type'] = $pstype;
        $orderdata['createtime'] = time();
        $orderdata['platform'] = 'mp';
        $orderdata['hexiao_code'] = random(16);
        $orderdata['hexiao_qr'] = createqrcode(m_url('admin/hexiao/hexiao?type=shop&co=' . $orderdata['hexiao_code']));
        $orderdata['status'] = 1;
        $orderdata['paytype'] = $data['paytype'];
        if (session('IS_ADMIN') == 0) {
            $user = Db::name('admin_user')->where('id', $this->uid)->find();
            $remark = '后台录入，操作员：' . $user['un'];
        } else {
            $remark = '后台录入';
        }
        $orderdata['remark'] = $remark;
        $orderid = Db::name('shop_order')->insertGetId($orderdata);

        $istc = 0; //设置了按单固定提成时 只将佣金计算到第一个商品里
        foreach ($prolist as $key => $v) {
            $product = $v['product'];
            $guige = $v['guige'];
            $num = $v['num'];
            $ogdata = [];
            $ogdata['aid'] = aid;
            $ogdata['bid'] = $product['bid'];
            $ogdata['mid'] = $data['mid'];
            $ogdata['orderid'] = $orderid;
            $ogdata['ordernum'] = $orderdata['ordernum'];
            $ogdata['proid'] = $product['id'];
            $ogdata['name'] = $product['name'];
            $ogdata['pic'] = $product['pic'];
            $ogdata['procode'] = $product['procode'];
            $ogdata['barcode'] = $product['barcode'];
            $ogdata['ggid'] = $guige['id'];
            $ogdata['ggname'] = $guige['name'];
            $ogdata['cid'] = $product['cid'];
            $ogdata['num'] = $num;
            $ogdata['cost_price'] = $guige['cost_price'];
            $ogdata['sell_price'] = $guige['sell_price'];
            $ogdata['totalprice'] = $num * $guige['sell_price'];
            $ogdata['status'] = 1;
            $ogdata['createtime'] = time();
            $agleveldata = Db::name('member_level')->where('aid', aid)->where('id', $member['levelid'])->find();
            if ($istc != 1) {
                $og_totalprice = $ogdata['totalprice'];
                $leveldk_money = 0;
                $coupon_money = 0;
                $scoredk_money = 0;
                $manjian_money = 0;

                //计算商品实际金额  商品金额 - 会员折扣 - 积分抵扣 - 满减抵扣 - 优惠券抵扣
                if ($sysset['fxjiesuantype'] == 1 || $sysset['fxjiesuantype'] == 2) {
                    $allproduct_price = $orderdata['totalprice'];
                    $og_leveldk_money = 0;
                    $og_coupon_money = 0;
                    $og_scoredk_money = 0;
                    $og_manjian_money = 0;
                    if ($allproduct_price > 0 && $og_totalprice > 0) {
                        if ($leveldk_money) {
                            $og_leveldk_money = $og_totalprice / $allproduct_price * $leveldk_money;
                        }
                        if ($coupon_money) {
                            $og_coupon_money = $og_totalprice / $allproduct_price * $coupon_money;
                        }
                        if ($scoredk_money) {
                            $og_scoredk_money = $og_totalprice / $allproduct_price * $scoredk_money;
                        }
                        if ($manjian_money) {
                            $og_manjian_money = $og_totalprice / $allproduct_price * $manjian_money;
                        }
                    }
                    $og_totalprice = round($og_totalprice - $og_coupon_money - $og_scoredk_money - $og_manjian_money, 2);
                    if ($og_totalprice < 0) $og_totalprice = 0;
                }
                $ogdata['real_totalprice'] = $og_totalprice; //实际商品销售金额

                //计算佣金的商品金额
                $commission_totalprice = $ogdata['totalprice'];
                if ($sysset['fxjiesuantype'] == 1) {
                    $commission_totalprice = $ogdata['real_totalprice'];
                }
                $commission_totalpriceCache = $commission_totalprice;
                if ($sysset['fxjiesuantype'] == 2) { //按利润提成
                    $commission_totalprice = $ogdata['totalprice'] - $guige['cost_price'] * $num;
                    if ($commission_totalprice < 0) $commission_totalprice = 0;
                }

                $agleveldata = Db::name('member_level')->where('aid', aid)->where('id', $member['levelid'])->find();
                if ($agleveldata['can_agent'] > 0 && $agleveldata['commission1own'] == 1) {
                    $member['pid'] = $member['id'];
                }
                if ($product['commissionset'] != -1) {
                    if ($member['pid']) {
                        $parent1 = Db::name('member')->where('aid', aid)->where('id', $member['pid'])->find();
                        if ($parent1) {
                            $agleveldata1 = Db::name('member_level')->where('aid', aid)->where('id', $parent1['levelid'])->find();
                            if ($agleveldata1['can_agent'] != 0) {
                                $ogdata['parent1'] = $parent1['id'];
                            }
                        }
                    }
                    if ($parent1['pid']) {
                        $parent2 = Db::name('member')->where('aid', aid)->where('id', $parent1['pid'])->find();
                        if ($parent2) {
                            $agleveldata2 = Db::name('member_level')->where('aid', aid)->where('id', $parent2['levelid'])->find();
                            if ($agleveldata2['can_agent'] > 1) {
                                $ogdata['parent2'] = $parent2['id'];
                            }
                        }
                    }
                    if ($parent2['pid']) {
                        $parent3 = Db::name('member')->where('aid', aid)->where('id', $parent2['pid'])->find();
                        if ($parent3) {
                            $agleveldata3 = Db::name('member_level')->where('aid', aid)->where('id', $parent3['levelid'])->find();
                            if ($agleveldata3['can_agent'] > 2) {
                                $ogdata['parent3'] = $parent3['id'];
                            }
                        }
                    }
                    if ($product['commissionset'] == 1) {//按商品设置的分销比例
                        $commissiondata = json_decode($product['commissiondata1'], true);
                        if ($commissiondata) {
                            $ogdata['parent1commission'] = $commissiondata[$agleveldata1['id']]['commission1'] * $commission_totalprice * 0.01;
                            $ogdata['parent2commission'] = $commissiondata[$agleveldata2['id']]['commission2'] * $commission_totalprice * 0.01;
                            $ogdata['parent3commission'] = $commissiondata[$agleveldata3['id']]['commission3'] * $commission_totalprice * 0.01;
                        }
                    } elseif ($product['commissionset'] == 2) {//按固定金额
                        $commissiondata = json_decode($product['commissiondata2'], true);
                        if ($commissiondata) {
                            if (getcustom('fengdanjiangli') && $product['fengdanjiangli']) {

                            } else {
                                $ogdata['parent1commission'] = $commissiondata[$agleveldata1['id']]['commission1'] * $num;
                                $ogdata['parent2commission'] = $commissiondata[$agleveldata2['id']]['commission2'] * $num;
                                $ogdata['parent3commission'] = $commissiondata[$agleveldata3['id']]['commission3'] * $num;
                            }
                        }
                    } elseif ($product['commissionset'] == 3) {//提成是积分
                        $commissiondata = json_decode($product['commissiondata3'], true);
                        if ($commissiondata) {
                            $ogdata['parent1score'] = $commissiondata[$agleveldata1['id']]['commission1'] * $num;
                            $ogdata['parent2score'] = $commissiondata[$agleveldata2['id']]['commission2'] * $num;
                            $ogdata['parent3score'] = $commissiondata[$agleveldata3['id']]['commission3'] * $num;
                        }
                    } else { //按会员等级设置的分销比例
                        if ($agleveldata1['commissiontype'] == 1) { //固定金额按单
                            $ogdata['parent1commission'] = $agleveldata1['commission1'];
                            $ogdata['parent2commission'] = $agleveldata2['commission2'];
                            $ogdata['parent3commission'] = $agleveldata3['commission3'];
                            $istc = 1;
                        } else {
                            $ogdata['parent1commission'] = $agleveldata1['commission1'] * $commission_totalprice * 0.01;
                            $ogdata['parent2commission'] = $agleveldata2['commission2'] * $commission_totalprice * 0.01;
                            $ogdata['parent3commission'] = $agleveldata3['commission3'] * $commission_totalprice * 0.01;
                        }
                    }
                }
            }
            $ogid = Db::name('shop_order_goods')->insertGetId($ogdata);
            if ($ogdata['parent1'] && ($ogdata['parent1commission'] > 0 || $ogdata['parent1score'] > 0)) {
                Db::name('member_commission_record')->insert(['aid' => aid, 'mid' => $ogdata['parent1'], 'frommid' => $member['id'], 'orderid' => $orderid, 'ogid' => $ogid, 'type' => 'shop', 'commission' => $ogdata['parent1commission'], 'score' => $ogdata['parent1score'], 'remark' => '下级购买商品奖励', 'createtime' => time()]);
            }
            if ($ogdata['parent2'] && ($ogdata['parent2commission'] || $ogdata['parent2score'])) {
                Db::name('member_commission_record')->insert(['aid' => aid, 'mid' => $ogdata['parent2'], 'frommid' => $member['id'], 'orderid' => $orderid, 'ogid' => $ogid, 'type' => 'shop', 'commission' => $ogdata['parent2commission'], 'score' => $ogdata['parent2score'], 'remark' => '下二级购买商品奖励', 'createtime' => time()]);
            }
            if ($ogdata['parent3'] && ($ogdata['parent3commission'] || $ogdata['parent3score'])) {
                Db::name('member_commission_record')->insert(['aid' => aid, 'mid' => $ogdata['parent3'], 'frommid' => $member['id'], 'orderid' => $orderid, 'ogid' => $ogid, 'type' => 'shop', 'commission' => $ogdata['parent3commission'], 'score' => $ogdata['parent3score'], 'remark' => '下三级购买商品奖励', 'createtime' => time()]);
            }


            if ($product['commissionset4'] == 1 && $product['lvprice'] == 1) { //极差分销
                if ($member['path']) {
                    $parentList = Db::name('member')->where('id', 'in', $member['path'])->order(Db::raw('field(id,' . $member['path'] . ')'))->select()->toArray();
                    if ($parentList) {
                        $parentList = array_reverse($parentList);
                        $lvprice_data = json_decode($guige['lvprice_data'], true);
                        $nowprice = $commission_totalpriceCache;
                        $giveidx = 0;
                        foreach ($parentList as $k => $parent) {
                            if ($parent['levelid'] && $lvprice_data[$parent['levelid']]) {
                                $thisprice = floatval($lvprice_data[$parent['levelid']]) * $num;
                                if ($nowprice > $thisprice) {
                                    $commission = $nowprice - $thisprice;
                                    $nowprice = $thisprice;
                                    $giveidx++;
                                    //if($giveidx <=3){
                                    //	$ogupdate['parent'.$giveidx] = $parent['id'];
                                    //	$ogupdate['parent'.$giveidx.'commission'] = $commission;
                                    //}
                                    Db::name('member_commission_record')->insert(['aid' => aid, 'mid' => $parent['id'], 'frommid' => $member['id'], 'orderid' => $orderid, 'ogid' => $ogid, 'type' => 'shop', 'commission' => $commission, 'score' => 0, 'remark' => '下级购买商品差价', 'createtime' => time()]);
                                }
                            }
                        }
                    }
                }
            }

            Db::name('shop_guige')->where('aid', aid)->where('id', $guige['id'])->update(['stock' => Db::raw("stock-$num"), 'sales' => Db::raw("sales+$num")]);
            Db::name('shop_product')->where('aid', aid)->where('id', $product['id'])->update(['stock' => Db::raw("stock-$num"), 'sales' => Db::raw("sales+$num")]);

            \app\model\Payorder::shop_pay($orderid);
            \app\common\System::plog('商城订单录入' . $orderid);
        }

        return json(['status' => 1, 'msg' => '录单成功', 'url' => true]);
    }

    public function getUser()
    {
        $mid = input('param.mid');
        $info = Db::name('member')->where('aid', aid)->where('id', $mid)->field('nickname,headimg,levelid')->find();
        return json($info);
    }
}