<?php


// +----------------------------------------------------------------------
// | 商品海报
// +----------------------------------------------------------------------
namespace app\controller;

use think\facade\Db;
use think\facade\View;

class ShopPoster extends Common
{
    public function initialize()
    {
        parent::initialize();
        if (bid > 0) showmsg('无访问权限');
    }

    public function index()
    {
        $type = input('param.type') ? input('param.type') : $this->platform[0];
        $posterset = Db::name('admin_set_poster')->where('aid', aid)->where('type', 'product')->where('platform', $type)->order('id')->find();
        $posterdata = json_decode($posterset['content'], true);

        $poster_bg = $posterdata['poster_bg'];
        $poster_data = $posterdata['poster_data'];

        View::assign('type', $type);
        View::assign('poster_bg', $poster_bg);
        View::assign('poster_data', $poster_data);
        return View::fetch();
    }

    public function save()
    {
        $type = input('param.type') ? input('param.type') : $this->platform[0];
        $poster_bg = input('post.poster_bg');
        $poster_data = input('post.poster_data');
        $data_index = ['poster_bg' => $poster_bg, 'poster_data' => json_decode($poster_data)];
        $posterset = Db::name('admin_set_poster')->where('aid', aid)->where('type', 'product')->where('platform', $type)->order('id')->find();
        Db::name('admin_set_poster')->where('id', $posterset['id'])->update(['content' => json_encode($data_index)]);
        if (input('post.clearhistory') == 1) {
            Db::name('member_poster')->where('aid', aid)->where('type', 'product')->where('posterid', $posterset['id'])->delete();
            $msg = '保存成功';
        } else {
            $msg = '保存成功';
        }
        \app\common\System::plog('商城海报设置');
        return json(['status' => 1, 'msg' => $msg, 'url' => true]);
    }
}