<?php


// +----------------------------------------------------------------------
// | 商城-商品管理
// +----------------------------------------------------------------------
namespace app\controller;

use app\common\Wechat;
use think\facade\Db;
use think\facade\View;

class ShopProduct extends Common
{
    //商品列表
    public function index()
    {
        if (request()->isAjax()) {
            $page = input('param.page');
            $limit = input('param.limit');
            if (input('param.field') && input('param.order')) {
                $order = input('param.field') . ' ' . input('param.order');
            } else {
                $order = 'sort desc,id desc';
            }
            $where = array();
            $where[] = ['aid', '=', aid];
            if (bid == 0) {
                if (input('param.bid')) {
                    $where[] = ['bid', '=', input('param.bid')];
                } elseif (input('param.showtype') == 2) {
                    $where[] = ['bid', '>', 0];
                    $where[] = ['linkid', '=', 0];
                } elseif (input('param.showtype') == 21) {
                    $where[] = ['bid', '=', -1];
                } else {
                    $where[] = ['bid', '=', 0];
                }
            } else {
                $where[] = ['bid', '=', bid];
            }
            $where[] = ['douyin_product_id', '=', ''];
            if (input('param.name')) $where[] = ['name', 'like', '%' . $_GET['name'] . '%'];
            if (input('?param.status') && input('param.status') !== '') {
                $status = input('param.status');
                $nowtime = time();
                $nowhm = date('H:i');
                if ($status == 1) {
                    $where[] = Db::raw("`status`=1 or (`status`=2 and unix_timestamp(start_time)<=$nowtime and unix_timestamp(end_time)>=$nowtime) or (`status`=3 and ((start_hours<end_hours and start_hours<='$nowhm' and end_hours>='$nowhm') or (start_hours>=end_hours and (start_hours<='$nowhm' or end_hours>='$nowhm'))) )");
                } else {
                    $where[] = Db::raw("`status`=0 or (`status`=2 and (unix_timestamp(start_time)>$nowtime or unix_timestamp(end_time)<$nowtime)) or (`status`=3 and ((start_hours<end_hours and (start_hours>'$nowhm' or end_hours<'$nowhm')) or (start_hours>=end_hours and (start_hours>'$nowhm' and end_hours<'$nowhm'))) )");
                }
            }
            if (input('?param.cid') && input('param.cid') !== '') {
                $cid = input('param.cid');
                //子分类
                $clist = Db::name('shop_category')->where('aid', aid)->where('pid', $cid)->column('id');
                if ($clist) {
                    $clist2 = Db::name('shop_category')->where('aid', aid)->where('pid', 'in', $clist)->column('id');
                    $cCate = array_merge($clist, $clist2, [$cid]);
                    if ($cCate) {
                        $whereCid = [];
                        foreach ($cCate as $k => $c2) {
                            $whereCid[] = "find_in_set({$c2},cid)";
                        }
                        $where[] = Db::raw(implode(' or ', $whereCid));
                    }
                } else {
                    $where[] = Db::raw("find_in_set(" . $cid . ",cid)");
                }
            }
            if (input('?param.cid2') && input('param.cid2') !== '') {
                $cid = input('param.cid2');
                //子分类
                $clist = Db::name('shop_category2')->where('aid', aid)->where('pid', $cid)->column('id');
                if ($clist) {
                    $clist2 = Db::name('shop_category2')->where('aid', aid)->where('pid', 'in', $clist)->column('id');
                    $cCate = array_merge($clist, $clist2, [$cid]);
                    if ($cCate) {
                        $whereCid = [];
                        foreach ($cCate as $k => $c2) {
                            $whereCid[] = "find_in_set({$c2},cid2)";
                        }
                        $where[] = Db::raw(implode(' or ', $whereCid));
                    }
                } else {
                    $where[] = Db::raw("find_in_set(" . $cid . ",cid2)");
                }
            }
            if (input('?param.gid') && input('param.gid') !== '') $where[] = Db::raw("find_in_set(" . input('param.gid/d') . ",gid)");

            if (input('?param.wxvideo_status') && input('param.wxvideo_status') !== '') {
                if (input('param.wxvideo_status') < 5) {
                    if (input('param.wxvideo_status') == 0) {
                        $where[] = ['wxvideo_product_id', '=', ''];
                    } else {
                        $where[] = ['wxvideo_edit_status', '=', input('param.wxvideo_status')];
                    }
                } else {
                    $where[] = ['wxvideo_status', '=', input('param.wxvideo_status')];
                }
            }
            $count = 0 + Db::name('shop_product')->where($where)->count();
            $data = Db::name('shop_product')->where($where)->page($page, $limit)->order($order)->select()->toArray();

            $cdata = Db::name('shop_category')->where('aid', aid)->column('name', 'id');
            if (bid > 0) {
                $cdata2 = Db::name('shop_category2')->Field('id,name')->where('aid', aid)->where('bid', bid)->order('sort desc,id')->column('name', 'id');
            }
            foreach ($data as $k => $v) {
                $gglist = Db::name('shop_guige')->where('aid', aid)->where('proid', $v['id'])->select()->toArray();
                $ggdata = array();
                foreach ($gglist as $gg) {
                    $ggdata[] = $gg['name'] . ' × ' . $gg['stock'] . ' <button class="layui-btn layui-btn-xs layui-btn-disabled">￥' . $gg['sell_price'] . '</button>';
                }
                $v['cid'] = explode(',', $v['cid']);
                $data[$k]['cname'] = null;
                if ($v['cid']) {
                    foreach ($v['cid'] as $cid) {
                        if ($data[$k]['cname'])
                            $data[$k]['cname'] .= ' ' . $cdata[$cid];
                        else
                            $data[$k]['cname'] .= $cdata[$cid];
                    }
                }
                if ($v['bid'] > 0) {
                    $v['cid2'] = explode(',', $v['cid2']);
                    $data[$k]['cname2'] = null;
                    if ($v['cid2']) {
                        foreach ($v['cid2'] as $cid) {
                            if ($data[$k]['cname2'])
                                $data[$k]['cname2'] .= ' ' . $cdata2[$cid];
                            else
                                $data[$k]['cname2'] .= $cdata2[$cid];
                        }
                    }
                    $data[$k]['bname'] = Db::name('business')->where('aid', aid)->where('id', $v['bid'])->value('name');
                } else {
                    $data[$k]['cname2'] = '';
                    $data[$k]['bname'] = '平台自营';
                }
                $data[$k]['ggdata'] = implode('<br>', $ggdata);
                $data[$k]['realsalenum'] = Db::name('shop_order_goods')->where('aid', aid)->where('proid', $v['id'])->where('status', 'in', '1,2,3')->sum('num');
                if ($v['status'] == 2) { //设置上架时间
                    if (strtotime($v['start_time']) <= time() && strtotime($v['end_time']) >= time()) {
                        $data[$k]['status'] = 1;
                    } else {
                        $data[$k]['status'] = 0;
                    }
                }
                if ($v['status'] == 3) { //设置上架周期
                    $start_time = strtotime(date('Y-m-d ' . $v['start_hours']));
                    $end_time = strtotime(date('Y-m-d ' . $v['end_hours']));
                    if (($start_time < $end_time && $start_time <= time() && $end_time >= time()) || ($start_time >= $end_time && ($start_time <= time() || $end_time >= time()))) {
                        $data[$k]['status'] = 1;
                    } else {
                        $data[$k]['status'] = 0;
                    }
                }
                if ($v['bid'] == -1) $data[$k]['sort'] = $v['sort'] - 1000000;
                 // 货币单位
               switch ($data[$k]['currency']) {
                        case '1':
                            $data[$k]['sell_price'] = '¥ '. $data[$k]['sell_price'];
                            break;                        
                        case '2':
                            $data[$k]['sell_price'] = 'NT$ '.$data[$k]['sell_price'];
                            break;
                        case '3':
                            $data[$k]['sell_price'] = '$ '.$data[$k]['sell_price'];
                            break;
                        case '4':
                            $data[$k]['sell_price'] = '₫ '.$data[$k]['sell_price'];
                            break;
                        case '5':
                            $data[$k]['sell_price'] = '฿ '.$data[$k]['sell_price'];
                            break;
                        case '6':
                            $data[$k]['sell_price'] = '₹ '.$data[$k]['sell_price'];
                            break;
                        case '7':
                            $data[$k]['sell_price'] = 'RM '.$data[$k]['sell_price'];
                            break;
                        default:
                            $data[$k]['sell_price'] = '¥ '.$data[$k]['sell_price'];
                            break;
                    }
            }
            return json(['code' => 0, 'msg' => '查询成功', 'count' => $count, 'data' => $data]);
        }
        //分类
        $clist = Db::name('shop_category')->Field('id,name')->where('aid', aid)->where('pid', 0)->order('sort desc,id')->select()->toArray();
        foreach ($clist as $k => $v) {
            $clist[$k]['child'] = Db::name('shop_category')->Field('id,name')->where('aid', aid)->where('pid', $v['id'])->order('sort desc,id')->select()->toArray();
        }
        if (bid > 0) {
            //商家的商品分类
            $clist2 = Db::name('shop_category2')->Field('id,name')->where('aid', aid)->where('bid', bid)->where('pid', 0)->order('sort desc,id')->select()->toArray();
            foreach ($clist2 as $k => $v) {
                $clist2[$k]['child'] = Db::name('shop_category')->Field('id,name')->where('aid', aid)->where('pid', $v['id'])->order('sort desc,id')->select()->toArray();
            }
            View::assign('clist2', $clist2);
        }
        //分组
        $glist = Db::name('shop_group')->where('aid', aid)->order('sort desc,id')->select()->toArray();
        View::assign('clist', $clist);
        View::assign('glist', $glist);

        $fromwxvideo = input('param.fromwxvideo') == 1 ? true : false;
        View::assign('fromwxvideo', $fromwxvideo);

        if ($fromwxvideo) {
            $rs = curl_post('https://api.weixin.qq.com/shop/account/get_brand_list?access_token=' . Wechat::access_token(aid, 'wx'), '{}');
            $rs = json_decode($rs, true);
            $brand_list = $rs['data'];
        } else {
            $brand_list = [];
        }

        if (session('BST_ID')) {
            $userlist = Db::name('admin_user')->field('id,aid,un')->where('id', '<>', $this->user['id'])->where('bid', 0)->where('isadmin', 1)->select()->toArray();
            View::assign('cancopy', true);
        } else {
            $userlist = [];
            View::assign('cancopy', false);
        }
        $default_cid = Db::name('member_level_category')->where('aid', aid)->where('isdefault', 1)->value('id');
        $default_cid = $default_cid ? $default_cid : 0;
        $levellist = Db::name('member_level')->where('aid', aid)->where('cid', $default_cid)->order('sort,id')->select()->toArray();
        View::assign('levellist', $levellist);
        $brand_list[] = ['brand_id' => **********, 'brand_wording' => '无品牌'];
        View::assign('brand_list', $brand_list);
        View::assign('userlist', $userlist);
        return View::fetch();
    }

    //编辑商品
    public function edit()
    {
        if (input('param.id')) {
            $info = Db::name('shop_product')->where('aid', aid)->where('id', input('param.id/d'))->find();
            if (!$info) showmsg('商品不存在');
            if (bid != 0 && $info['bid'] != bid) showmsg('无权限操作');
            if (bid != 0 && $info['linkid'] != 0) showmsg('无权限操作');
        }
        //多规格
        $newgglist = array();
        if ($info) {
            $gglist = Db::name('shop_guige')->where('aid', aid)->where('proid', $info['id'])->select()->toArray();
            foreach ($gglist as $k => $v) {
                $v['lvprice_data'] = json_decode($v['lvprice_data']);
                if ($v['ks'] !== null) {
                    $newgglist[$v['ks']] = $v;
                } else {
                    Db::name('shop_guige')->where('aid', aid)->where('id', $v['id'])->update(['ks' => $k]);
                    $newgglist[$k] = $v;
                }
            }
        }
        //分类
        $clist = Db::name('shop_category')->Field('id,name')->where('aid', aid)->where('pid', 0)->order('sort desc,id')->select()->toArray();
        foreach ($clist as $k => $v) {
            $child = Db::name('shop_category')->Field('id,name')->where('aid', aid)->where('pid', $v['id'])->order('sort desc,id')->select()->toArray();
            foreach ($child as $k2 => $v2) {
                $child2 = Db::name('shop_category')->Field('id,name')->where('aid', aid)->where('pid', $v2['id'])->order('sort desc,id')->select()->toArray();
                $child[$k2]['child'] = $child2;
            }
            $clist[$k]['child'] = $child;
        }
        if (bid > 0) {
            //商家的分类
            $clist2 = Db::name('shop_category2')->Field('id,name')->where('aid', aid)->where('bid', bid)->where('pid', 0)->order('sort desc,id')->select()->toArray();
            foreach ($clist2 as $k => $v) {
                $child = Db::name('shop_category2')->Field('id,name')->where('aid', aid)->where('bid', bid)->where('pid', $v['id'])->order('sort desc,id')->select()->toArray();
                foreach ($child as $k2 => $v2) {
                    $child2 = Db::name('shop_category2')->Field('id,name')->where('aid', aid)->where('bid', bid)->where('pid', $v2['id'])->order('sort desc,id')->select()->toArray();
                    $child[$k2]['child'] = $child2;
                }
                $clist2[$k]['child'] = $child;
            }
        }
        //分组
        $glist = Db::name('shop_group')->where('aid', aid)->order('sort desc,id')->select()->toArray();
        $freightdata = array();
        if ($info && $info['freightdata']) {
            $freightdata = Db::name('freight')->where('aid', aid)->where('id', 'in', $info['freightdata'])->order('sort desc,id')->select()->toArray();
        }
        $bset = Db::name('business_sysset')->where('aid', aid)->find();
        //分成结算类型
        $sysset = Db::name('admin_set')->where('aid', aid)->find();
        if ($sysset['fxjiesuantype'] == 1) {
            $jiesuantypeDesc = '成交价';
        } elseif ($sysset['fxjiesuantype'] == 2) {
            $jiesuantypeDesc = '销售利润';
        } else {
            $jiesuantypeDesc = '销售价';
        }

        $info['showtj'] = explode(',', $info['showtj']);
        $info['gettj'] = explode(',', $info['gettj']);
        $info['cid'] = explode(',', $info['cid']);
        $info['cid2'] = explode(',', $info['cid2']);
        if ($info['bid'] == -1) $info['sort'] = $info['sort'] - 1000000;
        $default_cid = Db::name('member_level_category')->where('aid', aid)->where('isdefault', 1)->value('id');
        $default_cid = $default_cid ? $default_cid : 0;
        if (getcustom('plug_businessqr') && bid != 0) {
            $aglevellist = Db::name('member_level')->where('aid', aid)->where('cid', $default_cid)->where('show_business', 1)->where('can_agent', '<>', 0)->order('sort,id')->select()->toArray();
            $levellist = Db::name('member_level')->where('aid', aid)->where('cid', $default_cid)->where('show_business', 1)->order('sort,id')->select()->toArray();
        } else {
            $aglevellist = Db::name('member_level')->where('aid', aid)->where('cid', $default_cid)->where('can_agent', '<>', 0)->order('sort,id')->select()->toArray();
            $levellist = Db::name('member_level')->where('aid', aid)->where('cid', $default_cid)->order('sort,id')->select()->toArray();
        }

        if ($info['id']) {
            $fuwulist = Db::name('shop_fuwu')->where('aid', aid)->where('bid', $info['bid'])->order('sort desc,id')->select()->toArray();
        } else {
            $fuwulist = Db::name('shop_fuwu')->where('aid', aid)->where('bid', bid)->order('sort desc,id')->select()->toArray();
        }
        if (getcustom('everyday_hongbao')) {
            $hset = \db('hongbao_everyday')->where('aid', aid)->find();
            View::assign('ehb_status', $hset['status']);
        }
        View::assign('fuwulist', $fuwulist);
        View::assign('aglevellist', $aglevellist);
        View::assign('levellist', $levellist);
        View::assign('info', $info);
        View::assign('newgglist', $newgglist);
        View::assign('clist', $clist);
        View::assign('clist2', $clist2);
        View::assign('glist', $glist);
        View::assign('freightdata', $freightdata);
        View::assign('bset', $bset);
        View::assign('jiesuantypeDesc', $jiesuantypeDesc);
        $fromwxvideo = input('param.fromwxvideo') == 1 ? true : false;

        if ($fromwxvideo) {
            $rs = curl_post('https://api.weixin.qq.com/shop/account/get_brand_list?access_token=' . Wechat::access_token(aid, 'wx'), '{}');
            $rs = json_decode($rs, true);
            $brand_list = $rs['data'];
        } else {
            $brand_list = [];
        }
        $brand_list[] = ['brand_id' => **********, 'brand_wording' => '无品牌'];
        View::assign('brand_list', $brand_list);
        View::assign('fromwxvideo', $fromwxvideo);
        if (getcustom('plug_xiongmao')) {
            View::assign('barcode', true);
        }

        return View::fetch();
    }

    //保存商品
    public function save()
    {
        if (input('post.id')) {
            $product = Db::name('shop_product')->where('aid', aid)->where('id', input('post.id/d'))->find();
            if (!$product) showmsg('商品不存在');
            if (bid != 0 && $product['bid'] != bid) showmsg('无权限操作');
        }
        $info = input('post.info/a');
        if (intval($info['sort']) >= 1000000) showmsg('商品序号不能大于1000000');
        $info['detail'] = \app\common\Common::geteditorcontent($info['detail']);
        $data = array();
        $data['name'] = $info['name'];
        $data['en_name'] = $info['en_name'];
        $data['th_name'] = $info['th_name'];
        $data['pic'] = $info['pic'];
        $data['pics'] = $info['pics'];
        $data['procode'] = $info['procode'];
        $data['sellpoint'] = $info['sellpoint'];
        $data['cid'] = $info['cid'];
    //    if ($info['currency']=='-请选择货币-') {
    //     return json(['status' => 0, 'msg' => '请选择货币']);
    //    }
        $data['currency'] = isset($info['currency'])?$info['currency']:5;
        if (bid > 0) {
            $data['cid2'] = $info['cid2'];
        }
        $data['price_type'] = $info['price_type'];
        $data['freighttype'] = $info['freighttype'];
        $data['freightdata'] = $info['freightdata'];
        $data['freightcontent'] = $info['freightcontent'];

        $data['commissionset'] = $info['commissionset'];
        $data['commissiondata1'] = jsonEncode(input('post.commissiondata1/a'));
        $data['commissiondata2'] = jsonEncode(input('post.commissiondata2/a'));
        $data['commissiondata3'] = jsonEncode(input('post.commissiondata3/a'));
        $data['commissionset4'] = $info['commissionset4'];
        $data['lvprice'] = $info['lvprice'];
        $data['showtj'] = implode(',', $info['showtj']);
        $data['gettj'] = implode(',', $info['gettj']);

        $data['fenhongset'] = $info['fenhongset'];

        if (bid != 0) {
            $bset = Db::name('business_sysset')->where('aid', aid)->find();
            if ($bset['commission_canset'] == 0) {
                $data['commissionset'] = '-1';
            }
            if ($bset['product_showset'] == 0) {
                $data['showtj'] = '-1';
                $data['gettj'] = '-1';
                // $data['lvprice'] = 0;
            }
        }

        $data['video'] = $info['video'];
        $data['video_duration'] = $info['video_duration'];
        $data['perlimit'] = $info['perlimit'];
        $data['perlimitdan'] = $info['perlimitdan'];
        $data['limit_start'] = $info['limit_start'];
        if (bid == 0) {
            $data['scoredkmaxset'] = $info['scoredkmaxset'];
            $data['scoredkmaxval'] = $info['scoredkmaxval'];
            $data['feepercent'] = $info['feepercent'] == '' || $info['feepercent'] < 0 ? null : $info['feepercent'];//商品独立抽成费率
        }

        if ($info['oldsales'] != $info['sales']) {
            $data['sales'] = $info['sales'];
        }
        $data['boss_sales'] = $info['boss_sales'] ?? 0;
        $data['sort'] = $info['sort'];
        $data['status'] = $info['status'];
        $data['start_time'] = $info['start_time'];
        $data['end_time'] = $info['end_time'];
        $data['start_hours'] = $info['start_hours'];
        $data['end_hours'] = $info['end_hours'];

        $data['detail'] = $info['detail'];


        $data['sharetitle'] = $info['sharetitle'];
        $data['sharepic'] = $info['sharepic'];
        $data['sharedesc'] = $info['sharedesc'];
        $data['sharelink'] = $info['sharelink'];

        if ($info['gid']) {
            $data['gid'] = implode(',', $info['gid']);
        } else {
            $data['gid'] = '';
        }
        if ($info['fwid']) {
            $data['fwid'] = implode(',', $info['fwid']);
        } else {
            $data['fwid'] = '';
        }
        if (!$product) $data['createtime'] = time();
        $data['gettjtip'] = $info['gettjtip'];
        $data['gettjurl'] = $info['gettjurl'];

        if (getcustom('payaftertourl')) {
            $data['payaftertourl'] = $info['payaftertourl'];
            $data['payafterbtntext'] = $info['payafterbtntext'];
        }
        if (getcustom('to86yk')) {
            $data['to86yk_tid'] = $info['to86yk_tid'];
        }
        $data['no_discount'] = $info['no_discount'];
        if (getcustom('plug_xiongmao')) {
            $data['barcode'] = $info['barcode'];
        }
        if (getcustom('everyday_hongbao')) {
            $data['everyday_hongbao_bl'] = $info['everyday_hongbao_bl'] != '' ? $info['everyday_hongbao_bl'] : null;
        }
        if (getcustom('fengdanjiangli')) {
            $data['fengdanjiangli'] = $info['fengdanjiangli'] ? $info['fengdanjiangli'] : '';
        }

        if ($info['wxvideo_third_cat_id']) $data['wxvideo_third_cat_id'] = $info['wxvideo_third_cat_id'];
        if ($info['wxvideo_brand_id']) $data['wxvideo_brand_id'] = $info['wxvideo_brand_id'];
        if ($info['wxvideo_qualification_pics']) $data['wxvideo_qualification_pics'] = $info['wxvideo_qualification_pics'];

        if ($info['lvprice'] == 1) {
            $default_cid = Db::name('member_level_category')->where('aid', aid)->where('isdefault', 1)->value('id');
            $default_cid = $default_cid ? $default_cid : 0;
            $levellist = Db::name('member_level')->where('aid', aid)->where('cid', $default_cid)->order('sort,id')->select()->toArray();
            $defaultlvid = $levellist[0]['id'];
            $sellprice_field = 'sell_price_' . $defaultlvid;
        } else {
            $sellprice_field = 'sell_price';
        }
        $sell_price = 0;
        $market_price = 0;
        $cost_price = 0;
        $weight = 0;
        $givescore = 0;
        $lvprice_data = [];
        foreach (input('post.option/a') as $ks => $v) {
            if ($sell_price == 0 || $v[$sellprice_field] < $sell_price) {
                $sell_price = $v[$sellprice_field];
                $market_price = $v['market_price'];
                $cost_price = $v['cost_price'];
                $givescore = $v['givescore'];
                $weight = $v['weight'];
                if ($info['lvprice'] == 1) {
                    $lvprice_data = [];
                    foreach ($levellist as $lv) {
                        $lvprice_data[$lv['id']] = $v['sell_price_' . $lv['id']];
                    }
                }
            }
        }
        if ($info['lvprice'] == 1) {
            $data['lvprice_data'] = json_encode($lvprice_data);
        }

        $data['market_price'] = $market_price;
        $data['cost_price'] = $cost_price;
        $data['sell_price'] = $sell_price;
        $data['givescore'] = $givescore;
        $data['weight'] = $weight;
        $data['stock'] = 0;
        if (in_array('buy_selectmember', getcustom())) {
            $data['balance'] = $info['balance'];
        }
        foreach (input('post.option/a') as $v) {
            $data['stock'] += $v['stock'];
        }
        //多规格 规格项
        $data['guigedata'] = input('post.specs');

        if (bid != 0) {
            $bset = Db::name('business_sysset')->where('aid', aid)->find();
            if ($bset['product_check'] == 1) {
                $data['ischecked'] = 0;
            }
        }
        if ($product) {
            if ($product['bid'] == -1) $data['sort'] = 1000000 + intval($data['sort']);
            Db::name('shop_product')->where('aid', aid)->where('id', $product['id'])->update($data);
            $proid = $product['id'];
            \app\common\System::plog('商城商品编辑' . $proid);
        } else {
            $data['aid'] = aid;
            $data['bid'] = bid;
            if (bid == 0 && $info['bid']) {
                $data['bid'] = $info['bid'];
                if ($info['bid'] == -1) $data['sort'] = 1000000 + intval($data['sort']);
            }
            $proid = Db::name('shop_product')->insertGetId($data);
            \app\common\System::plog('商城商品编辑' . $proid);
        }
        //dump(input('post.option/a'));die;
        //多规格
        $newggids = array();
        foreach (input('post.option/a') as $ks => $v) {
            $ggdata = array();
            $ggdata['proid'] = $proid;
            $ggdata['ks'] = $ks;
            $ggdata['name'] = $v['name'];
            $ggdata['pic'] = $v['pic'] ? $v['pic'] : '';
            $ggdata['market_price'] = $v['market_price'] > 0 ? $v['market_price'] : 0;
            if (getcustom('plug_huangfeihong')) {
                $ggdata['cost_price'] = $v['cost_price'] ? $v['cost_price'] : 0;
            } else {
                $ggdata['cost_price'] = $v['cost_price'] > 0 ? $v['cost_price'] : 0;
            }
            $ggdata['sell_price'] = $v['sell_price'] > 0 ? $v['sell_price'] : 0;
            $ggdata['weight'] = $v['weight'] > 0 ? $v['weight'] : 0;
            $ggdata['procode'] = $v['procode'];
            $ggdata['givescore'] = $v['givescore'];
            $ggdata['stock'] = $v['stock'] > 0 ? $v['stock'] : 0;
            $ggdata['limit_start'] = $v['limit_start'] > 0 ? $v['limit_start'] : 0;
            $lvprice_data = [];
            if ($info['lvprice'] == 1) {
                $ggdata['sell_price'] = $v['sell_price_' . $levellist[0]['id']] > 0 ? $v['sell_price_' . $levellist[0]['id']] : 0;
                foreach ($levellist as $lv) {
                    $sell_price = $v['sell_price_' . $lv['id']] > 0 ? $v['sell_price_' . $lv['id']] : 0;
                    $lvprice_data[$lv['id']] = $sell_price;
                }
                $ggdata['lvprice_data'] = json_encode($lvprice_data);
            }

            $guige = Db::name('shop_guige')->where('aid', aid)->where('proid', $proid)->where('ks', $ks)->find();
            if ($guige) {
                Db::name('shop_guige')->where('aid', aid)->where('id', $guige['id'])->update($ggdata);
                $ggid = $guige['id'];
            } else {
                $ggdata['aid'] = aid;
                $ggid = Db::name('shop_guige')->insertGetId($ggdata);
            }
            $newggids[] = $ggid;
        }
        Db::name('shop_guige')->where('aid', aid)->where('proid', $proid)->where('id', 'not in', $newggids)->delete();

        $this->tongbuproduct($proid);

        \app\common\Wxvideo::updateproduct($proid);

        return json(['status' => 1, 'msg' => '操作成功', 'url' => (string)url('index')]);
    }

    //改价格

    private function tongbuproduct($proids)
    {
        if (!getcustom('plug_businessqr')) {
            return;
        }
        if (!is_array($proids)) {
            $proids = explode(',', $proids);
        }
        $blist = [];
        foreach ($proids as $proid) {
            $product = Db::name('shop_product')->where('aid', aid)->where('id', $proid)->find();
            if ($product && $product['bid'] == -1) {
                $gglist = Db::name('shop_guige')->where('aid', aid)->where('proid', $product['id'])->select()->toArray();
                if (!$blist) {
                    $blist = Db::name('business')->where('aid', aid)->order('sort desc,id desc')->select()->toArray();
                }
                foreach ($blist as $business) {
                    $bpro = Db::name('shop_product')->where('aid', aid)->where('bid', $business['id'])->where('linkid', $product['id'])->find();
                    $data = $product;
                    $data['bid'] = $business['id'];
                    $data['linkid'] = $product['id'];
                    unset($data['id']);
                    unset($data['wxvideo_product_id']);
                    unset($data['wxvideo_edit_status']);
                    unset($data['wxvideo_status']);
                    unset($data['wxvideo_reject_reason']);
                    if ($bpro) {
                        Db::name('shop_product')->where('id', $bpro['id'])->update($data);
                        $newproid = $bpro['id'];
                    } else {
                        $newproid = Db::name('shop_product')->insertGetId($data);
                    }

                    $newggids = [];
                    foreach ($gglist as $gg) {
                        $ggdata = $gg;
                        $ggdata['proid'] = $newproid;
                        unset($ggdata['id']);

                        $guige = Db::name('shop_guige')->where('aid', aid)->where('proid', $newproid)->where('ks', $ggdata['ks'])->find();
                        if ($guige) {
                            Db::name('shop_guige')->where('aid', aid)->where('id', $guige['id'])->update($ggdata);
                            $ggid = $guige['id'];
                        } else {
                            $ggid = Db::name('shop_guige')->insertGetId($ggdata);
                        }
                        $newggids[] = $ggid;
                    }
                    Db::name('shop_guige')->where('aid', aid)->where('proid', $newproid)->where('id', 'not in', $newggids)->delete();
                }
            }
        }
    }

    //改状态

    public function changeprice()
    {
        $proid = input('post.proid/d');
        $product = Db::name('shop_product')->where('aid', aid)->where('id', $proid)->find();
        if (!$product) showmsg('商品不存在');
        if (bid != 0 && $product['bid'] != bid) showmsg('无权限操作');

        if ($product['lvprice'] == 1) {
            $default_cid = Db::name('member_level_category')->where('aid', aid)->where('isdefault', 1)->value('id');
            $default_cid = $default_cid ? $default_cid : 0;
            $levellist = Db::name('member_level')->where('aid', aid)->where('cid', $default_cid)->order('sort,id')->select()->toArray();
            $defaultlvid = $levellist[0]['id'];
            $sellprice_field = 'sell_price_' . $defaultlvid;
        } else {
            $sellprice_field = 'sell_price';
        }

        $sell_price = 0;
        $market_price = 0;
        $cost_price = 0;
        $lvprice_data = [];
        foreach (input('post.option/a') as $ks => $v) {
            if ($sell_price == 0 || $v[$sellprice_field] < $sell_price) {
                $sell_price = $v[$sellprice_field];
                $market_price = $v['market_price'];
                $cost_price = $v['cost_price'];
                if ($product['lvprice'] == 1) {
                    $lvprice_data = [];
                    foreach ($levellist as $lv) {
                        $lvprice_data[$lv['id']] = $v['sell_price_' . $lv['id']];
                    }
                }
            }
        }
        $data = [];
        if ($product['lvprice'] == 1) {
            $data['lvprice_data'] = json_encode($lvprice_data);
        }
        $data['market_price'] = $market_price;
        $data['cost_price'] = $cost_price;
        $data['sell_price'] = $sell_price;
        $data['stock'] = 0;
        foreach (input('post.option/a') as $v) {
            $data['stock'] += $v['stock'];
        }
        Db::name('shop_product')->where('aid', aid)->where('id', $proid)->update($data);
        foreach (input('post.option/a') as $ks => $v) {
            $ggdata = [];
            $ggdata['market_price'] = $v['market_price'] > 0 ? $v['market_price'] : 0;
            $ggdata['cost_price'] = $v['cost_price'] > 0 ? $v['cost_price'] : 0;
            $ggdata['sell_price'] = $v['sell_price'] > 0 ? $v['sell_price'] : 0;
            $ggdata['stock'] = $v['stock'] > 0 ? $v['stock'] : 0;
            $lvprice_data = [];
            if ($product['lvprice'] == 1) {
                $ggdata['sell_price'] = $v['sell_price_' . $levellist[0]['id']] > 0 ? $v['sell_price_' . $levellist[0]['id']] : 0;
                foreach ($levellist as $lv) {
                    $sell_price = $v['sell_price_' . $lv['id']] > 0 ? $v['sell_price_' . $lv['id']] : 0;
                    $lvprice_data[$lv['id']] = $sell_price;
                }
                $ggdata['lvprice_data'] = json_encode($lvprice_data);
            }
            Db::name('shop_guige')->where('aid', aid)->where('id', $v['ggid'])->update($ggdata);
        }
        \app\common\Wxvideo::update_without_audit($proid);

        return json(['status' => 1, 'msg' => '操作成功']);
    }

    //审核

    public function setst()
    {
        $st = input('post.st/d');
        $ids = input('post.ids/a');
        $where = [];
        $where[] = ['aid', '=', aid];
        $where[] = ['id', 'in', $ids];
        if (bid != 0) {
            $where[] = ['bid', '=', bid];
            $where[] = ['linkid', '=', 0];
        }
        Db::name('shop_product')->where($where)->update(['status' => $st]);

        $this->tongbuproduct($ids);
        if ($st == 0) {
            \app\common\Wxvideo::delisting($ids);
        } else {
            \app\common\Wxvideo::listing($ids);
        }
        \app\common\System::plog('商城商品改状态' . implode(',', $ids));
        return json(['status' => 1, 'msg' => '操作成功']);
    }

    //删除

    public function setcheckst()
    {
        $st = input('post.st/d');
        $id = input('post.id/d');
        $reason = input('post.reason');
        Db::name('shop_product')->where('aid', aid)->where('id', $id)->update(['ischecked' => $st, 'check_reason' => $reason]);
        return json(['status' => 1, 'msg' => '操作成功']);
    }

    //复制商品

    public function del()
    {
        $ids = input('post.ids/a');
        if (!$ids) $ids = array(input('post.id/d'));
        $where = [];
        $where[] = ['aid', '=', aid];
        $where[] = ['id', 'in', $ids];
        if (bid != 0) {
            $where[] = ['bid', '=', bid];
            $where[] = ['linkid', '=', 0];
        }
        $prolist = Db::name('shop_product')->where($where)->select();
        foreach ($prolist as $pro) {
            Db::name('shop_product')->where('id', $pro['id'])->delete();
            Db::name('shop_guige')->where('proid', $pro['id'])->delete();
            if (getcustom('plug_businessqr') && $pro['bid'] == -1) {
                $prolist2 = Db::name('shop_product')->where('linkid', $pro['id'])->select();
                foreach ($prolist2 as $pro2) {
                    Db::name('shop_product')->where('id', $pro2['id'])->delete();
                    Db::name('shop_guige')->where('proid', $pro2['id'])->delete();
                }
            }
            if ($pro['wxvideo_product_id']) {
                \app\common\Wxvideo::deleteproduct($pro['id']);
            }
        }
        \app\common\System::plog('商城商品删除' . implode(',', $ids));
        return json(['status' => 1, 'msg' => '删除成功']);
    }

    //获取分类信息

    public function procopy()
    {
        $product = Db::name('shop_product')->where('aid', aid)->where('bid', bid)->where('id', input('post.id/d'))->find();
        if (!$product) return json(['status' => 0, 'msg' => '商品不存在,请重新选择']);
        $gglist = Db::name('shop_guige')->where('aid', aid)->where('proid', $product['id'])->select()->toArray();
        $data = $product;
        $data['name'] = '复制-' . $data['name'];
        unset($data['id']);
        unset($data['wxvideo_product_id']);
        unset($data['wxvideo_edit_status']);
        unset($data['wxvideo_status']);
        unset($data['wxvideo_reject_reason']);
        $data['status'] = 0;
        $newproid = Db::name('shop_product')->insertGetId($data);
        foreach ($gglist as $gg) {
            $ggdata = $gg;
            $ggdata['proid'] = $newproid;
            unset($ggdata['id']);
            unset($ggdata['linkid']);
            Db::name('shop_guige')->insert($ggdata);
        }
        $this->tongbuproduct($newproid);
        \app\common\System::plog('商城商品复制' . $newproid);
        return json(['status' => 1, 'msg' => '复制成功', 'proid' => $newproid]);
    }

    //复制到其他账号商品

    public function getcategory()
    {
        if (!session('BST_ID')) return json(['status' => 0, 'msg' => '无权限操作']);
        $toaid = input('param.toaid/d');
        //分类
        $clist = Db::name('shop_category')->Field('id,name')->where('aid', $toaid)->where('pid', 0)->order('sort desc,id')->select()->toArray();
        foreach ($clist as $k => $v) {
            $child = Db::name('shop_category')->Field('id,name')->where('aid', $toaid)->where('pid', $v['id'])->order('sort desc,id')->select()->toArray();
            foreach ($child as $k2 => $v2) {
                $child2 = Db::name('shop_category')->Field('id,name')->where('aid', $toaid)->where('pid', $v2['id'])->order('sort desc,id')->select()->toArray();
                $child[$k2]['child'] = $child2;
            }
            $clist[$k]['child'] = $child;
        }
        return json(['status' => 1, 'data' => $clist]);
    }

    //导入商品

    public function userProcopy()
    {
        if (!session('BST_ID')) return json(['status' => 0, 'msg' => '无权限操作']);
        $ids = input('post.ids/a');
        $toaid = input('param.toaid');
        $tocid = input('param.tocid');
        if (!$toaid) return json(['status' => 0, 'msg' => '请选择账号']);
        if (!$tocid) return json(['status' => 0, 'msg' => '请选择分类']);
        if (!$ids) $ids = array(input('post.id/d'));
        $where = [];
        $where[] = ['aid', '=', aid];
        //$where[] = ['bid','=',bid];
        $where[] = ['id', 'in', $ids];
        $prolist = Db::name('shop_product')->where($where)->select()->toArray();
        if (!$prolist) return json(['status' => 0, 'msg' => '商品不存在,请重新选择']);

        foreach ($prolist as $product) {
            $gglist = Db::name('shop_guige')->where('aid', aid)->where('proid', $product['id'])->select()->toArray();
            $data = $product;
            $data['aid'] = $toaid;
            $data['bid'] = 0;
            $data['name'] = $data['name'];
            $data['cid'] = $tocid;
            //$data['status'] = 0;
            unset($data['id']);
            unset($data['wxvideo_product_id']);
            unset($data['wxvideo_edit_status']);
            unset($data['wxvideo_status']);
            unset($data['wxvideo_reject_reason']);
            unset($data['gid']);
            $newproid = Db::name('shop_product')->insertGetId($data);
            foreach ($gglist as $gg) {
                $ggdata = $gg;
                $ggdata['proid'] = $newproid;
                $ggdata['aid'] = $toaid;
                unset($ggdata['id']);
                unset($ggdata['linkid']);
                Db::name('shop_guige')->insert($ggdata);
            }
        }
        //$msg = '商城商品复制到账号'.input('post.userid/d').'/'.implode(',',$ids);
        //\app\common\System::plog($msg);
        return json(['status' => 1, 'msg' => '复制成功']);
    }

    //同步商品到商户

    public function importexcel()
    {
        set_time_limit(0);
        ini_set('memory_limit', -1);
        $file = input('post.file');
        if (!$file) return json(['status' => 0, 'msg' => '请上传excel文件']);
        $exceldata = $this->import_excel($file);
        $cateArr = Db::name('shop_category')->where('aid', aid)->column('name', 'id');
        $cateArr = array_flip($cateArr);
        $groupArr = Db::name('shop_group')->where('aid', aid)->column('name', 'id');
        $groupArr = array_flip($groupArr);
        $fuwuArr = Db::name('shop_fuwu')->where('aid', aid)->column('name', 'id');
        $fuwuArr = array_flip($fuwuArr);

        $insertnum = 0;
        $updatenum = 0;
        foreach ($exceldata as $data) {
            $indata = [];
            $indata['aid'] = aid;
            $indata['bid'] = bid;
            $indata['name'] = $data[0];

            $pic = $data[1];
            if (strpos($pic, '//') === 0) $pic = 'https:' . $pic;
            if ($pic) {
                $pic = \app\common\Pic::uploadoss($pic);
            }
            $indata['pic'] = $pic;

            $pics = [];
            if ($data[2]) {
                foreach (explode(',', $data[2]) as $v) {
                    $pic = $v;
                    if (strpos($pic, '//') === 0) $pic = 'https:' . $pic;
                    if ($pic) {
                        $pic = \app\common\Pic::uploadoss($pic);
                    }
                    $pics[] = $pic;
                }
            }

            $indata['pics'] = implode(',', $pics);
            $indata['sellpoint'] = $data[3];
            if ($data[4]) {
                $cids = [];
                foreach (explode(',', $data[4]) as $v) {
                    if (!$cateArr[$v]) {
                        if (bid == 0) {
                            $cids[] = Db::name('shop_category')->insertGetId(['aid' => aid, 'name' => $v, 'sort' => 0]);
                        }
                    } else {
                        $cids[] = $cateArr[$v];
                    }
                }
                $indata['cid'] = implode(',', $cids);
            }
            if ($data[5]) {
                $gids = [];
                foreach (explode(',', $data[5]) as $v) {
                    if (!$groupArr[$v]) {
                        if (bid == 0) {
                            $gids[] = Db::name('shop_group')->insertGetId(['aid' => aid, 'name' => $v, 'sort' => 0]);
                        }
                    } else {
                        $gids[] = $groupArr[$v];
                    }
                }
                $indata['gid'] = implode(',', $gids);
            }
            if ($data[6]) {
                $fwids = [];
                foreach (explode(',', $data[6]) as $v) {
                    if (!$fuwuArr[$v]) {
                        $fwid = Db::name('shop_fuwu')->insertGetId(['aid' => aid, 'bid' => bid, 'name' => $v, 'sort' => 0]);
                    } else {
                        $fwid = $fuwuArr[$v];
                    }
                    $fwids[] = $fwid;
                }
                $indata['fwid'] = implode(',', $fwids);
            }
            $indata['procode'] = $data[7];
            $indata['guigedata'] = '[{"k":0,"title":"规格","items":[{"k":0,"title":"默认规格"}]}]';
            $indata['market_price'] = round($data[8], 2);
            $indata['cost_price'] = round($data[9], 2);
            $indata['sell_price'] = round($data[10], 2);
            $indata['weight'] = intval($data[11]);
            $indata['stock'] = intval($data[12]);
            $indata['sales'] = intval($data[13]);
            if ($data[14] == '已上架' || $data[14] == '上架') {
                $indata['status'] = 1;
            } else {
                $indata['status'] = 0;
            }
            $indata['detail'] = jsonEncode([[
                'id' => 'M0000000000000',
                'temp' => 'richtext',
                'params' => ['bgcolor' => '#FFFFFF', 'margin_x' => 0, 'margin_y' => 0, 'padding_x' => 0, 'padding_y' => 0, 'quanxian' => ['all' => true], 'platform' => ['all' => true]],
                'data' => '',
                'other' => '',
                'content' => $data[15]
            ]]);

            $indata['createtime'] = time();
            if (bid != 0) $indata['commissionset'] = -1;
            $proid = Db::name('shop_product')->insertGetId($indata);
            $ggdata = [];
            $ggdata['aid'] = aid;
            $ggdata['proid'] = $proid;
            $ggdata['name'] = $indata['name'];
            $ggdata['pic'] = $indata['pic'];
            $ggdata['cost_price'] = $indata['cost_price'];
            $ggdata['market_price'] = $indata['market_price'];
            $ggdata['sell_price'] = $indata['sell_price'];
            $ggdata['stock'] = $indata['stock'];
            $ggdata['weight'] = $indata['weight'];
            $ggdata['ks'] = 0;
            Db::name('shop_guige')->insert($ggdata);

            $insertnum++;
        }
        \app\common\System::plog('导入商品');
        return json(['status' => 1, 'msg' => '成功导入' . $insertnum . '条数据']);
    }

    //选择商品

    public function chooseproduct()
    {
        //分类
        $clist = Db::name('shop_category')->Field('id,name')->where('aid', aid)->where('pid', 0)->order('sort desc,id')->select()->toArray();
        foreach ($clist as $k => $v) {
            $clist[$k]['child'] = Db::name('shop_category')->Field('id,name')->where('aid', aid)->where('pid', $v['id'])->order('sort desc,id')->select()->toArray();
        }
        //分组
        $glist = Db::name('shop_group')->where('aid', aid)->order('sort desc,id')->select()->toArray();
        //商户
        $blist = Db::name('business')->where('aid', aid)->order('sort desc,id desc')->select()->toArray();
        View::assign('blist', $blist);
        View::assign('clist', $clist);
        View::assign('glist', $glist);
        return View::fetch();
    }

    //获取商品信息
    public function getproduct()
    {
        $proid = input('post.proid/d');
        $product = Db::name('shop_product')->where('aid', aid)->where('id', $proid)->find();
        // 货币单位             
        switch ($product['currency']) {
            case '1':
                $product['sell_price'] = '¥ '.$product['sell_price'];
                $product['market_price'] = '¥ '.$product['market_price'];
                break;
            case '2':
                $product['sell_price'] = 'NT$ '.$product['sell_price'];
                $product['market_price'] = 'NT$ '.$product['market_price'];
                break;
            case '3':
                $product['sell_price'] = '$ '.$product['sell_price'];
                $product['market_price'] = '$ '.$product['market_price'];
                break;
            case '4':
                $product['sell_price'] = '₫ '.$product['sell_price'];
                $product['market_price'] = '₫ '.$product['market_price'];
                break;
            case '5':
                $product['sell_price'] = '฿ '.$product['sell_price'];
                $product['market_price'] = '฿ '.$product['market_price'];
                break;
            case '6':
                $product['sell_price'] = '₹ '.$product['sell_price'];
                $product['market_price'] = '₹ '.$product['market_price'];
                break;
            case '7':
                $product['sell_price'] = 'RM '.$product['sell_price'];
                $product['market_price'] = 'RM '.$product['market_price'];
                break;
            default:
                $product['sell_price'] = '¥ '.$product['sell_price'];
                $product['market_price'] = '¥ '.$product['market_price'];
                break;
        }
        //多规格
        $newgglist = array();
        $gglist = Db::name('shop_guige')->where('aid', aid)->where('proid', $product['id'])->select()->toArray();
        foreach ($gglist as $k => $v) {
            $newgglist[$v['ks']] = $v;
        }
        $guigedata = json_decode($product['guigedata']);
        return json(['product' => $product, 'gglist' => $newgglist, 'guigedata' => $guigedata]);
    }

    //上传商品到视频号
    public function towxvideo()
    {
        $proids = input('param.ids/a');
        $prolist = Db::name('shop_product')->where('aid', aid)->where('bid', 0)->where('id', 'in', $proids)->where('wxvideo_status', 0)->select()->toArray();
        if (!$prolist) return json(['status' => 0, 'msg' => '不存在未上传的商品']);

        $third_cat_id = input('param.third_cat_id');
        $brand_id = input('param.brand_id');
        $qualification_pics = input('param.qualification_pics');
        $update = [];
        $update['wxvideo_third_cat_id'] = $third_cat_id;
        $update['wxvideo_brand_id'] = $brand_id;
        $update['wxvideo_product_id'] = $product_id;
        if ($qualification_pics) {
            $update['wxvideo_qualification_pics'] = $qualification_pics;
        }
        Db::name('shop_product')->where('bid', 0)->where('id', 'in', $proids)->where('wxvideo_status', 0)->update($update);

        $errmsg = '';
        $successnum = 0;
        $errnum = 0;
        foreach ($prolist as $product) {
            $rs = \app\common\Wxvideo::updateproduct($product['id']);
            if ($rs['status'] == 1) {
                $successnum++;
            } else {
                $errnum++;
                $errmsg = $rs['msg'];
            }
        }
        if ($errnum == 0) {
            return json(['status' => 1, 'msg' => '成功同步商品' . $successnum . '个']);
        } else {
            return json(['status' => 0, 'msg' => '成功' . $successnum . '个,失败' . $errnum . '个,原因:' . $errmsg]);
        }

    }

    //更新视频号商品状态
    public function wxvideoupdatest()
    {
        $url = 'https://api.weixin.qq.com/shop/spu/get_list?access_token=' . Wechat::access_token(aid, 'wx');
        $prolist = [];
        $postdata = [];
        $postdata['page'] = 1;
        $postdata['page_size'] = 100;
        $rs = curl_post($url, jsonEncode($postdata));
        $rs = json_decode($rs, true);
        if ($rs['errcode'] != 0) {
            return json(['status' => 0, 'msg' => Wechat::geterror($rs), '$postdata' => $postdata]);
        }
        $totalnum = $rs['total_num'];
        $prolist = array_merge($prolist, $rs['spus']);
        if ($totalnum > 100) {
            $pagecount = ceil($totalnum / 100);
            for ($i = 1; $i < $pagecount; $i++) {
                $postdata = [];
                $postdata['page'] = $i + 1;
                $postdata['page_size'] = 100;
                $rs = curl_post($url, jsonEncode($postdata));
                $rs = json_decode($rs, true);
                if ($rs['spus']) {
                    $prolist = array_merge($prolist, $rs['spus']);
                }
            }
        }
        foreach ($prolist as $pro) {
            $product = Db::name('shop_product')->where('wxvideo_product_id', $pro['product_id'])->find();
            if (!$product) {
                curl_post('https://api.weixin.qq.com/shop/spu/del?access_token=' . Wechat::access_token(aid, 'wx'), jsonEncode(['product_id' => $pro['product_id']]));
                continue;
            }
            $wxvideo_reject_reason = $pro['audit_info']['reject_reason'];
            if ($pro['edit_status'] != $v['wxvideo_edit_status'] || $pro['status'] != $v['wxvideo_status']) {
                Db::name('shop_product')->where('id', $product['id'])->update(['wxvideo_edit_status' => $pro['edit_status'], 'wxvideo_status' => $pro['status'], 'wxvideo_reject_reason' => $wxvideo_reject_reason]);
            }
        }
        return json(['status' => 1, 'msg' => '同步更新完成']);
    }

    //撤回审核
    public function wxvideo_del_audit()
    {
        $proid = input('param.proid/d');
        $product = Db::name('shop_product')->where('aid', aid)->where('id', $proid)->find();
        $rs = curl_post('https://api.weixin.qq.com/shop/spu/del_audit?access_token=' . Wechat::access_token(aid, 'wx'), jsonEncode(['product_id' => $product['wxvideo_product_id']]));
        $rs = json_decode($rs, true);
        if ($rs['errcode'] != 0) {
            return json(['status' => 0, 'msg' => Wechat::geterror($rs)]);
        } else {
            Db::name('shop_product')->where('aid', aid)->where('id', $proid)->update(['wxvideo_edit_status' => 1]);
            return json(['status' => 1, 'msg' => '操作成功']);
        }
    }

    //视频号上架
    public function wxvideo_listing()
    {
        $proid = input('param.proid/d');
        $rs = \app\common\Wxvideo::listing($proid);
        return json($rs);
    }

    //视频号下架
    public function wxvideo_delisting()
    {
        $proid = input('param.proid/d');
        $rs = \app\common\Wxvideo::delisting($proid);
        return json($rs);
    }

    //规格拆分
    public function getsplitdata()
    {
        $proid = input('post.proid');
        $splitlist = Db::name('shop_ggsplit')->where('aid', aid)->where('proid', $proid)->select()->toArray();
        $gglist = Db::name('shop_guige')->where('aid', aid)->where('proid', $proid)->select()->toArray();
        if (!$splitlist) $splitlist = [['ggid1' => '', 'ggid2' => '', 'multiple' => '']];
        return json(['splitlist' => $splitlist, 'gglist' => $gglist]);
    }

    public function ggsplit()
    {
        //var_dump(input('post.'));
        $proid = input('post.proid/d');
        if (!$proid) return json(['status' => 0, 'msg' => '获取商品信息失败']);
        $ggid1Arr = input('post.ggid1/a');
        $ggid2Arr = input('post.ggid2/a');
        $multipleArr = input('post.multiple/a');
        $datalist = [];
        foreach ($ggid1Arr as $k => $v) {
            if ($ggid1Arr[$k] == '' || $ggid2Arr[$k] == '') continue;
            $data = [];
            $data['aid'] = aid;
            $data['proid'] = $proid;
            $data['ggid1'] = $ggid1Arr[$k];
            $data['ggid2'] = $ggid2Arr[$k];
            $data['multiple'] = $multipleArr[$k];
            $data['createtime'] = time();
            $datalist[$data['ggid1'] . '-' . $data['ggid2']] = $data;
            if (!$multipleArr[$k] || $multipleArr[$k] <= 0) return json(['status' => 0, 'msg' => '倍数必须是大于0的整数']);
            if ($data['ggid1'] == $data['ggid2']) return json(['status' => 0, 'msg' => '不能设置两个相同的规格进行拆分']);
        }
        Db::name('shop_ggsplit')->where('aid', aid)->where('proid', $proid)->delete();
        foreach ($datalist as $k => $data) {
            Db::name('shop_ggsplit')->insert($data);
        }
        \app\model\ShopProduct::calculateStock($proid);
        return json(['status' => 1, 'msg' => '设置成功', 'url' => true]);
    }

}
