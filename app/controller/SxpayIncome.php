<?php


// +----------------------------------------------------------------------
// | 随行付申请入驻
// +----------------------------------------------------------------------
namespace app\controller;

use think\facade\Db;
use think\facade\View;

class SxpayIncome extends Common
{
    public function index()
    {

        if (request()->isAjax()) {
            $page = input('param.page');
            $limit = input('param.limit');
            if (input('param.field') && input('param.order')) {
                $order = input('param.field') . ' ' . input('param.order');
            } else {
                $order = 'id desc';
            }
            $where = [];
            $where[] = ['aid', '=', aid];
            $where[] = ['delete', '=', 0];
            $count = 0 + db('sxpay_income')->where($where)->count();
            $data = db('sxpay_income')->where($where)->page($page, $limit)->order($order)->select();

            return ['code' => 0, 'msg' => '查询成功', 'count' => $count, 'data' => $data];
        }
        return View::fetch();
    }

    public function apply()
    {
        $id = input('param.id/d');
        if ($id) {
            $info = Db::name('sxpay_income')->where(['aid' => aid, 'id' => $id])->find();
        } else {
            $info = [];
        }
        if (request()->isPost()) {
            $data = input('post.info/a');
            if (!$data['identity_id_card_valid_time_cq']) {
                $data['identity_id_card_valid_time_cq'] = 0;
            } else {
                $data['identity_id_card_valid_time_cq'] = 1;
            }
            $data['aid'] = aid;
            $data['createtime'] = time();
            if ($info) {
                Db::name('sxpay_income')->where(['id' => $info['id']])->update($data);
                $id = $info['id'];
            } else {
                $id = Db::name('sxpay_income')->insertGetId($data);
            }

            $reqData = [];
            if ($info && $info['business_code']) {
                $reqData['mno'] = $info['business_code'];
            }
            $reqData['mecDisNm'] = $data['merchant_shortname'];
            $reqData['mblNo'] = $data['contact_mobile'];
            $reqData['operationalType'] = '01';
            if ($data['subject_type'] == 'SUBJECT_TYPE_MICRO') {
                $reqData['haveLicenseNo'] = '01';
            } elseif ($data['subject_type'] == 'SUBJECT_TYPE_INDIVIDUAL') {
                $reqData['haveLicenseNo'] = '02';
            } elseif ($data['subject_type'] == 'SUBJECT_TYPE_ENTERPRISE') {
                $reqData['haveLicenseNo'] = '03';
            }
            $reqData['mecTypeFlag'] = '00';
            $reqData['cprRegAddr'] = $data['store_street'];
            $reqData['regProvCd'] = $this->getareacode($data['store_province']);
            $reqData['regCityCd'] = $this->getareacode($data['store_province'], $data['store_city']);
            $reqData['regDistCd'] = $this->getareacode($data['store_province'], $data['store_city'], $data['store_area']);
            $reqData['mccCd'] = $data['mccCd'];
            $reqData['csTelNo'] = $data['service_phone'];
            $reqData['email'] = $data['contact_email'];
            $reqData['settleType'] = '04';

            $reqData['callbackUrl'] = PRE_URL . '/notify.php';
            $reqData['checkCallbackUrl'] = PRE_URL . '/notify.php';

            if ($data['subject_type'] != 'SUBJECT_TYPE_MICRO') {
                $reqData['cprRegNmCn'] = $data['business_merchant_name'];
                $reqData['registCode'] = $data['business_license_number'];
            }
            if ($data['subject_type'] == 'SUBJECT_TYPE_ENTERPRISE') {
                $reqData['licenseMatch'] = '00';
            }

            $reqData['identityName'] = $data['identity_id_card_name'];
            $reqData['identityTyp'] = '00';
            $reqData['identityNo'] = $data['identity_id_card_number'];
            $reqData['legalPersonLicStt'] = str_replace('-', '', $data['identity_id_card_valid_time1']);
            if ($data['identity_id_card_valid_time_cq'] == 1) {
                $reqData['legalPersonLicEnt'] = '********';
            } else {
                $reqData['legalPersonLicEnt'] = str_replace('-', '', $data['identity_id_card_valid_time2']);
            }
            $reqData['actNm'] = $data['jiesuan_account_name'];
            $reqData['actTyp'] = ($data['jiesuan_bank_account_type'] == 'BANK_ACCOUNT_TYPE_CORPORATE' ? '00' : '01');
            if ($reqData['actTyp'] == '01') {
                $reqData['stmManIdNo'] = $reqData['identityNo'];
                $reqData['accountLicStt'] = $reqData['legalPersonLicStt'];
                $reqData['accountLicEnt'] = $reqData['legalPersonLicEnt'];
            }
            $reqData['actNo'] = $data['jiesuan_account_number'];
            $reqData['lbnkNo'] = explode('-', $data['jiesuan_bank_name'])[0];
            $reqData['lbnkNm'] = explode('-', $data['jiesuan_bank_name'])[1];

            if ($data['subject_type'] != 'SUBJECT_TYPE_MICRO') {
                $reqData['licensePic'] = \app\custom\Sxpay::uploadimg($data['business_license_copy'], '13');
            }
            $reqData['legalPersonidPositivePic'] = \app\custom\Sxpay::uploadimg($data['identity_id_card_copy'], '02');
            $reqData['legalPersonidOppositePic'] = \app\custom\Sxpay::uploadimg($data['identity_id_card_national'], '03');
            if ($reqData['actTyp'] == '00') {
                $reqData['openingAccountLicensePic'] = \app\custom\Sxpay::uploadimg($data['account_license_pic'], '04');
            } else {
                $reqData['bankCardPositivePic'] = \app\custom\Sxpay::uploadimg($data['bank_card_pic'], '05');
            }
            $reqData['storePic'] = \app\custom\Sxpay::uploadimg($data['store_entrance_pic'], '10');
            $reqData['insideScenePic'] = \app\custom\Sxpay::uploadimg($data['indoor_pic'], '11');

            if ($data['store_other_pics']) {
                $store_other_pics = explode(',', $data['store_other_pics']);
                $reqData['otherMaterialPictureFour'] = \app\custom\Sxpay::uploadimg($store_other_pics[0], '19');
                if ($store_other_pics[1]) {
                    $reqData['otherMaterialPictureFive'] = \app\custom\Sxpay::uploadimg($store_other_pics[1], '20');
                }
                if ($store_other_pics[2]) {
                    $reqData['otherMaterialPictureThree'] = \app\custom\Sxpay::uploadimg($store_other_pics[2], '18');
                }
            }
            //\think\facade\Log::write($reqData);
            if ($info && $info['business_code']) {
                $rs = \app\custom\Sxpay::modify($reqData, $info['mchkey']);
            } else {
                $rs = \app\custom\Sxpay::income($reqData);
            }
            if ($rs['status'] == 1) {
                if ($info && $info['business_code']) {
                    $data['taskStatus_edit'] = 0;
                    $data['suggestion_edit'] = '';
                    if ($rs['data']['applicationId']) {
                        $data['applicationId_edit'] = $rs['data']['applicationId'];
                    }
                    if ($rs['data']['mchkey']) {
                        $data['mchkey'] = $rs['data']['mchkey'];
                    }
                } else {
                    $data['taskStatus'] = 0;
                    $data['suggestion'] = '';
                    if ($rs['data']['mno']) {
                        $data['business_code'] = $rs['data']['mno'];
                    }
                    if ($rs['data']['applicationId']) {
                        $data['applicationId'] = $rs['data']['applicationId'];
                    }
                    if ($rs['data']['mchkey']) {
                        $data['mchkey'] = $rs['data']['mchkey'];
                    }
                }
            } else {

            }
            Db::name('sxpay_income')->where(['id' => $id])->update($data);
            return json($rs);
        }

        $mccCdArr = [
            '5411' => '生活百货/百货商城/超市（非平台类）',
            '5331' => '生活百货/百货商城/杂货店',
            '5300' => '生活百货/百货商城/会员制批量零售店',
            '5309' => '生活百货/百货商城/国外代购及免税店',
            '5311' => '生活百货/百货商城/平台类综合商城',
            '5715' => '生活百货/百货商城/酒精饮料批发商（国际专用）',
            '5914' => '生活百货/百货商城/成人用品/避孕用品/情趣内衣',
            '5998' => '生活百货/百货商城/帐篷和遮阳篷商店',
            '5999' => '生活百货/百货商城/其他专业零售店',
            '5983' => '生活百货/百货商城/油品燃料经销',
            '5984' => '生活百货/百货商城/烟花爆竹',
            '5399' => '生活百货/百货商城/其他综合零售',
            '5611' => '生活百货/服饰鞋包/男性服饰',
            '5621' => '生活百货/服饰鞋包/女性成衣',
            '5631' => '生活百货/服饰鞋包/配饰商店',
            '5651' => '生活百货/服饰鞋包/内衣/家居服',
            '5661' => '生活百货/服饰鞋包/鞋类',
            '5681' => '生活百货/服饰鞋包/皮草皮具',
            '5691' => '生活百货/服饰鞋包/高档时装及奢侈品',
            '5697' => '生活百货/服饰鞋包/裁缝、修补、改衣制衣',
            '5698' => '生活百货/服饰鞋包/假发等饰品',
            '5699' => '生活百货/服饰鞋包/各类服装及饰物',
            '5137' => '生活百货/服饰鞋包/制服与商务正装定制',
            '5139' => '生活百货/服饰鞋包/鞋类销售平台（批发商）',
            '5948' => '生活百货/服饰鞋包/行李箱包',
            '5945' => '生活百货/母婴玩具/玩具、游戏用品',
            '5641' => '生活百货/母婴玩具/母婴用品',
            '5997' => '生活百货/美妆珠宝配饰/男士用品：剃须刀、烟酒具、瑞士军刀',
            '5977' => '生活百货/美妆珠宝配饰/化妆品',
            '5944' => '生活百货/美妆珠宝配饰/钟表店',
            '5094' => '生活百货/美妆珠宝配饰/珠宝和金银饰品',
            '5946' => '生活百货/数码家电/专业摄影器材',
            '5722' => '生活百货/数码家电/家用电器',
            '5732' => '生活百货/数码家电/数码产品及配件',
            '5045' => '生活百货/数码家电/商用计算机及服务器',
            '4812' => '生活百货/数码家电/手机、通讯设备销售',
            '5940' => '生活百货/运动户外/自行车及配件',
            '5941' => '生活百货/运动户外/体育用品/器材',
            '5655' => '生活百货/运动户外/运动服饰',
            '5942' => '生活百货/图书音像/书籍',
            '5994' => '生活百货/图书音像/报纸、杂志',
            '5735' => '生活百货/图书音像/音像制品',
            '5192' => '生活百货/图书音像/书、期刊和报纸（批发商）',
            '5996' => '生活百货/家居家纺建材/游泳、SPA、洗浴设备',
            '5949' => '生活百货/家居家纺建材/家用纺织品',
            '5712' => '生活百货/家居家纺建材/家具/家庭摆设',
            '5713' => '生活百货/家居家纺建材/地板和地毯',
            '5714' => '生活百货/家居家纺建材/窗帘、帷幕、室内装潢',
            '5718' => '生活百货/家居家纺建材/壁炉、屏风',
            '5719' => '生活百货/家居家纺建材/各种家庭装饰专营',
            '5200' => '生活百货/家居家纺建材/大型仓储式家庭用品卖场',
            '5211' => '生活百货/家居家纺建材/木材与建材商店',
            '5231' => '生活百货/家居家纺建材/玻璃、油漆涂料、墙纸',
            '5251' => '生活百货/家居家纺建材/家用五金工具',
            '5261' => '生活百货/家居家纺建材/草坪和花园用品',
            '5193' => '生活百货/家居家纺建材/花木栽种用品、苗木和花卉（批发商）',
            '5198' => '生活百货/家居家纺建材/油漆、清漆用品（批发商）',
            '5131' => '生活百货/家居家纺建材/布料、缝纫用品和其他纺织品（批发商）',
            '5039' => '生活百货/家居家纺建材/未列入其他代码的建材（批发商）',
            '3002' => '生活百货/家居家纺建材/大型企业批发',
            '5993' => '生活百货/饮食保健/烟草/雪茄',
            '5921' => '生活百货/饮食保健/酒类',
            '5811' => '生活百货/饮食保健/宴会提供商',
            '5812' => '生活百货/饮食保健/餐厅、订餐服务',
            '5813' => '生活百货/饮食保健/酒吧、舞厅、夜总会',
            '5814' => '生活百货/饮食保健/快餐店',
            '5815' => '生活百货/饮食保健/咖啡厅、茶馆',
            '5880' => '生活百货/饮食保健/校园团餐',
            '5881' => '生活百货/饮食保健/综合团餐',
            '5422' => '生活百货/饮食保健/肉、禽、蛋及水产品',
            '5423' => '生活百货/饮食保健/水果店',
            '5441' => '生活百货/饮食保健/糖果及坚果商店',
            '5451' => '生活百货/饮食保健/乳制品/冷饮',
            '5462' => '生活百货/饮食保健/面包糕点',
            '5466' => '生活百货/饮食保健/茶叶',
            '5467' => '生活百货/饮食保健/保健品',
            '5499' => '生活百货/饮食保健/其他食品零售',
            '5992' => '生活百货/文化玩乐宠物/花店',
            '5995' => '生活百货/文化玩乐宠物/宠物及宠物用品',
            '5970' => '生活百货/文化玩乐宠物/工艺美术用品',
            '5971' => '生活百货/文化玩乐宠物/艺术品和画廊',
            '5972' => '生活百货/文化玩乐宠物/邮票/纪念币',
            '5973' => '生活百货/文化玩乐宠物/宗教物品',
            '5947' => '生活百货/文化玩乐宠物/礼品、卡片、纪念品',
            '5950' => '生活百货/文化玩乐宠物/瓷器、玻璃和水晶摆件',
            '5937' => '生活百货/文化玩乐宠物/古玩复制品（赝品）',
            '5931' => '生活百货/文化玩乐宠物/旧商品店、二手商品店',
            '5932' => '生活百货/文化玩乐宠物/文物古董',
            '5733' => '生活百货/文化玩乐宠物/乐器',
            '7993' => '生活百货/文化玩乐宠物/家用电子游戏',
            '1520' => '商业及生活服务/房地产/房地产开发商',
            '1711' => '商业及生活服务/承包商（农业、建筑、出版）/空调类承包商',
            '1731' => '商业及生活服务/承包商（农业、建筑、出版）/电器承包商',
            '1740' => '商业及生活服务/承包商（农业、建筑、出版）/建筑材料承包商',
            '1750' => '商业及生活服务/承包商（农业、建筑、出版）/木工承包商',
            '1761' => '商业及生活服务/承包商（农业、建筑、出版）/金属产品承包商',
            '1771' => '商业及生活服务/承包商（农业、建筑、出版）/混凝土承包商',
            '1799' => '商业及生活服务/承包商（农业、建筑、出版）/其他工程承包商',
            '2741' => '商业及生活服务/商业服务/出版印刷服务',
            '2791' => '商业及生活服务/商业服务/刻版排版服务',
            '2842' => '商业及生活服务/商业服务/清洁抛光服务',
            '5935' => '商业及生活服务/商业服务/海上船只遇难救助',
            '5933' => '商业及生活服务/金融服务/典当行',
            '4829' => '商业及生活服务/金融服务/电汇和汇票服务',
            '4904' => '商业及生活服务/公共事业/公共事业-清洁服务缴费',
            '4906' => '商业及生活服务/公共事业/充电桩',
            '4909' => '商业及生活服务/公共事业/有线电视缴费',
            '5310' => '商业及生活服务/团购/团购及折扣店',
            '5962' => '商业及生活服务/直销/旅游相关服务直销',
            '5963' => '商业及生活服务/直销/上门直销（直销员）',
            '5964' => '商业及生活服务/直销/目录直销平台',
            '5965' => '商业及生活服务/直销/直销代理',
            '5966' => '商业及生活服务/直销/电话外呼直销',
            '5967' => '商业及生活服务/直销/电话接入直销',
            '5968' => '商业及生活服务/直销/订阅订购服务',
            '5969' => '商业及生活服务/直销/直销',
            '3003' => '航旅交通/公共交通/铁路局（直属）',
            '4011' => '航旅交通/公共交通/铁路货运',
            '4111' => '航旅交通/公共交通/公共交通',
            '4112' => '航旅交通/公共交通/铁路客运',
            '4113' => '航旅交通/公共交通/ETC不停车自动缴费',
            '4114' => '航旅交通/公共交通/MTC半自动车道收费',
            '4115' => '航旅交通/公共交通/地铁',
            '4119' => '航旅交通/公共交通/急救服务',
            '4121' => '航旅交通/公共交通/出租车服务（TAXI）',
            '4131' => '航旅交通/公共交通/长途公路客运',
            '4411' => '航旅交通/公共交通/游轮及巡游航线服务',
            '4457' => '航旅交通/公共交通/出租船只',
            '4468' => '航旅交通/公共交通/船舶、海运服务提供商',
            '4214' => '航旅交通/物流仓储/物流货运服务',
            '4215' => '航旅交通/物流仓储/快递服务',
            '4225' => '航旅交通/物流仓储/公共仓储、集装整理',
            '4511' => '航旅交通/航空票务/航空公司',
            '4512' => '航旅交通/航空票务/机票代理人',
            '4582' => '航旅交通/航空票务/机场服务',
            '4722' => '航旅交通/旅行住宿/旅行社和旅游服务',
            '4723' => '航旅交通/旅行住宿/国际货运代理和报关行',
            '4733' => '航旅交通/旅行住宿/大型旅游景点',
            '4789' => '航旅交通/旅行住宿/未列入其他代码的运输服务',
            '7011' => '航旅交通/旅行住宿/住宿服务（旅馆、酒店、汽车旅馆、度假村等）',
            '7012' => '航旅交通/旅行住宿/度假用别墅服务',
            '7032' => '航旅交通/旅行住宿/运动和娱乐露营',
            '7033' => '航旅交通/旅行住宿/活动房车场及野营场所 ',
            '5943' => '专业销售/办公用品/文具及办公用品',
            '5978' => '专业销售/办公用品/打字设备、打印复印机、扫描仪',
            '5021' => '专业销售/办公用品/办公及商务家具（批发商）',
            '5044' => '专业销售/办公用品/办公、影印及微缩摄影器材（批发商）',
            '5046' => '专业销售/办公用品/未列入其他代码的商用器材',
            '5111' => '专业销售/办公用品/文具、办公用品、复印纸和书写纸（批发商）',
            '5051' => '专业销售/工业产品/金属产品和服务（批发商）',
            '5065' => '专业销售/工业产品/电气产品和设备',
            '5072' => '专业销售/工业产品/五金器材及用品（批发商）',
            '5074' => '专业销售/工业产品/管道及供暖设备',
            '5085' => '专业销售/工业产品/工业设备和制成品',
            '5099' => '专业销售/工业产品/其他工业耐用品',
            '5169' => '专业销售/工业产品/化工产品',
            '5172' => '专业销售/工业产品/石油及石油产品（批发商）',
            '5199' => '专业销售/工业产品/其他工业原料和消耗品',
            '5013' => '专业销售/汽车和运输工具/机动车供应及零配件（批发商）',
            '5271' => '专业销售/汽车和运输工具/活动房车销售商',
            '5511' => '专业销售/汽车和运输工具/汽车销售',
            '5521' => '专业销售/汽车和运输工具/二手车销售',
            '5531' => '专业销售/汽车和运输工具/汽车商店、家庭用品商店（国际专用）',
            '5532' => '专业销售/汽车和运输工具/汽车轮胎经销',
            '5533' => '专业销售/汽车和运输工具/汽车零配件',
            '5541' => '专业销售/汽车和运输工具/加油站、服务站',
            '5542' => '专业销售/汽车和运输工具/加油卡、加油服务',
            '5551' => '专业销售/汽车和运输工具/船舶及配件销售',
            '5561' => '专业销售/汽车和运输工具/拖车、篷车及娱乐用车',
            '5564' => '专业销售/汽车和运输工具/轨道交通设备器材',
            '5565' => '专业销售/汽车和运输工具/飞机及配件、航道设施',
            '5566' => '专业销售/汽车和运输工具/运输搬运设备、起重装卸设备',
            '5571' => '专业销售/汽车和运输工具/摩托车及配件',
            '5572' => '专业销售/汽车和运输工具/电动车及配件',
            '5592' => '专业销售/汽车和运输工具/露营及旅行汽车',
            '5598' => '专业销售/汽车和运输工具/雪车',
            '5599' => '专业销售/汽车和运输工具/机动车综合经营',
            '5047' => '专业销售/药品医疗/医疗器械',
            '5122' => '专业销售/药品医疗/药品、药品经营者（批发商）',
            '5912' => '专业销售/药品医疗/药物',
            '5975' => '专业销售/药品医疗/助听器',
            '5976' => '专业销售/药品医疗/康复和身体辅助用品',
            '7210' => '商业及生活服务/生活服务/洗衣服务',
            '7211' => '商业及生活服务/生活服务/洗熨服务（自助洗衣服务）',
            '7216' => '商业及生活服务/生活服务/干洗店',
            '7217' => '商业及生活服务/生活服务/室内清洁服务',
            '7221' => '商业及生活服务/生活服务/摄影服务',
            '7230' => '商业及生活服务/生活服务/美容/美发服务',
            '7231' => '商业及生活服务/生活服务/美甲',
            '7251' => '商业及生活服务/生活服务/鞋帽清洗',
            '7261' => '商业及生活服务/生活服务/丧仪殡葬服务',
            '7273' => '商业及生活服务/生活服务/婚介服务',
            '7276' => '商业及生活服务/生活服务/财务债务咨询',
            '7277' => '商业及生活服务/生活服务/婚庆服务',
            '7278' => '商业及生活服务/生活服务/导购、经纪和拍卖服务',
            '7295' => '商业及生活服务/生活服务/家政服务',
            '7296' => '商业及生活服务/生活服务/服装出租',
            '7297' => '商业及生活服务/生活服务/按摩服务',
            '7298' => '商业及生活服务/生活服务/美容SPA和美体保健',
            '7299' => '商业及生活服务/生活服务/其他生活服务',
            '7511' => '商业及生活服务/生活服务/货品停放交易(国际专用)',
            '6010' => '商业及生活服务/金融服务/金融机构-商业银行服务',
            '6011' => '商业及生活服务/金融服务/金融机构-自动现金服务',
            '6012' => '商业及生活服务/金融服务/金融机构-其他服务',
            '6050' => '商业及生活服务/金融服务/贵金属投资',
            '6051' => '商业及生活服务/金融服务/外币汇兑',
            '6060' => '商业及生活服务/金融服务/小贷公司',
            '6061' => '商业及生活服务/金融服务/消费金融公司',
            '6062' => '商业及生活服务/金融服务/汽车金融公司',
            '6063' => '商业及生活服务/金融服务/融资租赁公司',
            '6064' => '商业及生活服务/金融服务/金融租赁公司',
            '6065' => '商业及生活服务/金融服务/信托公司',
            '6066' => '商业及生活服务/金融服务/支付机构',
            '6067' => '商业及生活服务/金融服务/融资担保公司',
            '6069' => '商业及生活服务/金融服务/P2P',
            '6211' => '商业及生活服务/金融服务/证券期货基金',
            '6760' => '商业及生活服务/金融服务/个人资金借贷',
            '6071' => '商业及生活服务/无人值守服务/自助贩卖机',
            '6072' => '商业及生活服务/无人值守服务/自助零售',
            '6073' => '商业及生活服务/无人值守服务/自助借还',
            '6074' => '商业及生活服务/无人值守服务/自助娱乐服务',
            '6075' => '商业及生活服务/无人值守服务/其他自助生活服务',
            '6513' => '商业及生活服务/房地产/不动产管理－物业管理',
            '7013' => '商业及生活服务/房地产/不动产代理——房地产经纪',
            '7311' => '商业及生活服务/商业服务/广告服务',
            '7321' => '商业及生活服务/商业服务/征信和信用报告咨询服务',
            '7322' => '商业及生活服务/商业服务/债务催收机构',
            '7333' => '商业及生活服务/商业服务/商业摄影、设计、绘图服务',
            '7338' => '商业及生活服务/商业服务/复印及绘图服务',
            '7339' => '商业及生活服务/商业服务/文字处理/翻译速记',
            '7340' => '商业及生活服务/商业服务/商户拓展',
            '7342' => '商业及生活服务/商业服务/灭虫及消毒服务',
            '7349' => '商业及生活服务/商业服务/清洁、保养及门卫服务',
            '7361' => '商业及生活服务/商业服务/猎头、职业中介',
            '7392' => '商业及生活服务/商业服务/公关和企业管理服务',
            '7393' => '商业及生活服务/商业服务/保安和监控服务',
            '7394' => '商业及生活服务/商业服务/设备、工具、家具和电器出租',
            '7395' => '商业及生活服务/商业服务/商业摄影摄像服务',
            '7399' => '商业及生活服务/商业服务/其他商业服务',
            '7512' => '商业及生活服务/汽车租赁和服务/汽车出租',
            '7513' => '商业及生活服务/汽车租赁和服务/卡车及拖车出租',
            '7519' => '商业及生活服务/汽车租赁和服务/房车和娱乐车辆出租',
            '7523' => '商业及生活服务/汽车租赁和服务/停车服务',
            '7531' => '商业及生活服务/汽车租赁和服务/汽车维修、保养、美容装饰',
            '7534' => '商业及生活服务/汽车租赁和服务/轮胎翻新、维修',
            '7535' => '商业及生活服务/汽车租赁和服务/汽车喷漆店',
            '7538' => '商业及生活服务/汽车租赁和服务/汽车改造等服务（非经销商）',
            '7542' => '商业及生活服务/汽车租赁和服务/洗车',
            '7549' => '商业及生活服务/汽车租赁和服务/拖车服务',
            '7622' => '商业及生活服务/维修服务/电器维修',
            '7623' => '商业及生活服务/维修服务/空调、制冷设备维修',
            '7629' => '商业及生活服务/维修服务/办公电器和小家电维修',
            '7631' => '商业及生活服务/维修服务/手表、钟表和首饰维修店',
            '7641' => '商业及生活服务/维修服务/家具维修、翻新',
            '7692' => '商业及生活服务/维修服务/焊接维修服务',
            '7699' => '商业及生活服务/维修服务/各类维修相关服务',
            '0742' => '商业及生活服务/承包商（农业、建筑、出版）/兽医服务',
            '0743' => '商业及生活服务/承包商（农业、建筑、出版）/葡萄酒生产商',
            '0744' => '商业及生活服务/承包商（农业、建筑、出版）/其他酒类生产商',
            '0763' => '商业及生活服务/承包商（农业、建筑、出版）/农业合作与农具',
            '0780' => '商业及生活服务/承包商（农业、建筑、出版）/景观美化与园艺服务',
            '4814' => '网络虚拟/电信通讯/电信运营商',
            '4815' => '网络虚拟/电信通讯/话费充值与缴费',
            '4821' => '网络虚拟/电信通讯/网络电话、传真',
            '4899' => '网络虚拟/电信通讯/付费电视',
            '7379' => '网络虚拟/互联网服务/计算机维护和修理服务',
            '7829' => '网络虚拟/娱乐票务/艺术创作服务',
            '7832' => '网络虚拟/娱乐票务/电影院及电影票',
            '7841' => '网络虚拟/娱乐票务/音像制品出租',
            '7911' => '网络虚拟/娱乐票务/歌舞厅/夜店',
            '7922' => '网络虚拟/娱乐票务/演出票务服务',
            '7929' => '网络虚拟/娱乐票务/乐队和文艺表演',
            '7932' => '网络虚拟/娱乐票务/桌球/桌游',
            '7933' => '网络虚拟/娱乐票务/保龄球',
            '7941' => '网络虚拟/娱乐票务/体育场馆',
            '7989' => '网络虚拟/娱乐票务/网吧',
            '7990' => '网络虚拟/娱乐票务/棋牌室',
            '7991' => '网络虚拟/娱乐票务/展览和艺术场馆',
            '7992' => '网络虚拟/娱乐票务/高尔夫球场',
            '7994' => '网络虚拟/娱乐票务/电玩娱乐场所',
            '7996' => '网络虚拟/娱乐票务/游乐园、马戏团、嘉年华',
            '7997' => '网络虚拟/娱乐票务/健身和运动俱乐部',
            '7998' => '网络虚拟/娱乐票务/动物园、水族馆',
            '7999' => '网络虚拟/娱乐票务/其他娱乐服务',
            '7995' => '网络虚拟/彩票/彩票',
            '8011' => '专业服务/医疗服务/诊所',
            '8021' => '专业服务/医疗服务/牙科医生',
            '8031' => '专业服务/医疗服务/正骨医生',
            '8041' => '专业服务/医疗服务/按摩医生',
            '8042' => '专业服务/医疗服务/眼科医疗服务',
            '8043' => '专业服务/医疗服务/眼镜店',
            '8050' => '专业服务/医疗服务/护理和照料服务',
            '8061' => '专业服务/医疗服务/民营医院',
            '8071' => '专业服务/医疗服务/医学及牙科实验室',
            '8099' => '专业服务/医疗服务/其他医疗保健服务',
            '8241' => '专业服务/教育服务/函授学校（成人教育）',
            '8244' => '专业服务/教育服务/商业和文秘学校',
            '3007' => '专业服务/教育服务/线下教培',
            '8641' => '专业服务/社会组织/行业协会和专业社团',
            '8661' => '专业服务/社会组织/宗教组织',
            '8675' => '专业服务/社会组织/汽车协会',
            '8699' => '专业服务/社会组织/其他会员组织',
            '8111' => '专业服务/专业咨询/法律咨询和律师事务所',
            '8734' => '专业服务/专业咨询/测试实验服务',
            '8911' => '专业服务/专业咨询/建筑、工程和测量服务',
            '8912' => '专业服务/专业咨询/装修、装潢、园艺',
            '8931' => '专业服务/专业咨询/会计、审计、财务服务',
            '8999' => '专业服务/专业咨询/其他专业服务',
            '9400' => '专业服务/政府服务/使领馆',
            '9402' => '专业服务/政府服务/国家邮政',
            '9701' => '专业服务/其他/Visa信任服务',
            '9702' => '专业服务/其他/GCAS紧急服务（仅限Visa使用）',
            '3005' => '校园服务/腾讯微校/校园直连(食堂类)',
            '3006' => '校园服务/腾讯微校/校园直连(高校类)'
        ];

        $dbsxpayset = Db::name('sysset')->where('name', 'sxpayset')->value('value');
        $dbsxpayset = json_decode($dbsxpayset, true);
        if ($dbsxpayset && $dbsxpayset['orgId']) {
            $feepercent = $dbsxpayset['feepercent'] . '%';
        } else {
            $feepercent = '0.38%';
        }

        View::assign('info', $info);
        View::assign('feepercent', $feepercent);
        View::assign('mccCdArr', $mccCdArr);
        return View::fetch();
    }

    //入驻结果查询

    function getareacode($province, $city = '', $area = '')
    {
        $reqData = [];
        $reqData['province'] = $province;
        $reqData['city'] = $city;
        $reqData['area'] = $area;
        $rs = \app\custom\Sxpay::getareacode($reqData);
        return $rs;
    }

    //修改结果查询

    public function applyQuery()
    {
        $id = input('post.id/d');
        $info = Db::name('sxpay_income')->where('aid', aid)->where('id', $id)->find();
        $reqData = [];
        $reqData['applicationId'] = $info['applicationId'];
        $rs = \app\custom\Sxpay::applyQuery($reqData, $info['mchkey']);
        if ($rs['status'] == 0) {
            return json($rs);
        }
        $data = $rs['data'];
        $repoInfo = $data['repoInfo'];
        $submchid = '';
        $zfbmchid = '';
        foreach ($repoInfo as $v) {
            if ($v['childNoType'] == 'WX') {
                $submchid = $v['childNo'];
            }
            if ($v['childNoType'] == 'ZFB') {
                $zfbmchid = $v['childNo'];
            }
        }
        Db::name('sxpay_income')->where('id', $id)->update(['taskStatus' => $data['taskStatus'], 'suggestion' => $data['suggestion'], 'submchid' => $submchid, 'zfbmchid' => $zfbmchid]);
        return json($rs);
    }

    //绑定appid

    public function modifyQuery()
    {
        $id = input('post.id/d');
        $info = Db::name('sxpay_income')->where('aid', aid)->where('id', $id)->find();
        $reqData = [];
        $reqData['applicationId'] = $info['applicationId_edit'];
        $rs = \app\custom\Sxpay::modifyQuery($reqData, $info['mchkey']);
        if ($rs['status'] == 0) {
            return json($rs);
        }
        $data = $rs['data'];
        Db::name('sxpay_income')->where('id', $id)->update(['taskStatus_edit' => $data['taskStatus'], 'suggestion_edit' => $data['suggestion']]);
        return json($rs);
    }

    //绑定jsapi授权目录

    public function bandappid()
    {
        $id = input('post.id/d');
        $accountType = input('post.bangdAccountType');
        $subAppid = input('post.bindSubAppid');
        $info = Db::name('sxpay_income')->where('aid', aid)->where('id', $id)->find();
        $reqData = [];
        $reqData['mno'] = $info['business_code'];
        $reqData['subMchId'] = $info['submchid'];
        $reqData['type'] = '01';
        $reqData['subAppid'] = $subAppid;
        $reqData['accountType'] = $accountType;
        $rs = \app\custom\Sxpay::addConf($reqData, $info['mchkey']);
        return json($rs);
    }

    //查看配置

    public function setjsapi()
    {
        $id = input('post.id/d');
        $accountType = input('post.bangdAccountType');
        $jsapiPath = input('post.jsapiPath');
        $info = Db::name('sxpay_income')->where('aid', aid)->where('id', $id)->find();
        $reqData = [];
        $reqData['mno'] = $info['business_code'];
        $reqData['subMchId'] = $info['submchid'];
        $reqData['type'] = '03';
        $reqData['jsapiPath'] = $jsapiPath;
        $rs = \app\custom\Sxpay::addConf($reqData, $info['mchkey']);
        return json($rs);
    }

    //签署协议

    public function viewConf()
    {
        $id = input('post.id/d');
        $info = Db::name('sxpay_income')->where('aid', aid)->where('id', $id)->find();
        $reqData = [];
        $reqData['mno'] = $info['business_code'];
        $reqData['subMchId'] = $info['submchid'];
        $rs = \app\custom\Sxpay::viewConf($reqData, $info['mchkey']);
        return json($rs);
    }

    //实名认证

    public function signxieyi()
    {
        $id = input('param.id/d');
        $info = Db::name('sxpay_income')->where('aid', aid)->where('id', $id)->find();
        $reqData = [];
        $reqData['mno'] = $info['business_code'];
        $reqData['signType'] = '00';
        $rs = \app\custom\Sxpay::signxieyi($reqData, $info['mchkey']);
        if ($rs['status'] == 1) {
            return redirect($rs['signUrl']);
        }
        die($rs['msg']);
    }

    //实名认证结果查询

    public function shiming()
    {
        $id = input('post.id/d');
        $info = Db::name('sxpay_income')->where('aid', aid)->where('id', $id)->find();
        if ($info['shiming_status'] != 0) {
            return json(['status' => 1, 'shiming_status' => $info['shiming_status'], 'shiming_qrurl' => $info['shiming_qrurl']]);
        }
        $reqData = [];
        $reqData['mno'] = $info['business_code'];
        $reqData['backUrl'] = PRE_URL . '/notify.php';
        $rs = \app\custom\Sxpay::shiming($reqData, $info['mchkey']);
        if ($rs['status'] == 1) {
            Db::name('sxpay_income')->where('aid', aid)->where('id', $id)->update(['shiming_status' => 1]);
        }
        $rs['shiming_status'] = 1;
        return json($rs);
    }

    //查看密钥

    public function shimingQuery()
    {
        $id = input('post.id/d');
        $info = Db::name('sxpay_income')->where('aid', aid)->where('id', $id)->find();
        $reqData = [];
        $reqData['mno'] = $info['business_code'];
        $rs = \app\custom\Sxpay::shimingQuery($reqData, $info['mchkey']);
        if ($rs['status'] == 1) {
            $shiming_status = 1;
            if ($rs['data']['idenStatus'] == 'APPLYMENT_STATE_WAITTING_FOR_AUDIT') {
                $shiming_status = 1;
            } elseif ($rs['data']['idenStatus'] == 'APPLYMENT_STATE_WAITTING_FOR_CONFIRM_CONTACT') {
                $shiming_status = 2;
            } elseif ($rs['data']['idenStatus'] == 'APPLYMENT_STATE_WAITTING_FOR_CONFIRM_LEGALPERSON') {
                $shiming_status = 3;
            } elseif ($rs['data']['idenStatus'] == 'APPLYMENT_STATE_PASSED') {
                $shiming_status = 4;
            } elseif ($rs['data']['idenStatus'] == 'APPLYMENT_STATE_REJECTED') {
                $shiming_status = 5;
            } elseif ($rs['data']['idenStatus'] == 'APPLYMENT_STATE_FREEZED') {
                $shiming_status = 6;
            } elseif ($rs['data']['idenStatus'] == 'APPLYMENT_STATE_CANCELED') {
                $shiming_status = 7;
            } elseif ($rs['data']['idenStatus'] == 'APPLYMENT_STATE_OPENACCOUNT') {
                $shiming_status = 8;
            }
            Db::name('sxpay_income')->where('id', $id)->update(['shiming_status' => $shiming_status, 'shiming_qrurl' => $rs['data']['infoQrcode']]);
            $rs['shiming_status'] = $shiming_status;
            $rs['shiming_qrurl'] = $rs['data']['infoQrcode'];
        }
        return json($rs);
    }

    //重置密钥

    public function viewMchkey()
    {
        $id = input('post.id/d');
        $info = Db::name('sxpay_income')->where('aid', aid)->where('id', $id)->find();
        return json(['status' => 1, 'data' => ['mchkey' => $info['mchkey'], 'business_code' => $info['business_code']]]);
    }

    //交易记录

    public function updateMchkey()
    {
        $id = input('post.id/d');
        $info = Db::name('sxpay_income')->where('aid', aid)->where('id', $id)->find();
        $reqData = [];
        $reqData['mno'] = $info['business_code'];
        $reqData['applicationId'] = $info['applicationId'];
        $reqData['applicationId_edit'] = $info['applicationId_edit'];
        $rs = \app\custom\Sxpay::updateMchkey($reqData, $info['mchkey']);
        if ($rs['status'] == 0) return json($rs);
        Db::name('sxpay_income')->where('aid', aid)->where('id', $id)->update(['mchkey' => $rs['data']['mchkey']]);
        return json(['status' => 1, 'msg' => '重置成功']);
    }

    //删除

    public function paylog()
    {
        if (request()->isAjax()) {
            $page = input('param.page');
            $limit = input('param.limit');
            if (input('param.field') && input('param.order')) {
                $order = input('param.field') . ' ' . input('param.order');
            } else {
                $order = 'id desc';
            }
            $where = [];
            $where[] = ['aid', '=', aid];
            if (input('param.mchid')) {
                $where[] = ['mch_id', '=', input('param.mchid')];
            } else {
                $mchidArr = Db::name('sxpay_income')->where('aid', aid)->where("business_code != '' && business_code is not null")->column('business_code');
                if ($mchidArr) {
                    $where[] = ['mch_id', 'in', $mchidArr];
                } else {
                    return json(['code' => 0, 'msg' => '查询成功', 'count' => 0, 'data' => []]);
                }
            }
            if (input('param.ctime')) {
                $ctime = explode(' ~ ', input('param.ctime'));
                $where[] = ['createtime', '>=', strtotime($ctime[0])];
                $where[] = ['createtime', '<', strtotime($ctime[1]) + 86400];
            }
            $count = 0 + Db::name('wxpay_log')->where($where)->count();
            $data = Db::name('wxpay_log')->where($where)->page($page, $limit)->order($order)->select()->toArray();
            foreach ($data as $k => $v) {
                $member = Db::name('member')->where('id', $v['mid'])->find();
                $data[$k]['nickname'] = $member['nickname'];
                $data[$k]['headimg'] = $member['headimg'];
            }
            return json(['code' => 0, 'msg' => '查询成功', 'count' => $count, 'data' => $data]);
        }
        return View::fetch();
    }

    //删除

    public function paylogdel()
    {
        $ids = input('post.ids/a');
        if (!$ids) $ids = array(input('post.id/d'));
        Db::name('wxpay_log')->where('aid', aid)->where('id', 'in', $ids)->delete();
        \app\common\System::plog('交易记录删除' . implode(',', $ids), 1);
        return json(['status' => 1, 'msg' => '删除成功']);
    }

    //获取分支行

    public function del()
    {
        $ids = input('post.ids/a');
        Db::name('sxpay_income')->where('aid', aid)->where('id', 'in', $ids)->update(['delete' => 1]);
        return json(['status' => 1, 'msg' => '删除成功']);
    }

    //获取省市区编码

    function getjiesuanbanklist()
    {
        $reqData = [];
        $reqData['account_bank'] = input('post.account_bank');
        $reqData['bank_province'] = input('post.bank_province');
        $reqData['bank_city'] = input('post.bank_city');
        $rs = \app\custom\Sxpay::getjiesuanbanklist($reqData);
        return json($rs);
    }
}