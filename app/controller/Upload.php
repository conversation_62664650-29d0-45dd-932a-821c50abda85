<?php


// +----------------------------------------------------------------------
// | 文件上传
// +----------------------------------------------------------------------
namespace app\controller;

use think\facade\Db;

class Upload extends Common
{
    public function group()
    {
        $groupArr = Db::name('admin_upload_group')->where('aid', aid)->where('uid', $this->uid)->order('sort desc,createtime')->column('id,name');
        return json(['status' => 1, 'data' => $groupArr]);
    }

    public function addgroup()
    {
        $data = [];
        $data['aid'] = aid;
        $data['uid'] = $this->uid;
        $data['createtime'] = time();
        $data['name'] = input('param.name');
        Db::name('admin_upload_group')->insert($data);
        return json(['status' => 1, 'msg' => '创建成功']);
    }

    public function editgroup()
    {
        $data['name'] = input('param.name');
        Db::name('admin_upload_group')->where('aid', aid)->where('uid', $this->uid)->where('id', input('param.gid/d'))->update(['name' => input('param.name')]);
        return json(['status' => 1, 'msg' => '修改成功']);
    }

    public function delgroup()
    {
        Db::name('admin_upload_group')->where('aid', aid)->where('uid', $this->uid)->where('id', input('param.gid/d'))->delete();
        return json(['status' => 1, 'msg' => '删除成功']);
    }

    public function changegroup()
    {
        Db::name('admin_upload')->where('aid', aid)->where('id', 'in', input('param.ids'))->update(['gid' => input('param.gid/d')]);
        return json(['status' => 1, 'msg' => '移动成功']);
    }

    //上传
    public function index()
    {
        //var_dump();die;
        $file = request()->file('file');
        if ($file) {
            $remote = Db::name('sysset')->where('name', 'remote')->value('value');
            $remote = json_decode($remote, true);
            $maxwidth = ($remote['thumb'] == 1 ? $remote['thumb_width'] : 0);
            $maxheight = ($remote['thumb'] == 1 ? $remote['thumb_height'] : 0);
            try {
                validate(['file' => ['fileExt:' . config('app.upload_type')]])->check(['file' => $file]);
                $savename = \think\facade\Filesystem::putFile('' . aid, $file);
                $filepath = 'upload/' . str_replace("\\", '/', $savename);
                $maxwidth = input('post.maxwidth') ? input('post.maxwidth') : $maxwidth;
                $maxheight = input('post.maxheight') ? input('post.maxheight') : $maxheight;

                $rinfo = [];
                $rinfo['extension'] = strtolower($file->getOriginalExtension());
                $rinfo['name'] = $file->getOriginalName();
                if (in_array($rinfo['extension'], array('jpg', 'jpeg', 'png', 'bmp'))) {
                    $size = getimagesize(ROOT_PATH . $filepath);
                    $rinfo['width'] = $size[0];
                    $rinfo['height'] = $size[1];
                    if ($maxwidth > 0 && $maxheight > 0) {
                        if ($rinfo['width'] > $maxwidth || $rinfo['height'] > $maxheight) {
                            $image = \think\Image::open(ROOT_PATH . $filepath);
                            $filepath = substr($filepath, 0, strlen($filepath) - strlen($rinfo['extension']) - 1) . '_thumb.' . $rinfo['extension'];
                            $image->thumb($maxwidth, $maxheight)->save(ROOT_PATH . $filepath, null, 100);
                            $size = getimagesize(ROOT_PATH . $filepath);
                            $rinfo['width'] = $size[0];
                            $rinfo['height'] = $size[1];
                        }
                    }
                }
                $rinfo['url'] = PRE_URL . '/' . $filepath;
                if (!in_array($rinfo['extension'], array('pem', 'xls', 'xlsx'))) {
                    $rinfo['url'] = \app\common\Pic::uploadoss($rinfo['url']);
                    $rinfo['id'] = Db::name('admin_upload')->insertGetId(array(
                        'aid' => $this->aid,
                        'uid' => $this->uid,
                        'name' => $rinfo['name'],
                        'dir' => date('Ymd'),
                        'url' => $rinfo['url'],
                        'type' => $rinfo['extension'],
                        'width' => $rinfo['width'],
                        'height' => $rinfo['height'],
                        'createtime' => time(),
                        'gid' => cookie('browser_gid') && cookie('browser_gid') != '-1' ? cookie('browser_gid') : '0'
                    ));
                }
                return json(['status' => 1, 'msg' => '上传成功', 'url' => $rinfo['url'], 'info' => $rinfo]);
            } catch (\think\exception\ValidateException $e) {
                return json(['status' => 0, 'msg' => $e->getMessage()]);
            }
        } else {
            $errorNo = $_FILES['file']['error'];
            switch ($errorNo) {
                case 1:
                    $errmsg = '上传的文件超过了 upload_max_filesize 选项限制的值';
                    break;
                case 2:
                    $errmsg = '上传文件的大小超过了 HTML 表单中 MAX_FILE_SIZE 选项指定的值';
                    break;
                case 3:
                    $errmsg = '文件只有部分被上传';
                    break;
                case 4:
                    $errmsg = '没有文件被上传';
                    break;
                case 6:
                    $errmsg = '找不到临时文件夹';
                    break;
                case 7:
                    $errmsg = '文件写入失败';
                    break;
                default:
                    $errmsg = '未知上传错误！';
            }
            return json(['status' => 0, 'msg' => $errmsg]);
        }
    }

    //浏览
    public function browser()
    {
        $pagenum = input('param.pagenum') ? input('param.pagenum') : 1;
        $where = [];
        if (input('param.gid') != '-1') {
            $where[] = ['gid', '=', input('param.gid/d')];
        }
        if (input('param.keyword') != '') {
            $where[] = ['name', 'like', '%' . input('param.keyword') . '%'];
        }
        if (input('param.sort') == 1) {
            $sort = 'createtime asc';
        } elseif (input('param.sort') == 3) {
            $sort = 'name asc';
        } elseif (input('param.sort') == 4) {
            $sort = 'name desc';
        } else {
            $sort = 'createtime desc';
        }
        $count = Db::name('admin_upload')->field('id,name,url,type,size,createtime,width,height,dir')->where('aid', aid)->where('uid', $this->uid)->where('isdel', 0)->where('platform', 'ht')->where($where)->where('type', '<>', 'pem')->count();
        $rs = Db::name('admin_upload')->field('id,name,url,type,size,createtime,width,height,dir')->where('aid', aid)->where('uid', $this->uid)->where('isdel', 0)->where('platform', 'ht')->where($where)->where('type', '<>', 'pem')->order($sort)->page($pagenum, 28)->select()->toArray();
        $totalpage = ceil($count / 28);
        return json(['status' => 1, 'isdir' => '0', 'data' => $rs, 'totalpage' => $totalpage]);
    }

    //微信素材
    public function material()
    {
        set_time_limit(0);
        ini_set('memory_limit', '1024M');
        $type = input('param.type') ? input('param.type') : 'image';
        $page = input('param.page') ? input('param.page') : 1;
        $access_token = \app\common\Wechat::access_token(aid, 'mp');
        $url = 'https://api.weixin.qq.com/cgi-bin/material/get_materialcount?access_token=' . $access_token;
        //dump($url);
        $rs = request_get($url);
        $rs = json_decode($rs, true);
        //dump($rs);
        $voice_count = $rs['voice_count'];
        $video_count = $rs['video_count'];
        $image_count = $rs['image_count'];
        $news_count = $rs['news_count'];
        $count = $rs[$type . '_count'];
        //获取素材
        $url = 'https://api.weixin.qq.com/cgi-bin/material/batchget_material?access_token=' . $access_token;
        //dump($url);
        $data = [];
        $data['type'] = $type;
        $data['offset'] = ($page - 1) * 18;
        $data['count'] = 18;
        $rs = request_post($url, jsonEncode($data));
        $rs = json_decode($rs, true);
        //dump($rs);
        $artlist = [];
        if ($rs['item']) {
            $artlist = $rs['item'];
            foreach ($artlist as $k => $v) {
                if ($type == 'voice' || $type == 'video') {
                    $material = Db::name('mp_material')->where('aid', aid)->where('media_id', $v['media_id'])->where('type', $type)->where('endtime', '>', time())->find();
                    if (!$material) {
                        $rs = request_post('https://api.weixin.qq.com/cgi-bin/material/get_material?access_token=' . $access_token, jsonEncode(['media_id' => $v['media_id']]));
                        if ($type == 'video') {
                            $url = json_decode($rs, true)['down_url'];
                            $name = date('Ym/d_His') . rand(1000, 9999) . ($type == 'voice' ? '.mp3' : '.mp4');
                            if (!file_exists(dirname(ROOT_PATH . 'upload/' . $name))) {
                                mk_dir(dirname(ROOT_PATH . 'upload/' . $name));
                            }
                            file_put_contents(ROOT_PATH . 'upload/' . $name, curl_get($url));
                            $url = PRE_URL . '/upload/' . $name;
                        } else {
                            $name = date('Ym/d_His') . rand(1000, 9999) . ($type == 'voice' ? '.mp3' : '.mp4');
                            if (!file_exists(dirname(ROOT_PATH . 'upload/' . $name))) {
                                mk_dir(dirname(ROOT_PATH . 'upload/' . $name));
                            }
                            file_put_contents(ROOT_PATH . 'upload/' . $name, $rs);
                            $url = PRE_URL . '/upload/' . $name;
                        }
                        $artlist[$k]['url'] = $url;
                        Db::name('mp_material')->insert(['aid' => aid, 'url' => $url, 'media_id' => $v['media_id'], 'type' => $type, 'createtime' => time()]);
                    } else {
                        $artlist[$k]['url'] = $material['url'];
                    }
                } elseif ($v['url'] && $v['media_id']) {
                    $material = Db::name('mp_material')->where('aid', aid)->where('media_id', $v['media_id'])->where('type', $type)->where('endtime', '>', time())->find();
                    if (!$material) {
                        $picurl = \app\common\Pic::tolocal($v['url']);
                        $artlist[$k]['url'] = $picurl;
                        Db::name('mp_material')->insert(['aid' => aid, 'url' => $picurl, 'media_id' => $v['media_id'], 'type' => $type, 'createtime' => time()]);
                    } else {
                        $artlist[$k]['url'] = $material['url'];
                    }
                }
            }
        }
        return json(['code' => 0, 'msg' => '查询成功', 'count' => $count, 'data' => $artlist]);
    }

    //删除
    public function deletefile()
    {
        $list = Db::name('admin_upload')->where('aid', aid)->where('uid', $this->uid)->where('id', 'in', input('post.ids'))->select()->toArray();
        Db::name('admin_upload')->where('aid', aid)->where('uid', $this->uid)->where('id', 'in', input('post.ids'))->delete();
        foreach ($list as $k => $v) {
            \app\common\Pic::deletepic($v['url']);
        }
        return json(['status' => 1, 'msg' => '删除成功']);
    }


    //图标库
    public function iconsvg()
    {
        if ($_POST['op'] == 'init') {
            $historylist = Db::name('iconsvg_history')->field('iconid id,name,show_svg')->where(array('aid' => aid, 'uid' => $this->uid))->limit(10)->order('createtime desc')->select();
            if (!$historylist) $historylist = [];

            $rs = curl_post('https://www.diandashop.com/index/api/iconsvg?op=init&domain=' . $_SERVER['HTTP_HOST']);
            $rs = json_decode($rs, true);
            if ($rs['status'] == 1) {
                $collection = $rs['collection'];
                $iconslist = $rs['iconslist'];
            } else {
                $collection = [];
                $iconslist = [];
            }
            return json(['clist' => $collection, 'iconslist' => $iconslist, 'historylist' => $historylist]);
        }
        if ($_POST['op'] == 'geticonlist') {
            $rs = curl_post('https://www.diandashop.com/index/api/iconsvg?op=geticonlist&domain=' . $_SERVER['HTTP_HOST'], ['cid' => $_POST['cid']]);
            $rs = json_decode($rs, true);
            if ($rs['status'] == 1) {
                $iconlist = $rs['iconlist'];
            } else {
                $iconlist = [];
            }
            return json(['iconlist' => $iconlist]);
        }
        if ($_POST['op'] == 'searchiconlist') {
            $pagenum = $_POST['pagenum'] ? $_POST['pagenum'] : 1;
            $keyword = $_POST['keyword'];

            $rs = curl_post('https://www.diandashop.com/index/api/iconsvg?op=geticonlist&domain=' . $_SERVER['HTTP_HOST'], ['pagenum' => $pagenum, 'keyword' => $keyword]);
            $rs = json_decode($rs, true);
            if ($rs['status'] == 1) {
                $iconlist = $rs['iconlist'];
            } else {
                $iconlist = [];
            }
            return json(['iconlist' => $iconlist]);
        }
        if ($_POST['op'] == 'geticonurl') {
            $pngdata = $_POST['pngdata'];
            $pngdata = str_replace('data:image/png;base64,', '', $pngdata);
            $pngdata = base64_decode(str_replace(' ', '+', $pngdata));

            $dir = 'upload/' . aid . date('/Ymd');
            if (!is_dir(ROOT_PATH . '/' . $dir)) mk_dir(ROOT_PATH . '/' . $dir, 0755, true);
            $filename = date('His') . rand(1000, 9999) . '.png';
            $mediapath = $dir . '/' . $filename;

            file_put_contents(ROOT_PATH . '/' . $mediapath, $pngdata);
            $size = getimagesize(ROOT_PATH . '/' . $mediapath);

            $url = PRE_URL . '/' . $mediapath;
            $url = \app\common\Pic::uploadoss($url);
            $adata = [];
            $adata['aid'] = aid;
            $adata['uid'] = $this->uid;
            $adata['iconid'] = $_POST['iconid'];
            $adata['name'] = $_POST['name'];
            $adata['show_svg'] = $_POST['show_svg'];
            $adata['pngurl'] = $url;
            $adata['createtime'] = time();
            Db::name('iconsvg_history')->insert($adata);


            Db::name('admin_upload')->insertGetId(array(
                'aid' => $this->aid,
                'uid' => $this->uid,
                'name' => $adata['name'],
                'dir' => date('Ymd'),
                'url' => $adata['pngurl'],
                'type' => 'png',
                'width' => $size[0],
                'height' => $size[1],
                'createtime' => time(),
                'gid' => '0'
            ));

            return json(['status' => 1, 'url' => $url]);
        }
    }

    function sizecount($size)
    {
        if ($size >= 1073741824) {
            $size = round($size / 1073741824 * 100) / 100 . ' GB';
        } elseif ($size >= 1048576) {
            $size = round($size / 1048576 * 100) / 100 . ' MB';
        } elseif ($size >= 1024) {
            $size = round($size / 1024 * 100) / 100 . ' KB';
        } else {
            $size = $size . ' Bytes';
        }
        return $size;
    }
}
