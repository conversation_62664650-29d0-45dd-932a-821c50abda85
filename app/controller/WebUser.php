<?php


// +----------------------------------------------------------------------
// | 后台账号 子账号
// +----------------------------------------------------------------------
namespace app\controller;

use think\facade\Db;
use think\facade\View;

class WebUser extends Common
{
    public function initialize()
    {
        parent::initialize();
        $this->uid = session('BST_ID');
        $this->user = db('admin_user')->where(['id' => $this->uid])->find();
        if (!session('BST_ID') || !$this->user || $this->user['isadmin'] != 2) {
            showmsg('无访问权限');
        }
    }

    //账号列表
    public function index()
    {
        if (request()->isAjax()) {
            $page = input('param.page');
            $limit = input('param.limit');

            if (input('param.field') && input('param.order')) {
                if (input('param.field') == 'logintime') {
                    $order = 'admin_user.' . input('param.field') . ' ' . input('param.order');
                } else {
                    $order = 'admin.' . input('param.field') . ' ' . input('param.order');
                }
            } else {
                $order = 'admin.id desc';
            }
            $where = [];
            if (input('param.aid')) $where[] = ['admin.id', '=', input('param.aid')];
            if (input('param.un')) $where[] = ['admin_user.un', 'like', '%' . input('param.un') . '%'];
            if (input('param.tel')) $where[] = ['admin.tel', 'like', '%' . input('param.tel') . '%'];
            if (input('param.linkman')) $where[] = ['admin.linkman', 'like', '%' . input('param.linkman') . '%'];
            if (input('?param.status') && input('param.status') !== '') $where[] = ['admin.status', '=', input('param.status')];
            $count = 0 + Db::name('admin')->alias('admin')->join('admin_user admin_user', 'admin.id=admin_user.aid and admin_user.isadmin>0')->where($where)->count();
            $data = Db::name('admin')->alias('admin')->field('admin.*,admin_user.id uid,admin_user.un,admin_user.pwd,admin_user.logintime')->join('admin_user admin_user', 'admin.id=admin_user.aid and admin_user.bid=0 and admin_user.isadmin>0')->where($where)->limit(($page - 1) * $limit . ',' . $limit)->order($order)->select()->toArray();
            //dump(Db::name()->getlastsql());
            //dump($data);die;
            foreach ($data as $k => $v) {
                $data[$k]['wxappinfo'] = \app\common\System::appinfo($v['id'], 'wx');
                $data[$k]['mpappinfo'] = \app\common\System::appinfo($v['id'], 'mp');
                $admin_set = db('admin_set')->where('aid', $v['id'])->find();
                if (!$admin_set) {
                    $color1 = '#FD4A46';
                } else {
                    $color1 = $admin_set['color1'];
                }
                $data[$k]['color1'] = $color1;
            }
            return ['code' => 0, 'msg' => '查询成功', 'count' => $count, 'data' => $data];
        }
        if (input('param.type') == 1) {
            $lastDayStart = strtotime(date('Y-m-d', time() - 86400));
            $lastDayEnd = $lastDayStart + 86400;
            $thisMonthStart = strtotime(date('Y-m-1'));
            $nowtime = time();
            //会员数
            $memberCount = 0 + Db::name('member')->where([])->count();
            $memberLastDayCount = 0 + Db::name('member')->where('createtime', '>=', $lastDayStart)->where('createtime', '<', $lastDayEnd)->count();
            $memberThisMonthCount = 0 + Db::name('member')->where('createtime', '>=', $thisMonthStart)->where('createtime', '<', $nowtime)->count();
            //订单数 订单金额
            $ordernumCount = 0 + Db::name('shop_order')->where('status', 'in', '1,2,3')->count();
            $ordernumLastDayCount = 0 + Db::name('shop_order')->where('status', 'in', '1,2,3')->where('paytime', '>=', $lastDayStart)->where('paytime', '<', $lastDayEnd)->count();
            $ordernumThisMonthCount = 0 + Db::name('shop_order')->where('status', 'in', '1,2,3')->where('paytime', '>=', $thisMonthStart)->where('paytime', '<', $nowtime)->count();
            $ordermoneyCount = 0 + Db::name('shop_order')->where('status', 'in', '1,2,3')->sum('totalprice');
            $ordermoneyLastDayCount = 0 + Db::name('shop_order')->where('status', 'in', '1,2,3')->where('paytime', '>=', $lastDayStart)->where('paytime', '<', $lastDayEnd)->sum('totalprice');
            $ordermoneyThisMonthCount = 0 + Db::name('shop_order')->where('status', 'in', '1,2,3')->where('paytime', '>=', $thisMonthStart)->where('paytime', '<', $nowtime)->sum('totalprice');
            //商品数量
            $productCount = 0 + Db::name('shop_product')->where([])->count();
            $product0Count = 0 + Db::name('shop_product')->where('status', 0)->count();
            $product1Count = 0 + Db::name('shop_product')->where('status', 1)->count();

            //收款金额
            $payCount = Db::name('payorder')->where('paytypeid', 'not in', '1,4')->where('status', 1)->sum('money');
            $payLastDayCount = 0 + Db::name('payorder')->where('paytypeid', 'not in', '1,4')->where('status', 1)->where('paytime', '>=', $lastDayStart)->where('paytime', '<', $lastDayEnd)->sum('money');
            $payThisMonthCount = 0 + Db::name('payorder')->where('paytypeid', 'not in', '1,4')->where('status', 1)->where('paytime', '>=', $thisMonthStart)->where('paytime', '<', $nowtime)->sum('money');

            View::assign('memberCount', $memberCount);
            View::assign('memberLastDayCount', $memberLastDayCount);
            View::assign('memberThisMonthCount', $memberThisMonthCount);
            View::assign('ordernumCount', $ordernumCount);
            View::assign('ordernumLastDayCount', $ordernumLastDayCount);
            View::assign('ordernumThisMonthCount', $ordernumThisMonthCount);
            View::assign('ordermoneyCount', $ordermoneyCount);
            View::assign('ordermoneyLastDayCount', $ordermoneyLastDayCount);
            View::assign('ordermoneyThisMonthCount', $ordermoneyThisMonthCount);
            View::assign('productCount', $productCount);
            View::assign('product0Count', $product0Count);
            View::assign('product1Count', $product1Count);
            View::assign('payCount', $payCount);
            View::assign('payLastDayCount', $payLastDayCount);
            View::assign('payThisMonthCount', $payThisMonthCount);

            //是否配置了计划任务
            $autotimes = cache('autotimes');
            if ($autotimes) {
                $isauto = 1;
            } else {
                $isauto = 0;
            }
            View::assign('isauto', $isauto);

            $config = include('config.php');
            $authkey = $config['authkey'];
            $domain = $_SERVER['HTTP_HOST'];
            $rs = request_post('https://www.diandashop.com/index/upgrade2/getversion', ['authkey' => $authkey, 'authdomain' => $domain]);
            $rsdata = json_decode($rs, true);
            $needupgrade = 0;
            $myversion = file_get_contents('version.php');
            if ($rsdata && $rsdata['status'] == 1 && $rsdata['version'] != $myversion) {
                $needupgrade = 1;
            }
            View::assign('needupgrade', $needupgrade);
            View::assign('myversion', $myversion);
            View::assign('newversion', $rsdata['version']);

        }
        return View::fetch();
    }

    //登录
    public function alogin()
    {
        $rs = Db::name('admin_user')->where(array('id' => input('param.uid/d')))->find();
        if ($rs) {
            session('ADMIN_LOGIN', 1);
            session('ADMIN_UID', $rs['id']);
            session('ADMIN_AID', $rs['aid']);
            session('ADMIN_BID', $rs['bid']);
            session('ADMIN_NAME', $rs['un']);
            session('IS_ADMIN', $rs['isadmin']);
            return redirect((string)url('Backstage/index'));
        }
    }

    //编辑
    public function edit()
    {
        $menudata = \app\common\Menu::getdata(0, 0);
        if (input('param.id')) {
            $id = input('param.id/d');
            define('aid', $id);
            $ainfo = Db::name('admin')->where('id', $id)->find();
            $asetinfo = Db::name('admin_set')->where('aid', $id)->find();
            $uinfo = Db::name('admin_user')->where('aid', $id)->where('bid', 0)->where('isadmin', '>', 0)->find();
            $auth_data = $uinfo ? json_decode($uinfo['auth_data'], true) : array();
        } else {
            $ainfo = ['type' => 1, 'status' => 1, 'endtime' => time() + 86400 * 365, 'platform' => 'mp,wx'];
            $asetinfo = [];
            $uinfo = [];
            $auth_data = [];
        }

        View::assign('auth_data', $auth_data);
        View::assign('menudata', $menudata);

        View::assign('ainfo', $ainfo);
        View::assign('asetinfo', $asetinfo);
        View::assign('uinfo', $uinfo);
        View::assign('rinfo', json_decode($ainfo['remote'], true));
        return View::fetch();
    }

    public function save()
    {
        $ainfo = input('post.ainfo/a');
        $uinfo = input('post.uinfo/a');
        $uinfo['auth_data'] = str_replace('^_^', '\/*', jsonEncode(input('post.auth_data/a')));
        $ainfo['platform'] = implode(',', $ainfo['platform']);
        $ainfo['remote'] = jsonEncode(input('post.rinfo/a'));
        //$asetinfo = input('post.asetinfo/a');

        $hasun = Db::name('admin_user')->where('id', '<>', $uinfo['id'])->where('un', $uinfo['un'])->find();
        if ($hasun) {
            return json(['status' => 0, 'msg' => '该账号已存在']);
        }
        $ainfo['endtime'] = strtotime($ainfo['endtime']);
        if ($ainfo['id']) {
            Db::name('admin')->where('id', $ainfo['id'])->update($ainfo);
            \app\common\System::plog('编辑用户' . $ainfo['id'], 1);
        } else {
            $ainfo['createtime'] = time();
            $ainfo['token'] = random(10);
            $ainfo['id'] = Db::name('admin')->insertGetId($ainfo);
            \app\common\System::initaccount($ainfo['id']);
            \app\common\System::plog('添加用户' . $ainfo['id'], 1);
        }
        if ($uinfo['id']) {
            if ($uinfo['pwd'] != '') {
                $uinfo['pwd'] = md5($uinfo['pwd']);
            } else {
                unset($uinfo['pwd']);
            }
            Db::name('admin_user')->where('id', $uinfo['id'])->update($uinfo);
        } else {
            $uinfo['pwd'] = md5($uinfo['pwd']);
            $uinfo['aid'] = $ainfo['id'];
            $uinfo['createtime'] = time();
            $uinfo['isadmin'] = 1;
            $uinfo['random_str'] = random(16);
            Db::name('admin_user')->insert($uinfo);
        }
        return json(['status' => 1, 'msg' => '操作成功', 'url' => (string)url('index')]);
    }

    //删除
    public function del()
    {
        $ids = input('post.ids/a');
        foreach ($ids as $id) {
            if ($id == 1) return json(['status' => 0, 'msg' => '总账号不允许删除']);
            $admin = Db::name('admin')->where('id', intval($id))->find();
            Db::name('admin')->where('id', intval($id))->delete();
            Db::name('admin_user')->where('aid', intval($id))->where('bid', 0)->delete();
        }
        \app\common\System::plog('删除用户' . implode(',', $ids), 1);
        return json(['status' => 1, 'msg' => '删除成功']);
    }

    //下载小程序uniapp代码包
    public function downloaduniapp()
    {
        if ($_SERVER['HTTP_HOST'] == 'v2.diandashop.com' || $_SERVER['HTTP_HOST'] == 'v2a.diandashop.com' || $_SERVER['HTTP_HOST'] == 'v2d.diandashop.com') {
            return json(['status' => 0, 'msg' => '演示站无下载权限']);
        }
        $aid = input('post.aid');

        $navigationBarBackgroundColor = input('post.navigationBarBackgroundColor');
        $navigationBarTextStyle = input('post.navigationBarTextStyle');
        $admin = Db::name('admin')->where('id', $aid)->find();
        $sysset = Db::name('admin_set')->where('aid', $aid)->find();
        $appapp = Db::name('admin_setapp_app')->where('aid', $aid)->find();

        //import('file',EXTEND_PATH);
        $wxdir = ROOT_PATH . 'uniapp';
        $copydir = ROOT_PATH . 'upload/uniapp' . $aid;
        \app\common\File::clear_dir($copydir);
        \app\common\File::all_copy($wxdir, $copydir);

        //配置文件 pages.json
        $window = array(
            "navigationBarBackgroundColor" => $navigationBarBackgroundColor,
            "navigationBarTextStyle" => $navigationBarTextStyle,
            "navigationBarTitleText" => '',
            "h5" => ["titleNView" => false]
        );

        //页面 pages
        $pagesjson = file_get_contents(ROOT_PATH . 'uniapp/pages.json');
        $pagesjson = str_replace('"navigationBarTextStyle": "black"', '"navigationBarTextStyle": "' . $navigationBarTextStyle . '"', $pagesjson);
        $pagesjson = str_replace('"navigationBarBackgroundColor": "#F8F8F8"', '"navigationBarBackgroundColor": "' . $navigationBarBackgroundColor . '"', $pagesjson);
        file_put_contents($copydir . '/pages.json', $pagesjson);

        //配置信息
        $uniacid = $aid;
        if ($admin['domain']) {
            $siteroot = 'https://' . $admin['domain'];
        } else {
            $siteroot = str_replace('http://', 'https://', request()->domain());
        }
        $siteinfostr = 'var siteinfo = {"uniacid":"' . $uniacid . '","siteroot":"' . $siteroot . '"};module.exports = siteinfo;';
        file_put_contents($copydir . '/siteinfo.js', $siteinfostr);

        $zipname = uniqid() . '.zip';
        $zippath = ROOT_PATH . 'upload/' . $zipname;
        $myfile = fopen($zippath, "w");
        fclose($myfile);
        \app\common\File::add_file_to_zip($copydir, $zippath, 'uniapp_' . $aid);
        $url = PRE_URL . '/upload/' . $zipname;
        \app\common\File::remove_dir($copydir);
        \app\common\System::plog('下载uniapp代码' . $aid, 1);
        return json(['status' => 1, 'msg' => '打包成功', 'url' => $url]);
    }
}
