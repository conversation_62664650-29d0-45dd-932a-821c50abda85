<?php


// +----------------------------------------------------------------------
// | 小程序设置
// +----------------------------------------------------------------------
namespace app\controller;

use think\facade\Db;
use think\facade\View;

class Wxset extends Common
{
    //列表
    public function index()
    {
        $set = Db::name('admin_setapp_wx')->where('aid', aid)->find();
        if (!$set || $set['appid'] == '') {
            showmsg('请先创建或授权小程序');
        }
        $access_token = \app\common\Wechat::access_token(aid, 'wx');
        $info = curl_get('https://api.weixin.qq.com/cgi-bin/account/getaccountbasicinfo?access_token=' . $access_token);
        $info = json_decode($info, true);
        //dump($info);
        if ($info['errcode']) {
            if ($info['errcode'] == '41033') {
                showmsg('非本平台创建的小程序无法设置，请前往微信公众平台进行设置(mp.weixin.qq.com)');
            } else {
                showmsg(\app\common\Wechat::geterror($info));
            }
        } else {
            //已设置的类目
            $category = curl_get('https://api.weixin.qq.com/cgi-bin/wxopen/getcategory?access_token=' . $access_token);
            $category = json_decode($category, true);
            //dump($category);
            //是否可被搜索到
            $cansearch = curl_get('https://api.weixin.qq.com/wxa/getwxasearchstatus?access_token=' . $access_token);
            $cansearch = json_decode($cansearch, true);
            //附近小程序地点列表
            $nearbypoidata = curl_get('https://api.weixin.qq.com/wxa/getnearbypoilist?page=1&page_rows=20&access_token=' . $access_token);
            $nearbypoidata = json_decode($nearbypoidata, true);
            $poi_list = json_decode($nearbypoidata['data']['data'], true);

            //是否开通直播
            $url = 'https://api.weixin.qq.com/wxa/business/getliveinfo?access_token=' . $access_token;
            $rs = request_post($url, jsonEncode(['start' => 0, 'limit' => 1]));
            $rs = json_decode($rs, true);
            if (isset($rs['errcode']) && $rs['errcode'] != 0 && $rs['errcode'] != 1) {//未开通
                $info['livestatus'] = 0;
            } else {
                $info['livestatus'] = 1;
            }


            $info['user_name'] = $set['nickname'];

            View::assign('info', $info);
            View::assign('category', $category);
            View::assign('cansearch', $cansearch);
            View::assign('nearbypoidata', $nearbypoidata);
            View::assign('poi_list', $poi_list);
            return View::fetch();
        }
    }

    //设置小程序名称
    public function setnickname()
    {
        $access_token = \app\common\Wechat::access_token(aid, 'wx');
        $data = [];
        $data['nick_name'] = input('post.set_nickname_nickname');
        if (!$data['nick_name']) {
            return json(['status' => 0, 'msg' => '请填写名称']);
        }
        if (input('post.set_nickname_license')) {
            $data['license'] = \app\common\Wechat::pictomedia(aid, 'wx', input('post.set_nickname_license'));
        }
        if (input('post.naming_other_stuff_1')) {
            $data['naming_other_stuff_1'] = \app\common\Wechat::pictomedia(aid, 'wx', input('post.naming_other_stuff_1'));
        }
        $rs = curl_post('https://api.weixin.qq.com/wxa/setnickname?access_token=' . $access_token, jsonEncode($data));
        $rs = json_decode($rs, true);
        if ($rs['errcode'] != 0) {
            return json(['status' => 0, 'msg' => \app\common\Wechat::geterror($rs) . ($rs['wording'] ? '-' . $rs['wording'] : '')]);
        } else {
            Db::name('admin_setapp_wx')->where('aid', aid)->update(['nickname' => $data['nick_name']]);
            if ($rs['audit_id']) {
                return json(['status' => 1, 'msg' => '名称已提交,请等待审核', 'url' => (string)url('index')]);
            } else {
                return json(['status' => 1, 'msg' => '修改成功', 'url' => (string)url('index')]);
            }
        }
    }

    //设置小程序头像
    public function setheadimg()
    {
        $access_token = \app\common\Wechat::access_token(aid, 'wx');
        $headimg = input('post.set_headimg');
        if (!$headimg) return json(['status' => 0, 'msg' => '请上传头像']);
        $data = array();
        $data['head_img_media_id'] = \app\common\Wechat::pictomedia(aid, 'wx', $headimg);
        $data['x1'] = 0;
        $data['y1'] = 0;
        $data['x2'] = 1;
        $data['y2'] = 1;
        $rs = curl_post('https://api.weixin.qq.com/cgi-bin/account/modifyheadimage?access_token=' . $access_token, jsonEncode($data));
        $rs = json_decode($rs, true);
        //dump($data);
        if ($rs['errcode'] != 0) {
            return json(['status' => 0, 'msg' => \app\common\Wechat::geterror($rs)]);
        } else {
            $info = curl_get('https://api.weixin.qq.com/cgi-bin/account/getaccountbasicinfo?access_token=' . $access_token);
            $info = json_decode($info, true);
            $head_image_url = \app\common\Pic::uploadoss($info['head_image_info']['head_image_url']);
            Db::name('admin_setapp_wx')->where('aid', aid)->update(['headimg' => $head_image_url]);
            return json(['status' => 1, 'msg' => '修改成功', 'url' => (string)url('index')]);
        }
    }

    //设置小程序简介
    public function setsignature()
    {
        $access_token = \app\common\Wechat::access_token(aid, 'wx');
        $signature = input('post.set_signature');
        $data = array('signature' => $signature);
        $rs = curl_post('https://api.weixin.qq.com/cgi-bin/account/modifysignature?access_token=' . $access_token, jsonEncode($data));
        $rs = json_decode($rs, true);
        //dump($data);
        if ($rs['errcode'] != 0) {
            return json(['status' => 0, 'msg' => \app\common\Wechat::geterror($rs)]);
        } else {
            Db::name('admin_setapp_wx')->where('aid', aid)->update(['signature' => $signature]);
            return json(['status' => 1, 'msg' => '修改成功', 'url' => (string)url('index')]);
        }
    }

    public function closesearch()
    {
        $access_token = \app\common\Wechat::access_token(aid, 'wx');
        $data = array('status' => 1);
        $rs = curl_post('https://api.weixin.qq.com/wxa/changewxasearchstatus?access_token=' . $access_token, jsonEncode($data));
        $rs = json_decode($rs, true);
        if ($rs['errcode'] != 0) {
            return json(['status' => 0, 'msg' => \app\common\Wechat::geterror($rs)]);
        } else {
            return json(['status' => 1, 'msg' => '修改成功', 'url' => (string)url('index')]);
        }
    }

    public function opensearch()
    {
        $access_token = \app\common\Wechat::access_token(aid, 'wx');
        $data = array('status' => 0);
        $rs = curl_post('https://api.weixin.qq.com/wxa/changewxasearchstatus?access_token=' . $access_token, jsonEncode($data));
        $rs = json_decode($rs, true);
        if ($rs['errcode'] != 0) {
            return json(['status' => 0, 'msg' => \app\common\Wechat::geterror($rs)]);
        } else {
            return json(['status' => 1, 'msg' => '修改成功', 'url' => (string)url('index')]);
        }
    }

    public function poiadd()
    {
        $access_token = \app\common\Wechat::access_token(aid, 'wx');
        $related_proof_material = input('post.related_proof_material');
        $data = array();
        $data['related_name'] = $_POST['related_name'];
        $data['related_credential'] = $_POST['related_credential'];
        $data['related_address'] = $_POST['related_address'];
        if ($related_proof_material) {
            $data['related_proof_material'] = \app\common\Wechat::pictomedia(aid, 'wx', $related_proof_material);
        }
        $rs = curl_post('https://api.weixin.qq.com/wxa/addnearbypoi?access_token=' . $access_token, jsonEncode($data));
        $rs = json_decode($rs, true);
        if ($rs['errcode'] != 0) {
            return json(['status' => 0, 'msg' => \app\common\Wechat::geterror($rs)]);
        } else {
            return json(['status' => 1, 'msg' => '已提交,请等待审核', 'url' => (string)url('index')]);
        }
    }

    public function setpoist()
    {
        $access_token = \app\common\Wechat::access_token(aid, 'wx');
        $data = array();
        $data['poi_id'] = $_POST['poi_id'];
        $data['status'] = $_POST['st'];
        $rs = curl_post('https://api.weixin.qq.com/wxa/setnearbypoishowstatus?access_token=' . $access_token, jsonEncode($data));
        $rs = json_decode($rs, true);
        if ($rs['errcode'] != 0) {
            return json(['status' => 0, 'msg' => \app\common\Wechat::geterror($rs)]);
        } else {
            return json(['status' => 1, 'msg' => '操作成功', 'url' => (string)url('index')]);
        }
    }

    //申请开通小程序直播
    public function applylive()
    {
        $access_token = \app\common\Wechat::access_token(aid, 'wx');
        $data = array();
        $data['action'] = 'apply';
        $rs = curl_post('https://api.weixin.qq.com/wxa/business/applyliveinfo?access_token=' . $access_token, jsonEncode($data));
        $rs = json_decode($rs, true);
        if ($rs['errcode'] != 0) {
            if ($rs['errcode'] == 2) {
                return json(['status' => 0, 'msg' => '小程序近90天没有存在支付行为，不能申请开通直播能力（数据生效时间为T+1，请耐心等待）']);
            }
            return json(['status' => 0, 'msg' => \app\common\Wechat::geterror($rs)]);
        } else {
            return json(['status' => 1, 'msg' => '请小程序管理员在微信端点击消息通知卡片进入功能开通页面', 'url' => (string)url('index')]);
        }
    }

    //设置用户隐私指引
    public function yinsi()
    {
        $access_token = \app\common\Wechat::access_token(aid, 'wx');
        if (request()->isPost()) {
            $owner_setting = input('post.owner_setting/a');
            $setting_list = input('post.setting_list/a');

            $new_setting_list = [];
            foreach ($setting_list as $k => $v) {
                $new_setting_list[] = ['privacy_key' => $k, 'privacy_text' => $v];
            }
            $postdata = [];
            $postdata['owner_setting'] = $owner_setting;
            $postdata['setting_list'] = $new_setting_list;
            $rs = curl_post('https://api.weixin.qq.com/cgi-bin/component/setprivacysetting?access_token=' . $access_token, jsonEncode($postdata));
            $rs = json_decode($rs, true);
            if ($rs['errcode'] != 0) {
                return json(['status' => 0, 'msg' => \app\common\Wechat::geterror($rs)]);
            } else {
                return json(['status' => 1, 'msg' => '设置成功', 'url' => true]);
            }
        }
        $rs = curl_post('https://api.weixin.qq.com/cgi-bin/component/getprivacysetting?access_token=' . $access_token, '{}');
        $rs = json_decode($rs, true);
        if ($rs['errcode'] != 0) {
            return json(['status' => 0, 'msg' => \app\common\Wechat::geterror($rs)]);
        }

        $privacy_list = $rs['privacy_list'];
        $setting_list = $rs['setting_list'];
        $owner_setting = $rs['owner_setting'];
        $privacy_desc_list = $rs['privacy_desc']['privacy_desc_list'];

        $privacyArr = [];
        if ($privacy_list) {
            foreach ($privacy_list as $k => $v) {
                $privacy_text = '';
                $privacy_desc = '';
                foreach ($setting_list as $k2 => $v2) {
                    if ($v2['privacy_key'] == $v) {
                        $privacy_text = $v2['privacy_text'];
                    }
                }
                foreach ($privacy_desc_list as $k3 => $v3) {
                    if ($v3['privacy_key'] == $v) {
                        $privacy_desc = $v3['privacy_desc'];
                    }
                }
                $privacyArr[$v] = ['desc' => $privacy_desc, 'text' => $privacy_text];
            }
        }
        View::assign('privacyArr', $privacyArr);
        View::assign('owner_setting', $owner_setting);
        return View::fetch();
    }
}