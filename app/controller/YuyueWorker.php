<?php


// +----------------------------------------------------------------------
// | 配送员
// +----------------------------------------------------------------------
namespace app\controller;

use think\facade\Db;
use think\facade\View;

class YuyueWorker extends Common
{
    public function initialize()
    {
        parent::initialize();
    }

    //列表
    public function index()
    {
        if (request()->isAjax()) {
            $page = input('param.page');
            $limit = input('param.limit');
            if (input('param.field') && input('param.order')) {
                $order = input('param.field') . ' ' . input('param.order');
            } else {
                $order = 'sort desc,id';
            }
            $where = array();
            $where[] = ['aid', '=', aid];
            $where[] = ['bid', '=', bid];
            if (input('param.realname')) $where[] = ['realname', 'like', '%' . input('param.realname') . '%'];
            if (input('?param.status') && input('param.status') !== '') $where[] = ['status', '=', input('param.status')];
            $count = 0 + Db::name('yuyue_worker')->where($where)->count();
            $data = Db::name('yuyue_worker')->where($where)->page($page, $limit)->order($order)->select()->toArray();
            foreach ($data as $k => $v) {
                $member = Db::name('member')->where('id', $v['mid'])->find();
                $data[$k]['nickname'] = $member['nickname'];
                $data[$k]['mheadimg'] = $member['headimg'];
                $pszCount = Db::name('yuyue_worker_order')->where('aid', aid)->where('worker_id', $v['id'])->where('status', 'in', '0,1,2')->count();
                $ywcCount = Db::name('yuyue_worker_order')->where('aid', aid)->where('worker_id', $v['id'])->where('status', 3)->count();
                $data[$k]['pszCount'] = $pszCount;
                $data[$k]['ywcCount'] = $ywcCount;
            }
            return json(['code' => 0, 'msg' => '查询成功', 'count' => $count, 'data' => $data]);
        }
        $set = db('yuyue_set')->field('diyname')->where('aid', aid)->find();
        View::assign('set', $set);
        return View::fetch();
    }

    //编辑
    public function edit()
    {
        if (input('param.id')) {
            $info = Db::name('yuyue_worker')->where('aid', aid)->where('bid', bid)->where('id', input('param.id/d'))->find();
        } else {
            $info = array('id' => '');
        }
        //分类
        $clist = Db::name('yuyue_worker_category')->Field('id,name')->where('aid', aid)->where('bid', bid)->where('status', 1)->where('pid', 0)->order('sort desc,id')->select()->toArray();
        View::assign('clist', $clist);
        View::assign('info', $info);
        return View::fetch();
    }

    public function save()
    {
        $info = input('post.info/a');
        $hasun = Db::name('yuyue_worker')->where('aid', aid)->where('id', '<>', $info['id'])->where('un', $info['un'])->find();
        if ($hasun) {
            return json(['status' => 0, 'msg' => '该账号已被占用']);
        }
        if ($info['id']) {
            //$member = Db::name('member')->where('id',$info['mid'])->find();
            //$info['headimg'] = $member['headimg'];
            $info['pwd'] = md5($info['pwd']);
            Db::name('yuyue_worker')->where('aid', aid)->where('id', $info['id'])->update($info);
            \app\common\System::plog('编辑人员' . $info['id']);
        } else {
            if ($info['pwd'] != '') {
                $info['pwd'] = md5($info['pwd']);
            } else {
                unset($info['pwd']);
            }
            $info['aid'] = aid;
            $info['bid'] = bid;
            $info['createtime'] = time();
            //$member = Db::name('member')->where('id',$info['mid'])->find();
            //$info['headimg'] = $member['headimg'];
            $id = Db::name('yuyue_worker')->insertGetId($info);
            \app\common\System::plog('添加人员' . $id);
        }
        return json(['status' => 1, 'msg' => '操作成功', 'url' => (string)url('index')]);
    }

    //改状态
    public function setst()
    {
        $st = input('post.st/d');
        $ids = input('post.ids/a');
        Db::name('yuyue_worker')->where('aid', aid)->where('bid', bid)->where('id', 'in', $ids)->update(['status' => $st]);
        \app\common\System::plog('配送员改状态' . implode(',', $ids));
        return json(['status' => 1, 'msg' => '操作成功']);
    }

    //删除
    public function del()
    {
        $ids = input('post.ids/a');
        Db::name('yuyue_worker')->where('aid', aid)->where('bid', bid)->where('id', 'in', $ids)->delete();
        \app\common\System::plog('配送员删除' . implode(',', $ids));
        return json(['status' => 1, 'msg' => '删除成功']);
    }

    //获取配送员
    public function getpeisonguser()
    {
        $set = Db::name('yuyue_set')->where('aid', aid)->where('bid', bid)->find();

        $order = Db::name(input('param.type'))->where('id', input('param.orderid'))->find();
        if ($order['bid'] > 0) {
            $business = Db::name('business')->field('name,address,tel,logo,longitude,latitude')->where('id', $order['bid'])->find();
        } else {
            $business = Db::name('admin_set')->field('name,address,tel,logo,longitude,latitude')->where('aid', aid)->find();
        }
        $juli = getdistance($order['longitude'], $order['latitude'], $business['longitude'], $business['latitude'], 1);
        $ticheng = $order['paidan_money'];
        $selectArr = [];
        if ($set['paidantype'] == 0) { //抢单模式
            $selectArr[] = ['id' => 0, 'title' => '--服务人员抢单--'];
        } else {
            $peisonguser = Db::name('yuyue_worker')->where('aid', aid)->where('bid', $order['bid'])->where('status', 1)->order('sort desc,id')->select()->toArray();
            foreach ($peisonguser as $k => $v) {
                $dan = Db::name('yuyue_worker_order')->where('worker_id', $v['id'])->where('status', 'in', '0,1')->count();
                $title = $v['realname'] . '-' . $v['tel'] . '(进行中' . $dan . '单)';
                //查看是否在改时间已经有服务
                $order = Db::name('yuyue_order')->where('aid', aid)->where('worker_id', $v['id'])->where('status', 'in', '1,2')->where('yy_time', $order['yy_time'])->find();
                $status = 1;
                if ($order) {
                    $status = -1;
                }
                $selectArr[] = ['id' => $v['id'], 'title' => $title, 'status' => $status];
            }
        }

        $psfee = $ticheng * (1 + $set['businessfee'] * 0.01);
        return json(['status' => 1, 'peisonguser' => $selectArr, 'paidantype' => $set['paidantype'], 'psfee' => $psfee, 'ticheng' => $ticheng]);
    }

    //派单
    public function peisong()
    {
        $orderid = input('post.orderid/d');
        $worker_id = input('post.worker_id/d');
        $order = Db::name('yuyue_order')->where('id', $orderid)->where('aid', aid)->where('bid', bid)->find();
        if (!$order) return json(['status' => 0, 'msg' => '订单不存在']);
        if ($order['status'] != 1 && $order['status'] != 12) return json(['status' => 0, 'msg' => '订单状态不符合']);
        //取出该订单的服务人员
        $fwpeoid = Db::name('yuyue_product')->where('id', $order['proid'])->where('aid', aid)->where('bid', bid)->value('fwpeoid');

        $rs = \app\model\YuyueWorkerOrder::create($order, $worker_id, $fwpeoid);
        if ($rs['status'] == 0) return json($rs);
        \app\common\System::plog('预约派单' . $orderid);
        return json(['status' => 1, 'msg' => '操作成功']);
    }
}
