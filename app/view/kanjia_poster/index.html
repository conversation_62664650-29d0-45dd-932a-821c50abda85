<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title></title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  {include file="public/css"/}
	<link href="__STATIC__/imgsrc/designer.css" rel="stylesheet">
	<link rel="stylesheet" href="__STATIC__/admin/poster/jquery.contextMenu.css"/>
	<style type='text/css'>
	#editor-content{ height:602px;overflow:hidden}
	ul.right-menu{
		list-style:none;
		margin: 0 0 20px 0;
		padding: 0px;
		width: 100%;
		float:left;
	}
	ul.right-menu li{
		cursor:pointer;
		float:left;
		margin: 2px 5px;
		padding: 10px 18px;
		color:#428bca;
		font-size:14px;
	}
	ul.right-menu li.active{
		background-color: #428BCA;
		color: #fff;
	}

	#poster{width:344px;height:600px;border:0px solid #ccc;position:relative;overflow:hidden}
	#poster::-webkit-scrollbar { /*滚动条整体样式*/
		width: 2px;     /*高宽分别对应横竖滚动条的尺寸*/
		height: 0px;
	}
	#poster::-webkit-scrollbar-thumb { /*滚动条里面小方块*/
		border-radius: 1px;
		-webkit-box-shadow: inset 0 0 1px rgba(0,0,0,0.5);
		background: rgba(0,0,0,0.5);
	}
	#poster::-webkit-scrollbar-track { /*滚动条里面轨道*/
		-webkit-box-shadow: inset 0 0 0px rgba(0,0,0,0.2);
		border-radius: 0;
		background: rgba(0,0,0,0);
	}
	#poster .bg{position:absolute;width:100%;z-index:0;}
	#poster .drag[type=img] img,#poster .drag[type=thumb] img {width:100%;height:100%; }

	#poster .drag {position: absolute; width:80px;height:80px; border:1px solid rgba(0,0,0,0.5); }
		 
	#poster .drag[type=nickname]{ width:80px;height:40px; font-size:14px; line-height:14px; font-family: 黑体;}
	#poster .drag[type=text]{ width:auto;height:auto; font-size:14px; line-height:14px; font-family: 黑体;}
	#poster .drag[type=textarea]{overflow:hidden;font-size:14px; line-height:14px; font-family: 黑体;}
	#poster .drag[type=pro_img]{ background:#eee;overflow:hidden}
	#poster .drag img { position:absolute;z-index:0;width:100%; }
	#poster .drag .shadow {width:100%;height:100%}

	#poster .rRightDown,.rLeftDown,.rLeftUp,.rRightUp,.rRight,.rLeft,.rUp,.rDown{position:absolute;width:7px;height:7px;z-index:1;font-size:0;}    
	#poster .rRightDown,.rLeftDown,.rLeftUp,.rRightUp,.rRight,.rLeft,.rUp,.rDown{background:rgba(200,0,0,0.5);}
	.rLeftDown,.rRightUp{ cursor:ne-resize;}
	.rRightDown,.rLeftUp{ cursor:nw-resize;}
	.rRight,.rLeft{ cursor:e-resize;}
	.rUp,.rDown{ cursor:n-resize;}
	.rLeftDown{ left:-4px;bottom:-4px;}
	.rRightUp{ right:-4px;top:-4px;}
	.rRightDown{ right:-4px;bottom:-4px;}
	.rRightDown{ background-color:#00F;}   
				 
	.rLeftUp{ left:-4px;top:-4px;}
	.rRight{ right:-4px;top:50%;margin-top:-4px;}
	.rLeft{ left:-4px;top:50%;margin-top:-4px;}
	.rUp{ top:-4px;left:50%;margin-left:-4px;}
	.rDown{ bottom:-4px;left:50%;margin-left:-4px;}
	.context-menu-layer { z-index:9999;}
	.context-menu-list { z-index:9999;}
	</style>
</head>
<body>
  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
      <div class="layui-card layui-col-md12">
				<div class="layui-card-header">
					<div class="layui-tab layui-tab-brief">
						<ul class="layui-tab-title">
							<li {if $field=='kanjia'}class="layui-this"{/if} onclick="location.href='{:url()}/field/kanjia'"><i class="fa fa-cog"></i> 分享海报设置</li>
							<li {if $field=='kanjiajoin'}class="layui-this"{/if} onclick="location.href='{:url()}/field/kanjiajoin'"><i class="fa fa-cog"></i> 邀请帮砍海报设置</li>
						</ul>
					</div>
				</div>
				<div class="layui-card-body" pad15>
					{if count($platform) >1}
					<div class="layui-tab layui-tab-brief">
						<ul class="layui-tab-title">
							{foreach $platform as $pn}
							<li {if $type==$pn}class="layui-this"{/if} onclick="location.href='{:url()}/field/{$field}/type/{$pn}'">{:getplatformname($pn)}</li>
							{/foreach}
						</ul>
					</div>
					{/if}
					<table style='width:100%;margin-top:20px'>
						<tr>
							<td style='width:340px;padding:10px;' valign='top'>

					<div class="dsn-phone " style="float:right" >
						<div class="dsn-phone-left"></div>
						<div class="dsn-phone-center">
							<div class="dsn-phone-top"></div>
							<div class="dsn-phone-main">
								<div id="editor">
									<div class="dsn-mod dsn-topbar dsn-mod-nohover" style="color:#fff;background:#000;height:30px">
										<div style="float:left;width:100%;font-size:12px">
											<div style="float:left;width:30%">&nbsp;<i class="fa fa-signal"></i> wechat <i class="fa fa-wifi"></i></div>
											<div style="float:left;text-align:center;width:40%">12:00</div>
											<div style="float:left;text-align:right;width:30%">100% <i class="fa fa-battery-full"></i>&nbsp;</div>
										</div>
										<div style="float:left;width:98%;margin:2px 1% 0 1%;display:none">
											<div style="float:left;width:30%">&nbsp;</div>
											<div style="float:left;text-align:center;width:40%;font-size:16px;height:27px;line-height:27px">首页海报</div>
											<div style="float:right;width:30%;border-radius:20px;width:70px;height: 25px;border:1px solid rgba(255,255,255,0.2);text;text-align:center;overflow:hidden" ng-style="{'border-color':navigationBarTextStyle=='black'?'rgba(0,0,0,0.2)':'rgba(255,255,255,0.2)'}">
												<div style="float:left;width:49%;font-size:17px;height:27px;line-height:27px">
													<div style="float:left;margin-left:2px">&nbsp;&bull;</div>
													<div style="float:left;font-size:27px">&bull;</div>
													<div style="float:left;">&bull;</div>
												</div>
												<div style="float:left;width:2%;"><span style="border-right:1px solid;opacity:0.2;"></span></div>
												<div style="float:right;width:49%;"><i class="fa fa-dot-circle-o" style="font-size:19px;height:25px;line-height:25px"></i></div>
											</div>
										</div>
									</div>
									<div id="editor-content">


								<div id='poster'>
									{if !empty($poster_bg)}
									<img src="{$poster_bg}" class='bg'/>
									{/if}
									{if !empty($poster_data)}
									{foreach $poster_data as $key=>$d}
									<div class="drag" type="{$d['type']}" index="{$key+1}" style="zindex:{$key+1};left:{$d['left']};top:{$d['top']};{if $d['type']!='text'}width:{$d['width']};height:{$d['height']}{/if}" src="{$d['src']}" size="{$d['size']}" color="{$d['color']}" content="{$d['content']}"> 
										{if $d['type']=='qrwx'}
											<img src="__STATIC__/admin/poster/qrwx.jpg" />
										{elseif $d['type']=='qrbaidu'}
											<img src="__STATIC__/admin/poster/qrwx.jpg" />
										{elseif $d['type']=='qrtoutiao'}
											<img src="__STATIC__/admin/poster/qrwx.jpg" />
										{elseif $d['type']=='qralipay'}
											<img src="__STATIC__/admin/poster/qrwx.jpg" />
										{elseif $d['type']=='qrqq'}
											<img src="__STATIC__/admin/poster/qrwx.jpg" />
										{elseif $d['type']=='qrmp'}
											<img src="__STATIC__/admin/poster/qrmp.jpg" />
										{elseif $d['type']=='qrgz'}
											<img src="__STATIC__/admin/poster/qrmp.jpg" />
										{elseif $d['type']=='head'}
											<img src="__STATIC__/admin/poster/default-avatar.png" radius="{$d.radius}" style="border-radius:{$d['radius']/2}%"/>
										{elseif $d['type']=='nickname'}
											<div class=text style="font-size:{$d['size']};line-height:{$d['size']};color:{$d['color']}">[昵称]</div> 
										{elseif $d['type']=='text'}
											<div class=text style="font-size:{$d['size']};line-height:{$d['size']};color:{$d['color']}">{$d['content']}</div> 
										{elseif $d['type']=='textarea'}
											<div class=textarea style="font-size:{$d['size']};line-height:{$d['size']};color:{$d['color']}">{$d['content']}</div> 
										{elseif $d['type']=='img'}
											<img src="{$d['src']}" />
										{elseif $d['type']=='pro_img'}
											<img src="__STATIC__/admin/poster/product.jpg" />
										{/if}
										<div class="rRightDown"> </div><div class="rLeftDown"> </div><div class="rRightUp"> </div><div class="rLeftUp"> </div><div class="rRight"> </div><div class="rLeft"> </div><div class="rUp"> </div><div class="rDown"></div>
									</div>
									{/foreach} 
									{/if}
								</div>

								</div>
								</div>
							</div>
							<div class="dsn-phone-bottom"></div>
						</div>
						<div class="dsn-phone-right"></div>
					</div>

							</td>
							<td valign='top' style='padding:10px;padding-top:150px'>
								<form class="layui-form" lay-filter="">
										<div class="layui-form-item">
											<label class="layui-form-label">背景图片</label>
											<input type="hidden" id="poster_bg" class="layui-input" value="{$poster_bg}">
											<button style="float:left;" type="button" class="layui-btn layui-btn-primary" onclick="setpic('pic_poster_bg','poster_bg')">选择或上传</button>
											<div class="layui-form-mid layui-word-aux" style="margin-left:10px;">建议尺寸：宽680像素，高1300像素</div>
										</div>

										<div class="layui-form-item">
											<label class="layui-form-label">海报元素</label>
											<div class="layui-input-block">
												 <button class='layui-btn layui-btn-primary btn-com' type='button' data-type='head' style="margin-bottom:5px">头像</button>
												 <button class='layui-btn layui-btn-primary btn-com' type='button' data-type='text' style="margin-bottom:5px">文字</button>
												 
												 
												 <button class='layui-btn layui-btn-primary btn-com' type='button' data-type='img' style="margin-bottom:5px">图片</button>
												 <button class='layui-btn layui-btn-primary btn-com' type='button' data-type='pro_img' style="margin-bottom:5px">商品图片</button>
												 <button class='layui-btn layui-btn-primary btn-com' type='button' data-type='shadow' style="margin-bottom:5px">阴影区</button>
												 <button class='layui-btn layui-btn-primary btn-com' type='button' data-type='textarea' style="margin-bottom:5px">文本块</button>
											</div>
										</div>

										<div class="layui-form-item">
											<label class="layui-form-label">二维码元素</label>
											<div class="layui-input-block">
												<button class='layui-btn layui-btn-primary btn-com' type='button' data-type='qrmp' style="margin-bottom:5px">网页二维码</button>
												{if in_array('mp',$platform)}
												<!-- <button class='layui-btn layui-btn-primary btn-com' type='button' data-type='qrgz' style="margin-bottom:5px">关注二维码</button> -->
												{/if}
												{if in_array('wx',$platform)}
												<button class='layui-btn layui-btn-primary btn-com' type='button' data-type='qrwx' style="margin-bottom:5px">微信小程序码</button>
												{/if}
												<!-- {if in_array('alipay',$platform)}
												<button class='layui-btn layui-btn-primary btn-com' type='button' data-type='qralipay' style="margin-bottom:5px">支付宝小程序码</button>
												{/if}
												{if in_array('baidu',$platform)}
												<button class='layui-btn layui-btn-primary btn-com' type='button' data-type='qrbaidu' style="margin-bottom:5px">百度小程序码</button>
												{/if}
												{if in_array('toutiao',$platform)}
												<button class='layui-btn layui-btn-primary btn-com' type='button' data-type='qrtoutiao' style="margin-bottom:5px">头条小程序码</button>
												{/if}
												{if in_array('qq',$platform)}
												<button class='layui-btn layui-btn-primary btn-com' type='button' data-type='qrtoutiao' style="margin-bottom:5px">QQ小程序码</button>
												{/if} -->
											</div>
										</div>
										
										<div id='headset' style='display:none'>
											<div class="layui-form-item">
												<label class="layui-form-label">圆角度</label>
												<div class="layui-input-inline" style="width:200px">
													<div id="headslider" style="margin-top:18px;margin-left:5px"></div>
												</div>
												<div class="layui-form-mid" style="margin-left:10px;">0表示方形,100表示圆形</div>
											</div>
										</div>
										
										<div id='pro_imgset' style='display:none'>
											<!-- <div class="layui-form-item">
												<label class="layui-form-label">是否显示下方阴影区域</label>
												<div class="layui-input-inline">
													<select id='pro_imgshowyy' lay-ignore style="width:120px;;line-height:38px;height: 38px;border: 1px solid #e6e6e6;border-radius: 2px;">
														<option value='1'>显示</option>
														<option value='0'>不显示</option>
													</select>
												</div>
											</div> -->
										</div>
										<div id='textset' style='display:none'>
											<div class="layui-form-item">
												<label class="layui-form-label">文字内容</label>
												<div class="layui-input-inline" style="width:400px">
													<input type="text" value="" id="textcontent" class="layui-input">
												</div>
												<div class="layui-form-mid layui-word-aux" style="clear:both;margin-left:110px;">[昵称]、[姓名]、[手机号]、[商城名称]、[商品名称]、[商品销售价]、[商品市场价]会自动替换为当前用户的昵称、姓名、手机号等信息</div>
											</div>
											<div class="layui-form-item">
												<label class="layui-form-label">文字颜色</label>
												<div class="layui-input-inline">
													<input class="layui-input" id="textcolor" type="text" value="">
												</div>
												<div class="_colorpicker"></div>
											</div>
											<div class="layui-form-item">
												<label class="layui-form-label">文字大小</label>
												<div class="layui-input-inline">
													<input type="text" id="textsize" class="layui-input" placeholder="例如: 14,16"/>
												</div>
												<div class="layui-form-mid" style="margin-left:10px;">px</div>
											</div>
										</div>
										<div id='textareaset' style='display:none'>
											<div class="layui-form-item">
												<label class="layui-form-label">文本内容</label>
												<div class="layui-input-inline" style="width:400px">
													<input type="text" value="" id="textareacontent" class="layui-input">
												</div>
												<div class="layui-form-mid layui-word-aux" style="clear:both;margin-left:110px;">[昵称]、[姓名]、[手机号]、[商城名称]、[商品名称]、[商品销售价]、[商品市场价]会自动替换为当前用户的昵称、姓名、手机号等信息</div>
											</div>
											<div class="layui-form-item">
												<label class="layui-form-label">文本颜色</label>
												<div class="layui-input-inline">
													<input class="layui-input" id="textareacolor" type="text" value="">
												</div>
												<div class="_colorpicker"></div>
											</div>
											<div class="layui-form-item">
												<label class="layui-form-label">文本大小</label>
												<div class="layui-input-inline">
													<input type="text" id="textareasize" class="layui-input" placeholder="例如: 14,16"/>
												</div>
												<div class="layui-form-mid" style="margin-left:10px;">px</div>
											</div>
										</div>
										<div id="shadowset" style="display:none">
											<div class="layui-form-item">
												<label class="layui-form-label">颜色及透明度</label>
												<div class="layui-input-inline" style="width:200px">
													<input type="text" id="shadowcolor" value="rgba(0,0,0,0.5)" class="layui-input" />
												</div>
												<div id="shadowcolorpicker"></div>
											</div>
										</div>
										<div id='imgset' style='display:none'>
											<div class="layui-form-item">
												<label class="layui-form-label">图片：</label>
												<input type="hidden" id="imgurl" value="">
												<button style="float:left;" type="button" class="layui-btn layui-btn-primary" onclick="setpic2('img_pic','imgurl')">上传图片</button>
												<div style="float:left;padding-top:10px;margin-left:110px;clear: both;">
													<div class="layui-imgbox" style="width:100px;"><div class="layui-imgbox-img"><img id="img_pic" src=""/></div></div>
												</div>
											</div>
										</div>
										
										<div class="layui-form-item" style="margin-top:50px;display:none">
											<label class="layui-form-label">清除之前<br>海报缓存</label>
											<div class="layui-input-inline">
												<input type="radio" name="clearhistory" title="是" value="1" checked/>
												<input type="radio" name="clearhistory" title="否" value="0"/>
											</div>
										</div>
										<div class="layui-form-item">
											<div class="layui-input-block" style="margin-left:130px;">
												<button class="layui-btn layui-btn-normal layui-btn-lg" lay-submit lay-filter="formsubmit">保 存</button>
											</div>
										</div>
									</form>
										
							</td>
						</tr>
					</table>
					<input type="hidden" name="poster_data" value="" />
				</div>
			</div>
		</div>
  </div>
	
	{include file="public/js"/}
	<script src="__STATIC__/admin/js/jscolor.js"></script>
	<script language='javascript' src="__STATIC__/admin/poster/designer.js"></script>
	<script language='javascript' src="__STATIC__/admin/poster/jquery.contextMenu.js"></script>
	<script language='javascript'>
	var headsliderValue = 0;
  var headslider = layui.slider.render({
    elem: '#headslider',  //绑定元素
		change: function(value){
			headsliderValue = value
		}
  });
	//var shadowcolorValue = 'rgba(0,0,0,0.5)'
	function shadowcolor(){
		layui.colorpicker.render({
			elem: '#shadowcolorpicker',
			format:'rgb',
			alpha: true,
			color:$('#shadowcolor').val(),
			predefine: true,
			colors: ['#ff4444','#e64340','#ec8b89','#ed3f14','#ff9900',
				'#06bf04','#179b16','#9ed99d','#19be6b',
				'#3388ff','#2b85e4','#5cadff',
				'#000000','#333333','#666666','#999999','#c9c9c9','#f7f7f8','#1c2438','#495060','#dddee1','#e9eaec'],
			change:function(color){
				//shadowcolorValue = color;
				$('#shadowcolor').val(color)
			}
		});
	}
	
	//上传图片
	function setpic(imgid,hideid){
		fileUploader.show(function(imgs) {
			var params = '';
			console.log(imgs);
			if (imgs.length == 0) {
					return;
			} else {
				$('#'+imgid).attr('src',imgs.url + params);
				$('#'+hideid).val(imgs.url + params);

				var bg = $(':input[name=bg]').val();
				$('#poster .bg').remove();
				var bgh = $("<img src='"+imgs.url + params +"' class='bg' />");
				var first = $('#poster .drag:first');
				if(first.length>0){
					bgh.insertBefore(first);  
				} else{
					$('#poster').append(bgh);      
				}
			}
		},{type: 'image',direct: false,multi: false});
	}
	//上传图片
	function setpic2(imgid,hideid){
		fileUploader.show(function(imgs) {
			console.log(imgs);
			if (imgs.length == 0) {
					return;
			} else {
				$('#'+imgid).attr('src',imgs.url);
				$('#'+hideid).val(imgs.url);
			}
		},{type: 'image',direct: false,multi: false});
	}

	function chushihua(){
		if(confirm('初始化后您设计的海报将被清除,还原到初始状态,确定要初始化吗')){
			$.post("{:url('chushihua')}",{},function(data){
				dialog(data.msg)
				location.href = location.href + (location.href.indexOf('?') > -1 ? '&' : '?') + 't={time()}';
			})
		}
	}
  layui.form.on('submit(formsubmit)', function(formobj){
		console.log(formobj);
			var data = [];
			$('.drag').each(function(){
					var obj = $(this);
					var type = obj.attr('type');
					var left = obj.css('left'),top = obj.css('top');
					var d= { left:left,top:top,type:obj.attr('type'),width:obj.css('width'),height:obj.css('height')};
					if(type=='qrwx'|| type=='qrmp'|| type=='qrgz'|| type=='qrtoutiao'|| type=='qrbaidu' || type=='qralipay' || type=='qrqq'){
							d.size = obj.attr('size');
					} else if(type=='img'){
							d.src = obj.attr('src');
					} else if(type=='text'){
							d.size = obj.attr('size');
							d.color = obj.attr('color');
							d.content = obj.attr('content');
					} else if(type=='textarea'){
							d.size = obj.attr('size');
							d.color = obj.attr('color');
							d.content = obj.attr('content');
					} else if(type=='pro_img'){
							d.showyy = obj.attr('showyy');
					} else if(type=='shadow'){
							d.shadow = obj.find('.shadow').attr('shadow');
					} else if(type=='head'){
							d.radius = obj.find('img').attr('radius');
					}
					data.push(d);
			});
			console.log(formobj);
			var type = '{$type}';
			var field = '{$field}';
			var poster_bg = $('#poster_bg').val();
			var poster_data = JSON.stringify(data);
			var clearhistory = formobj.field.clearhistory
			$.post("{:url('save')}",{type:type,field:field,poster_bg:poster_bg,poster_data:poster_data,clearhistory:clearhistory},function(data){
				dialog(data.msg,data.status,data.url);
			})
			return false;
	});
        
	function bindEvents(obj){
		var index = obj.attr('index');
		var type = obj.attr('type');
		if(type!='text'){
			var rs = new Resize(obj, { Max: true, mxContainer: "#poster" });
			rs.Set($(".rRightDown",obj), "right-down");
			rs.Set($(".rLeftDown",obj), "left-down");
			rs.Set($(".rRightUp",obj), "right-up");
			rs.Set($(".rLeftUp",obj), "left-up");
			rs.Set($(".rRight",obj), "right");
			rs.Set($(".rLeft",obj), "left");
			rs.Set($(".rUp",obj), "up");
			rs.Set($(".rDown",obj), "down"); 
			rs.Scale = false;
			if(type=='head' || type=='qrwx' || type=='qrmp' || type=='qrgz' || type=='qrtoutiao'|| type=='qrbaidu' || type=='qralipay' || type=='qrqq'){
				rs.Scale = true;
			}
		}
		new Drag(obj, { Limit: true, mxContainer: "#poster" });
		$('.drag .remove').unbind('click').click(function(){
			$(this).parent().remove();
		});
    $.contextMenu({
			selector:'.drag[index=' + index + ']',
        callback: function(key, options) {
				var index = parseInt($(this).attr('zindex'));
        if(key=='next'){
					var nextdiv = $(this).next('.drag');
					if(nextdiv.length>0 ){
					   nextdiv.insertBefore($(this));  
					}
				} else if(key=='prev'){
					var prevdiv = $(this).prev('.drag');
					if(prevdiv.length>0 ){
					   $(this).insertBefore(prevdiv);  
					} 
				} else if(key=='last'){
					var len = $('.drag').length;
					 if(index >=len-1){
						return;
					} 
					var last = $('#poster .drag:last');
					if(last.length>0){
					   $(this).insertAfter(last);  
					}
				}else if(key=='first'){
					var index = $(this).index();
					if(index<=1){
						return;
					}
					var first = $('#poster .drag:first');
					if(first.length>0){
					   $(this).insertBefore(first);  
					}
				}else if(key=='delete'){
				   $(this).remove();
				}
				var n =1 ;
				$('.drag').each(function(){
					$(this).css("z-index",n);
					n++; 
				})
			},
			items: {
				"next": { name: "调整到上层"},
				"prev": { name: "调整到下层"},
				"last": { name: "调整到最顶层"},
				"first": { name: "调整到最低层"},
				"delete": { name: "删除元素"}
			}
		});
		obj.unbind('click').click(function(){
			bind($(this));
		});    
	}
	var imgsettimer = 0 ;
	var nametimer = 0;
	var bgtimer = 0 ;
	function clearTimers(){
		clearInterval(imgsettimer);
		clearInterval(nametimer);
		clearInterval(bgtimer);
	}
  function bind(obj){
		var imgset = $('#imgset'),qrwxset = $('#qrwxset'),qrmpset = $('#qrmpset'),qrgzset = $('#qrgzset'),qralipayset = $('#qralipayset'),qrbaiduset = $('#qrbaiduset'),qrtoutiaoset = $('#qrtoutiaoset'),qrqqset = $('#qrqqset'),textset = $('#textset'),textareaset = $('#textareaset'),pro_imgset=$('#pro_imgset'),shadowset=$('#shadowset'),headset=$('#headset');
    imgset.hide(),qrwxset.hide(),qrmpset.hide(),qrgzset.hide(),textset.hide(),textareaset.hide(),pro_imgset.hide(),shadowset.hide(),headset.hide();
    clearTimers();
    var type = obj.attr('type');
    if(type=='img'){
			imgset.show();
			var src = obj.attr('src');
			var input = imgset.find('input');
			var img = imgset.find('img');
			if(typeof(src)!='undefined' && src!=''){
				input.val(src); 
        img.attr('src',src);
			}     
			imgsettimer = setInterval(function(){
			   if(input.val()!=src && input.val()!=''){
				   var url = input.val();
				   obj.attr('src',input.val()).find('img').attr('src',url);
			   }
			},10);       
		} else if(type=='qr'){
			/*qrset.show();
			var size = obj.attr('size') || "3";
			var sel = qrset.find('#qrsize');
			sel.val(size);
			sel.unbind('change').change(function(){
			  obj.attr('size',sel.val()) 
			});*/
		} else if(type=='pro_img'){
			pro_imgset.show();
			var showyy = obj.attr('showyy') || "1";
			var sel = pro_imgset.find('#pro_imgshowyy');
			sel.val(showyy);
			sel.unbind('change').change(function(){
			  obj.attr('showyy',sel.val()) 
			});
		}  else if(type=='text'){
			textset.show();
			var content = obj.attr('content') || "[昵称]";
			var color = obj.attr('color') || "#000";
			var size = obj.attr('size') || "16";
			var textcontent = textset.find('#textcontent');
			var textcolor = textset.find('#textcolor');
			var textsize = textset.find('#textsize');
			var picker = textset.find('.sp-preview-inner');
			textcontent.val(content); 
			textcolor.val(color); 
			textsize.val(size.replace("px",""));  
			picker.find('.text').text( content );
			picker.css( { 'background-color':color,'font-size':size});
			initcolorpicker();
			nametimer = setInterval(function(){
			   obj.attr('content',textcontent.val()).find('.text').text(textcontent.val());
			   obj.attr('color',textcolor.val()).find('.text').css('color',textcolor.val());
			   obj.attr('size',textsize.val() +"px").find('.text').css({'font-size':textsize.val() +"px",'line-height':textsize.val() +"px"});
			},10);    
		} else if(type=='textarea'){
			textareaset.show();
			var content = obj.attr('content') || "文本内容";
			var color = obj.attr('color') || "#000";
			var size = obj.attr('size') || "16";
			var textareacontent = textareaset.find('#textareacontent');
			var textareacolor = textareaset.find('#textareacolor');
			var textareasize = textareaset.find('#textareasize');
			var picker = textareaset.find('.sp-preview-inner');
			textareacontent.val(content); 
			textareacolor.val(color); 
			textareasize.val(size.replace("px",""));  
			picker.find('.textarea').text( content );
			picker.css( { 'background-color':color,'font-size':size});
			initcolorpicker();
			nametimer = setInterval(function(){
			   obj.attr('content',textareacontent.val()).find('.textarea').text(textareacontent.val());
			   obj.attr('color',textareacolor.val()).find('.textarea').css('color',textareacolor.val());
			   obj.attr('size',textareasize.val() +"px").find('.textarea').css({'font-size':textareasize.val() +"px",'line-height':textareasize.val() +"px"});
			},10);    
		}else if(type=='shadow'){
			shadowset.show();
			var shadowcolorValue = obj.find('.shadow').attr('shadow') || "rgba(0,0,0,0.5)";
			$('#shadowcolor').val(shadowcolorValue)
			shadowcolor();
			nametimer = setInterval(function(){
			  obj.find('.shadow').attr('shadow',$('#shadowcolor').val());
				obj.find('.shadow').css('background',$('#shadowcolor').val());
			},10);
		}else if(type=='head'){
			headset.show();
			headsliderValue = obj.find('img').attr('radius') || 0;
			headslider.setValue(headsliderValue);
			nametimer = setInterval(function(){
			  obj.find('img').attr('radius',headsliderValue);
				obj.find('img').css('border-radius',headsliderValue/2+'%');
			},10);
		}
	}
         
  $(function(){                 
		$('.drag').each(function(){
			bindEvents($(this));
		})        
		$('.btn-com').click(function(){
			$('#imgset').hide()
			$('#qrwxset').hide()
			$('#qrmpset').hide()
			$('#qrgzset').hide()
			$('#qrbaiudset').hide()
			$('#qralipayset').hide()
			$('#qrtoutiaoset').hide()
			$('#qrqqset').hide()
			$('#textset').hide();
			$('#textareaset').hide();
			clearTimers();
			if($('#poster img').length<=0){
				//alert('请选择背景图片!');
				//return;
			}
			var type = $(this).data('type');
			var img = "";
			if(type=='qrwx'){
				img = '<img src="__STATIC__/admin/poster/qrwx.jpg" />';
			}else if(type=='qrmp'){
				img = '<img src="__STATIC__/admin/poster/qrmp.jpg" />';
			}else if(type=='qrgz'){
				img = '<img src="__STATIC__/admin/poster/qrmp.jpg" />';
			}else if(type=='qrbaidu'){
				img = '<img src="__STATIC__/admin/poster/qrwx.jpg" />';
			}else if(type=='qralipay'){
				img = '<img src="__STATIC__/admin/poster/qrwx.jpg" />';
			}else if(type=='qrtoutiao'){
				img = '<img src="__STATIC__/admin/poster/qrwx.jpg" />';
			}else if(type=='qrqq'){
				img = '<img src="__STATIC__/admin/poster/qrwx.jpg" />';
			}else if(type=='head'){
				img = '<img src="__STATIC__/admin/poster/default-avatar.png" />';
			}else  if(type=='img'){
				img = '<img src="__STATIC__/admin/poster/img.png" />';
			}else if(type=='pro_img'){
				img = '<img src="__STATIC__/admin/poster/product.jpg" />';
			}else if(type=='text'){
				img = '<div class=text>[昵称]</div>';
			}else if(type=='textarea'){
				img = '<div class=textarea>文本内容</div>';
			}else if(type=='shadow'){
				img = '<div class="shadow" shadow="rgba(0,0,0,0.5)" style="background:rgba(0,0,0,0.5)"></div>';
			}
			var index = $('#poster .drag').length+1;
			var obj = $('<div class="drag" type="' + type +'" index="' + index +'" style="z-index:' + index+'">' + img+'<div class="rRightDown"> </div><div class="rLeftDown"> </div><div class="rRightUp"> </div><div class="rLeftUp"> </div><div class="rRight"> </div><div class="rLeft"> </div><div class="rUp"> </div><div class="rDown"></div></div>');
			$('#poster').append(obj);
			bindEvents(obj);
		});
		$('.drag').click(function(){
			bind($(this));
		})
	})
</script>
	{include file="public/copyright"/}
</body>
</html>