<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>商品管理</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  {include file="public/css"/}
</head>
<body>
  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-card layui-col-md12">
          <div class="layui-card-header">商品管理</div>
          <div class="layui-card-body" pad15>
						<div class="layui-col-md4" style="padding-bottom:10px">
							{if input('param.showtype')!=2}<a class="layui-btn layuiadmin-btn-list" href="javascript:void(0)" onclick="openmax('{:url('edit')}')">添加</a>{/if}
							<button class="layui-btn layui-btn-primary layuiadmin-btn-list" onclick="datadel(0)">删除</button>
							<button class="layui-btn layui-btn-primary layuiadmin-btn-list" onclick="setst(0,1)">上架</button>
							<button class="layui-btn layui-btn-primary layuiadmin-btn-list" onclick="setst(0,0)">下架</button>
						</div>
						<div class="layui-form layui-col-md8" style="text-align:right;padding-bottom:10px">
							<div class="layui-inline layuiadmin-input-useradmin">
								<label class="layui-form-label" style="width:60px">商品名称</label>
								<div class="layui-input-block" style="width:120px;margin-left:90px">
									<input type="text" name="name" autocomplete="off" class="layui-input">
								</div>
							</div>
							<div class="layui-inline">
								<label class="layui-form-label" style="width:30px">状态</label>
								<div class="layui-input-block" style="width:120px;margin-left:60px;text-align:left">
									<select name="status">
										<option value="">全部</option>
										<option value="1">已上架</option>
										<option value="0">未上架</option>
									</select>
								</div>
							</div>
							<div class="layui-inline">
								<button class="layui-btn layuiadmin-btn-replys" lay-submit="" lay-filter="LAY-app-forumreply-search">
									<i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>
								</button>
							</div>
						</div>
						<div class="layui-col-md12">
							<table id="tabledata" lay-filter="tabledata"></table>
						</div>
          </div>
        </div>
    </div>
  </div>
	{include file="public/js"/}
	<script>
  var table = layui.table;
	var datawhere = {};
  //数据表
  var tableIns = table.render({
    elem: '#tabledata'
    ,url: "{$Request.url}" //数据接口
    ,page: true //开启分页
    ,cols: [[ //表头
			{type:"checkbox"},
      {field: 'id', title: 'ID',  sort: true,width:80},
			{if input('param.showtype')==2}
      {field: 'bname', title: '所属商户',width:160},
			{/if}
      {field: 'name', title: '商品信息',width:320,templet:function(d){
				var html = '';
				html+='<img src="'+d.pic+'" style="max-width:70px;float:left"/>';
				html+='<div style="float: left;width:200px;margin-left: 10px;white-space:normal;line-height:20px;">';
				html+='	<div style="width:100%;">'+d.name+'</div>';
				html+='	<div style="color:#666;font-size:12px">原价: ￥'+d.sell_price+'</div>';
				html+='	<div style="color:#f60;font-size:12px">最低价: ￥'+d.min_price+'</div>';
				html+='</div>';
				return html;
			}},
      {field: 'stock', title: '库存',width:100},
      {field: 'sales', title: '已成功',sort: true,width:100},
      {field: 'saleing', title: '砍价中',sort: true,width:100},
      {field: 'sort', title: '活动时间',templet:function(d){ return date('Y-m-d H:i',d.starttime) + ' ~ '+date('Y-m-d H:i',d.endtime)},width:250},
      {field: 'status', title: '状态',templet:function(d){ 
				if(d.status==0){
					return '<span style="color:red">未上架</span>';
				}else{
					if(d.starttime > {:time()}){
						return '<button class="layui-btn layui-btn-sm" style="background-color:#888">未开始</button>';
					}else if(d.endtime < {:time()}){
						return '<button class="layui-btn layui-btn-sm" style="background-color:#ccc">已结束</button>';
					}else{
						return '<button class="layui-btn layui-btn-sm" style="background-color:#5FB878">进行中</button>';
					}
				}
			}},
			{if input('param.showtype')==2 || $bid!=0}
			{field: 'status', title: '审核状态',templet:function(d){ 
				if(d.ischecked==0) return '<span style="color:blue">待审核</span>';
				if(d.ischecked==1) return '<span style="color:green">已通过</span>';
				if(d.ischecked==2) return '<span style="color:red">已驳回</span>';
			},width:100},
			{/if}
			{field: 'operation', title: '操作',templet:function(d){
				var html = '';
				{if input('param.showtype')!=2}
				html += '<button class="table-btn" onclick="openmax(\'{:url('joinlist')}/proid/'+d.id+'\')">参与列表</button>';
				{/if}
				html += '<button class="table-btn" onclick="openmax(\'{:url('edit')}/id/'+d.id+'\')">编辑</button>';
				html += '<button class="table-btn" onclick="datadel('+d.id+')">删除</button><br>';
				
				if(d.freighttype == 4){
					html += '<br><button class="table-btn" onclick="openmax(\'{:url('KanjiaCode/codelist')}/proid/'+d.id+'/isopen/1\')">卡密信息</button>';
				}
				if('{$bid}'==0 && d.bid>0){
					if(d.ischecked == 0 || d.ischecked == 2){
						html += '<button class="table-btn" onclick="setcheckst(\''+d.id+'\',1)">通过</button>';
					}
					if(d.ischecked == 0 || d.ischecked == 1){
						html += '<button class="table-btn" onclick="setcheckst(\''+d.id+'\',2)">驳回</button>';
					}
				}
				return html;
      },width:220}
    ]]
  });
	//排序
	table.on('sort(tabledata)', function(obj){
		datawhere.field = obj.field;
		datawhere.order = obj.type;
		tableIns.reload({
			initSort: obj,
			where: datawhere
		});
	});
	//检索
	layui.form.on('submit(LAY-app-forumreply-search)', function(obj){
		var field = obj.field
		var olddatawhere = datawhere
		datawhere = field
		datawhere.field = olddatawhere.field
		datawhere.order = olddatawhere.order
		tableIns.reload({
			where: datawhere,
			page: {curr: 1}
		});
	})
	
	//审核
	function setcheckst(id,st){
		if(st == 2){
			var html = '';
			html+='	<div class="layui-form-item" style="margin-top:40px;margin-right:20px;">';
			html+='		<label class="layui-form-label" style="width:80px">驳回原因</label>';
			html+='		<div class="layui-input-inline" style="width:350px">';
			html+='			<textarea type="text" id="check_reason" class="layui-textarea"></textarea>';
			html+='		</div>';
			html+='	</div>';
			var checkLayer = layer.open({type:1,area:['500px','250px'],title:false,content:html,shadeClose:true,btn: ['确定', '取消'],
				yes:function(){
					var index = layer.load();
					$.post("{:url('setcheckst')}",{id:id,st:st,reason:$('#check_reason').val()},function(res){
						layer.close(index);
						dialog(res.msg,res.status);
						layer.close(checkLayer);
						tableIns.reload()
					})
				}
			})
		}else{
			layer.confirm('确定要审核通过吗?',{icon: 7, title:'操作确认'}, function(index){
				layer.close(index);
				var index = layer.load();
				$.post("{:url('setcheckst')}",{id:id,st:st},function(data){
					layer.close(index);
					dialog(data.msg,data.status);
					tableIns.reload()
				})
			});
		}
	}
	//删除
	function datadel(id){
		var ids = [];
		if(id==0){
			var checkStatus = table.checkStatus('tabledata')
			var checkData = checkStatus.data; //得到选中的数据
			if(checkData.length === 0){
				 return layer.msg('请选择数据');
			}
			for(var i=0;i<checkData.length;i++){
				ids.push(checkData[i]['id']);
			}
		}else{
			ids.push(id);
		}
		layer.confirm('确定要删除吗?',{icon: 7, title:'操作确认'}, function(index){
			//do something
			layer.close(index);
			var index = layer.load();
			$.post("{:url('del')}",{ids:ids},function(data){
				layer.close(index);
				dialog(data.msg,data.status);
				tableIns.reload()
			})
		});
	}
	//上下架
	function setst(id,st){
		var ids = [];
		if(id==0){
			var checkStatus = table.checkStatus('tabledata')
			var checkData = checkStatus.data; //得到选中的数据
			if(checkData.length === 0){
				 return layer.msg('请选择数据');
			}
			for(var i=0;i<checkData.length;i++){
				ids.push(checkData[i]['id']);
			}
		}else{
			ids.push(id);
		}
		layer.confirm('确定要'+(st==0?'下架':'上架')+'吗?',{icon: 7, title:'操作确认'}, function(index){
			//do something
			layer.close(index);
			var index = layer.load();
			$.post("{:url('setst')}",{ids:ids,st:st},function(data){
				layer.close(index);
				dialog(data.msg,data.status);
				tableIns.reload()
			})
		});
	}
	</script>
	{include file="public/copyright"/}
</body>
</html>