<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>参与列表</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  {include file="public/css"/}
</head>
<body>
  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-card layui-col-md12">
          <div class="layui-card-header">
						参与列表
						<i class="layui-icon layui-icon-close" style="font-size:18px;font-weight:bold;cursor:pointer" onclick="closeself()"></i>
					</div>
          <div class="layui-card-body" pad15>
						<!-- <div class="layui-col-md4" style="padding-bottom:10px">
							<a class="layui-btn layuiadmin-btn-list" href="javascript:void(0)" onclick="openedit()">添加</a>
							<button class="layui-btn layui-btn-primary layuiadmin-btn-list" onclick="datadel(0)">删除</button>
							<button class="layui-btn layui-btn-primary layuiadmin-btn-list" onclick="setst(0,1)">上架</button>
							<button class="layui-btn layui-btn-primary layuiadmin-btn-list" onclick="setst(0,0)">下架</button>
						</div> -->
						<div class="layui-form layui-col-md12" style="text-align:right;padding-bottom:10px">
							<div class="layui-inline">
								<label class="layui-form-label" style="width:30px">状态</label>
								<div class="layui-input-block" style="width:120px;margin-left:60px;text-align:left">
									<select name="status">
										<option value="">全部</option>
										<option value="0">进行中</option>
										<option value="1">已成功</option>
										<option value="2">已失败</option>
									</select>
								</div>
							</div>
							<div class="layui-inline">
								<button class="layui-btn layuiadmin-btn-replys" lay-submit="" lay-filter="LAY-app-forumreply-search">
									<i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>
								</button>
							</div>
						</div>
						<div class="layui-col-md12">
							<table id="tabledata" lay-filter="tabledata"></table>
						</div>
          </div>
        </div>
    </div>
  </div>
	{include file="public/js"/}
	<script>
  var table = layui.table;
	var datawhere = {};
  //数据表
  var tableIns = table.render({
    elem: '#tabledata'
    ,url: '{$Request.url}' //数据接口
    ,page: true //开启分页
    ,cols: [[ //表头
			{type:"checkbox"},
      {field: 'id', title: 'ID',  sort: true,width:80},
      {field: 'nickname', title: '头像昵称',templet: '<div><img src="{{d.headimg}}" style="width:50px;height:50px;border-radius:50%"> {{d.nickname}}</div>'},
      {field: 'now_price', title: '当前价格',sort: true,templet: '<div>￥{{d.now_price}}</div>'},
      {field: 'helpnum', title: '帮砍人数',sort: true,width:100},
      {field: 'createtime', title: '参与时间',sort: true,templet:function(d){ return date('Y-m-d H:i',d.createtime)}},
      {field: 'endtime', title: '完成时间',sort: true,templet:function(d){ return date('Y-m-d H:i',d.endtime)}},
      {field: 'status', title: '状态',templet:function(d){ 
				if(d.status==0){
						return '<button class="layui-btn layui-btn-sm" style="background-color:#f55">进行中</button>';
				}else if(d.status==1){
					return '<button class="layui-btn layui-btn-sm" style="background-color:#3e5">已完成</button>';
				}else if(d.status==2){
					return '<button class="layui-btn layui-btn-sm" style="background-color:#888">已失败</button>';
				}
			}},
      {field: 'operation', title: '操作',templet: '<div><button class="table-btn" onclick="openhelplist({{d.id}})">帮砍记录</button></div>'}
    ]]
  });
	//排序
	table.on('sort(tabledata)', function(obj){
		datawhere.field = obj.field;
		datawhere.order = obj.type;
		tableIns.reload({
			initSort: obj,
			where: datawhere
		});
	});
	//检索
	layui.form.on('submit(LAY-app-forumreply-search)', function(obj){
		var field = obj.field
		var olddatawhere = datawhere
		datawhere = field
		datawhere.field = olddatawhere.field
		datawhere.order = olddatawhere.order
		tableIns.reload({
			where: datawhere,
			page: {curr: 1}
		});
	})
	//删除
	function datadel(id){
		var ids = [];
		if(id==0){
			var checkStatus = table.checkStatus('tabledata')
			var checkData = checkStatus.data; //得到选中的数据
			if(checkData.length === 0){
				 return layer.msg('请选择数据');
			}
			for(var i=0;i<checkData.length;i++){
				ids.push(checkData[i]['id']);
			}
		}else{
			ids.push(id);
		}
		layer.confirm('确定要删除吗?',{icon: 7, title:'操作确认'}, function(index){
			//do something
			layer.close(index);
			var index = layer.load();
			$.post('',{op:'joinlistdel',ids:ids},function(data){
				layer.close(index);
				dialog(data.msg,data.status);
				tableIns.reload()
			})
		});
	}
	function openhelplist(id){
		var index = layer.load();
		$.post("{:url('gethelplist')}",{id:id},function(data){
			layer.close(index);
			layui.laytpl(helplistTpl.innerHTML).render(data, function(html){
				var logisticsLayer = layer.open({type:1,title:'帮砍记录',content:html,area:"800px",shadeClose:true,btn: ['关闭']})
			})
		});
	}
	</script>
	<script id="helplistTpl" type="text/html">
	<div style="margin:10px">
	<table class="layui-table">
	<thead>
    <tr>
      <th>头像/昵称</th>
      <th>砍价时间</th>
      <th>砍掉价格</th>
      <th>砍后价格</th>
    </tr> 
  </thead>
  <tbody>
		{{# $.each(d.helplist,function(index,item){ }}
    <tr>
      <td><img src="{{item.headimg}}" style="width:50px;height:50px;border-radius:50%"> {{item.nickname}}</td>
      <td>{{date('Y-m-d H:i',item.createtime)}}</td>
      <td>￥{{item.cut_price}}</td>
      <td>{{item.after_price}}</td>
    </tr>
		{{# }); }}
  </tbody>
	</table>
	</div>
	</script>
	{include file="public/copyright"/}
</body>
</html>