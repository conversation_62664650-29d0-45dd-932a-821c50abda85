<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>系统设置</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  {include file="public/css"/}
</head>
<body>
  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
      <div class="layui-card layui-col-md12">
				<div class="layui-card-header"><i class="fa fa-cog"></i> 系统设置</div>
				<div class="layui-card-body" pad15>
					<div class="layui-form" lay-filter="">
						<!-- <div class="layui-form-item">
							<label class="layui-form-label" style="width:100px">商城标题：</label>
							<div class="layui-input-inline">
								<input type="text" name="info[name]" lay-verify="required" lay-verType="tips" class="layui-input" value="{$info['name']}">
							</div>
						</div> -->
						<div class="layui-form-item">
							<label class="layui-form-label" style="width:100px">banner图：</label>
							<input type="hidden" name="info[pic]" id="pic" class="layui-input" value="{$info['pic']}">
							<button style="float:left;" type="button" class="layui-btn layui-btn-primary" upload-input="pic" upload-preview="logoPreview" onclick="uploader(this)">上传图片</button>
							<div class="layui-form-mid layui-word-aux" style="margin-left:10px;">建议尺寸：750×430像素</div>
							<div id="logoPreview" style="float:left;padding-top:10px;margin-left:130px;clear: both;">
								<div class="layui-imgbox" style="width:100px;"><div class="layui-imgbox-img"><img src="{$info['pic']}"/></div></div>
							</div>
						</div>
						<!-- <div class="layui-form-item">
							<label class="layui-form-label" style="width:100px">轮播图：</label>
							<input type="hidden" name="info[pics]" value="{$info.pics}" id="pics">
							<button style="float:left;" type="button" class="layui-btn layui-btn-primary" onclick="uploader(this,true)" upload-input="pics" upload-preview="picList" >批量上传</button>
							<div class="layui-form-mid layui-word-aux" style="margin-left:10px;">建议尺寸：640×320像素</div>
							<div id="picList" style="float:left;padding-top:10px;padding-left:130px;clear: both;">
								{if $info['pics']}
								{php}$pics = explode(',',$info['pics']);{/php}
								{foreach $pics as $pic}
								<div class="layui-imgbox">
									<a class="layui-imgbox-close" href="javascript:void(0)" onclick="$(this).parent().remove();getpicsval('pics','picList')" title="删除"><i class="layui-icon layui-icon-close-fill"></i></a>
									<span class="layui-imgbox-img"><img src="{$pic}"></span>
								</div>
								{/foreach}{/if}
							</div>
						</div> -->
						<div class="layui-form-item">
							<label class="layui-form-label" style="width:100px">自动收货天数：</label>
							<div class="layui-input-inline">
								<input type="text" name="info[autoshdays]" lay-verify="required" lay-verType="tips" class="layui-input" value="{$info.autoshdays}">
							</div>
							<div class="layui-form-mid">天</div>
							<div class="layui-form-mid layui-word-aux" style="margin-left:10px;">发货后多少天用户未确认收货自动完成收货</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label" style="width:100px">进店及咨询：</label>
							<div class="layui-input-inline">
								<input type="radio" name="info[showjd]" value="1" {if $info['showjd']==1}checked{/if} title="是"/>
								<input type="radio" name="info[showjd]" value="0" {if $info['showjd']==0}checked{/if} title="否"/>
							</div>
							<div class="layui-form-mid layui-word-aux" style="margin-left:10px;">商品详情页是否显示进店及咨询</div>
						</div>
						
						<div class="layui-form-item">
							<div class="layui-input-block" style="margin-left:130px;">
								<button class="layui-btn" lay-submit lay-filter="formsubmit">提 交</button>
							</div>
						</div>
					</div>
				</div>
			</div>
    </div>
  </div>
	{include file="public/js"/}
	<script>
	layui.form.on('radio(freightfree)', function(data){
		if(data.value == '1'){
			$('#freightfreemoneyset').show();
		}else{
			$('#freightfreemoneyset').hide();
		}
	})
	layui.form.on('radio(comment)', function(data){
		if(data.value == '1'){
			$('#commentset').show();
		}else{
			$('#commentset').hide();
		}
	})
	layui.form.on('submit(formsubmit)', function(obj){
		var field = obj.field
		if(field['info[logo]'] == ''){
			dialog('请上传商家LOGO',0);return;
		}
		$.post("{:url('save')}",field,function(data){
			dialog(data.msg,data.status,data.url);
		})
	})
  </script>
	{include file="public/copyright"/}
</body>
</html>