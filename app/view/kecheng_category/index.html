<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>课程类型</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  {include file="public/css"/}
	<style>
	#urlqr img{margin:0 auto}
	</style>
</head>
<body>
  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-card layui-col-md12">
          <div class="layui-card-header">课程类型</div>
          <div class="layui-card-body" pad15>
						<div class="layui-col-md4" style="padding-bottom:10px">
							<a class="layui-btn layuiadmin-btn-list" href="javascript:void(0)" onclick="openmax('{:url('edit')}')">添加</a>
							<button class="layui-btn layui-btn-primary layuiadmin-btn-list" onclick="datadel(0)">删除</button>
						</div>
						<!-- <div class="layui-form layui-col-md8" style="text-align:right;padding-bottom:10px">
							<div class="layui-inline layuiadmin-input-useradmin">
								<label class="layui-form-label" style="width:60px">分类名称</label>
								<div class="layui-input-block" style="width:120px;margin-left:90px">
									<input type="text" name="name" autocomplete="off" class="layui-input" value="">
								</div>
							</div>
							<div class="layui-inline">
								<label class="layui-form-label" style="width:60px">状态</label>
								<div class="layui-input-block" style="width:120px;margin-left:90px;text-align:left">
									<select name="status">
										<option value="">全部</option>
										<option value="1">显示</option>
										<option value="0">隐藏</option>
									</select>
								</div>
							</div>
							<div class="layui-inline">
								<button class="layui-btn layuiadmin-btn-replys" lay-submit="" lay-filter="LAY-app-forumreply-search">
									<i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>
								</button>
							</div>
						</div> -->
						<div class="layui-col-md12">
							<table id="tabledata" lay-filter="tabledata"></table>
						</div>
          </div>
        </div>
    </div>
  </div>
	{include file="public/js"/}
	<script>
  var table = layui.table;
	var datawhere = {};
  //数据表
  var tableIns = table.render({
    elem: '#tabledata'
    ,url: "{$Request.url}" //数据接口
    ,page: true //开启分页
    ,cols: [[ //表头
			{type:"checkbox"},
      {field: 'id', title: 'ID',  width:80},
			{if getcustom('kecheng_discount') && $bid > 0}
      {field: 'pcname', title: '所属上级'},
			{/if}
      {field: 'name', title: '名称'},
      {field: 'pic', title: '图片',templet: function (d) {
          if(d.pic) {
              return '<div><img src="'+d.pic+'" style="height:50px"/></div>'
          }
					return '';
      }},
      {field: 'sort', title: '序号',width:80},
      //{field: 'createtime', title: '创建时间',templet:function(d){ return date('Y-m-d H:i',d.createtime)},width:150},
      {field: 'status', title: '状态',templet:function(d){ return d.status==1?'<span style="color:green">显示</span>':'<span style="color:red">隐藏</span>'},width:80},
      {field: 'operation', title: '操作',templet: function(d){
				var html = '';
			  html += '<button class="table-btn" onclick="showurl('+d.id+',\''+d.type+'\')">查看链接</button>';
				html += '<button class="table-btn" onclick="openmax(\'{:url('edit')}/id/'+d.id+'\')">编辑</button>';
				html += '<button class="table-btn" onclick="datadel('+d.id+')">删除</button>';
				if(d.pid==0){
					html += '<button class="table-btn" onclick="openmax(\'{:url('edit')}/pid/'+d.id+'\')">添加子分类</button>';
				}
				return html;
      },width:250}
    ]]
  });
	//排序
	table.on('sort(tabledata)', function(obj){
		datawhere.field = obj.field;
		datawhere.order = obj.type;
		tableIns.reload({
			initSort: obj,
			where: datawhere
		});
	});
	//检索
	layui.form.on('submit(LAY-app-forumreply-search)', function(obj){
		var field = obj.field
		var olddatawhere = datawhere
		datawhere = field
		datawhere.field = olddatawhere.field
		datawhere.order = olddatawhere.order
		tableIns.reload({
			where: datawhere,
			page: {curr: 1}
		});
	})
	//删除
	function datadel(id){
		var ids = [];
		if(id==0){
			var checkStatus = table.checkStatus('tabledata')
			var checkData = checkStatus.data; //得到选中的数据
			if(checkData.length === 0){
				 return layer.msg('请选择数据');
			}
			var ids = [];
			for(var i=0;i<checkData.length;i++){
				ids.push(checkData[i]['id']);
			}
		}else{
			ids.push(id)
		}
		layer.confirm('确定要删除吗?',{icon: 7, title:'操作确认'}, function(index){
			//do something
			layer.close(index);
			var index = layer.load();
			$.post("{:url('del')}",{ids:ids},function(data){
				layer.close(index);
				dialog(data.msg,data.status);
				tableIns.reload()
			})
		});
	}
	function showurl(id,type){
	   var pagepath = 'activity/kecheng/list?cid='+id;
		{if !in_array('mp',$platform)}
		showwxqrcode(pagepath);
		return;
		{/if}
		url = '{$Think.const.PRE_URL2}/h5/{$aid}.html#'+pagepath;
		var html = '';
		html+='<div style="margin:20px">';
		html+='	<div style="width:100%;margin:10px 0" id="urlqr"></div>'; 
		{if in_array('wx',$platform)}
		html+='	<div style="width:100%;text-align:center"><button class="layui-btn layui-btn-sm layui-btn-primary" onclick="showwxqrcode(\''+pagepath+'\')">查看小程序码</button></div>'; 
		{/if}
		html+='	<div style="height:50px;line-height:25px;">链接地址：'+url+'</div>';
		{if in_array('wx',$platform)}
		html+='	<div style="height:50px;line-height:25px;">页面路径：<button class="layui-btn layui-btn-xs layui-btn-primary" onclick="copyText(\'/'+pagepath+'\')">复制</button><br>/'+pagepath+'</div>';
		{/if}
		html+='</div>'
		layer.open({type:1,'title':'链接地址',area:['500px','430px'],shadeClose:true,'content':html})
		var qrcode = new QRCode('urlqr', {
			text: 'your content',
			width: 200,
			height: 200,
			colorDark : '#000000',
			colorLight : '#ffffff',
			correctLevel : QRCode.CorrectLevel.L
		});
		qrcode.clear();
		qrcode.makeCode(url);
	}
	function showwxqrcode(pagepath){
		var index = layer.load();
		$.post("{:url('DesignerPage/getwxqrcode')}",{path:pagepath},function(res){
			layer.close(index);
			if(res.status==0){
				//dialog(res.msg);
				layer.open({type:1,area:['300px','350px'],content:'<div style="margin:auto auto;text-align:center"><div style="color:red;width:280px;height:180px;margin-top:100px">'+res.msg+'</div><div style="height:25px;line-height:25px;">'+pagepath+'</div></div>',title:false,shadeClose:true})
			}else{
				layer.open({type:1,area:['300px','350px'],content:'<div style="margin:auto auto;text-align:center"><img src="'+res.url+'" style="margin-top:20px;max-width:280px;max-height:280px"/><div style="height:25px;line-height:25px;">'+pagepath+'</div></div>',title:false,shadeClose:true})
			}
		})
	}
	function copyText(text) {
			var textarea = document.createElement("textarea"); //创建input对象
			var currentFocus = document.activeElement; //当前获得焦点的元素
			var toolBoxwrap = document.getElementById('NewsToolBox'); //将文本框插入到NewsToolBox这个之后
			toolBoxwrap.appendChild(textarea); //添加元素
			textarea.value = text;
			textarea.focus();
			if (textarea.setSelectionRange) {
					textarea.setSelectionRange(0, textarea.value.length); //获取光标起始位置到结束位置
			} else {
					textarea.select();
			}
			try {
					var flag = document.execCommand("copy"); //执行复制
			} catch (eo) {
					var flag = false;
			}
			toolBoxwrap.removeChild(textarea); //删除元素
			currentFocus.focus();
			if(flag) layer.msg('复制成功');
			return flag;
	}
	</script>
	{include file="public/copyright"/}
	<div id="NewsToolBox"></div>
</body>
</html>