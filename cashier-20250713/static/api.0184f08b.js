import{m as e,E as t}from"./index.1e5dca8d.js";var r,n=function(e,t){return function(){for(var r=new Array(arguments.length),n=0;n<r.length;n++)r[n]=arguments[n];return e.apply(t,r)}},o=Object.prototype.toString,a=(r=Object.create(null),function(e){var t=o.call(e);return r[t]||(r[t]=t.slice(8,-1).toLowerCase())});function s(e){return e=e.toLowerCase(),function(t){return a(t)===e}}function i(e){return Array.isArray(e)}function u(e){return void 0===e}var c=s("ArrayBuffer");function f(e){return null!==e&&"object"==typeof e}function d(e){if("object"!==a(e))return!1;var t=Object.getPrototypeOf(e);return null===t||t===Object.prototype}var l=s("Date"),h=s("File"),p=s("Blob"),m=s("FileList");function g(e){return"[object Function]"===o.call(e)}var v=s("URLSearchParams");function y(e,t){if(null!=e)if("object"!=typeof e&&(e=[e]),i(e))for(var r=0,n=e.length;r<n;r++)t.call(null,e[r],r,e);else for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.call(null,e[o],o,e)}var R,E=(R="undefined"!=typeof Uint8Array&&Object.getPrototypeOf(Uint8Array),function(e){return R&&e instanceof R}),b={isArray:i,isArrayBuffer:c,isBuffer:function(e){return null!==e&&!u(e)&&null!==e.constructor&&!u(e.constructor)&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)},isFormData:function(e){var t="[object FormData]";return e&&("function"==typeof FormData&&e instanceof FormData||o.call(e)===t||g(e.toString)&&e.toString()===t)},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&c(e.buffer)},isString:function(e){return"string"==typeof e},isNumber:function(e){return"number"==typeof e},isObject:f,isPlainObject:d,isUndefined:u,isDate:l,isFile:h,isBlob:p,isFunction:g,isStream:function(e){return f(e)&&g(e.pipe)},isURLSearchParams:v,isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product&&"NativeScript"!==navigator.product&&"NS"!==navigator.product)&&("undefined"!=typeof window&&"undefined"!=typeof document)},forEach:y,merge:function e(){var t={};function r(r,n){d(t[n])&&d(r)?t[n]=e(t[n],r):d(r)?t[n]=e({},r):i(r)?t[n]=r.slice():t[n]=r}for(var n=0,o=arguments.length;n<o;n++)y(arguments[n],r);return t},extend:function(e,t,r){return y(t,(function(t,o){e[o]=r&&"function"==typeof t?n(t,r):t})),e},trim:function(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")},stripBOM:function(e){return 65279===e.charCodeAt(0)&&(e=e.slice(1)),e},inherits:function(e,t,r,n){e.prototype=Object.create(t.prototype,n),e.prototype.constructor=e,r&&Object.assign(e.prototype,r)},toFlatObject:function(e,t,r){var n,o,a,s={};t=t||{};do{for(o=(n=Object.getOwnPropertyNames(e)).length;o-- >0;)s[a=n[o]]||(t[a]=e[a],s[a]=!0);e=Object.getPrototypeOf(e)}while(e&&(!r||r(e,t))&&e!==Object.prototype);return t},kindOf:a,kindOfTest:s,endsWith:function(e,t,r){e=String(e),(void 0===r||r>e.length)&&(r=e.length),r-=t.length;var n=e.indexOf(t,r);return-1!==n&&n===r},toArray:function(e){if(!e)return null;var t=e.length;if(u(t))return null;for(var r=new Array(t);t-- >0;)r[t]=e[t];return r},isTypedArray:E,isFileList:m};function O(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}var C=function(e,t,r){if(!t)return e;var n;if(r)n=r(t);else if(b.isURLSearchParams(t))n=t.toString();else{var o=[];b.forEach(t,(function(e,t){null!=e&&(b.isArray(e)?t+="[]":e=[e],b.forEach(e,(function(e){b.isDate(e)?e=e.toISOString():b.isObject(e)&&(e=JSON.stringify(e)),o.push(O(t)+"="+O(e))})))})),n=o.join("&")}if(n){var a=e.indexOf("#");-1!==a&&(e=e.slice(0,a)),e+=(-1===e.indexOf("?")?"?":"&")+n}return e};function w(){this.handlers=[]}w.prototype.use=function(e,t,r){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!r&&r.synchronous,runWhen:r?r.runWhen:null}),this.handlers.length-1},w.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},w.prototype.forEach=function(e){b.forEach(this.handlers,(function(t){null!==t&&e(t)}))};var S=w,A=function(e,t){b.forEach(e,(function(r,n){n!==t&&n.toUpperCase()===t.toUpperCase()&&(e[t]=r,delete e[n])}))};function j(e,t,r,n,o){Error.call(this),this.message=e,this.name="AxiosError",t&&(this.code=t),r&&(this.config=r),n&&(this.request=n),o&&(this.response=o)}b.inherits(j,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:this.config,code:this.code,status:this.response&&this.response.status?this.response.status:null}}});var T=j.prototype,N={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED"].forEach((function(e){N[e]={value:e}})),Object.defineProperties(j,N),Object.defineProperty(T,"isAxiosError",{value:!0}),j.from=function(e,t,r,n,o,a){var s=Object.create(T);return b.toFlatObject(e,s,(function(e){return e!==Error.prototype})),j.call(s,e.message,t,r,n,o),s.name=e.name,a&&Object.assign(s,a),s};var L=j,_={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1};var x=function(e,t){t=t||new FormData;var r=[];function n(e){return null===e?"":b.isDate(e)?e.toISOString():b.isArrayBuffer(e)||b.isTypedArray(e)?"function"==typeof Blob?new Blob([e]):Buffer.from(e):e}return function e(o,a){if(b.isPlainObject(o)||b.isArray(o)){if(-1!==r.indexOf(o))throw Error("Circular reference detected in "+a);r.push(o),b.forEach(o,(function(r,o){if(!b.isUndefined(r)){var s,i=a?a+"."+o:o;if(r&&!a&&"object"==typeof r)if(b.endsWith(o,"{}"))r=JSON.stringify(r);else if(b.endsWith(o,"[]")&&(s=b.toArray(r)))return void s.forEach((function(e){!b.isUndefined(e)&&t.append(i,n(e))}));e(r,i)}})),r.pop()}else t.append(a,n(o))}(e),t},P=b.isStandardBrowserEnv()?{write:function(e,t,r,n,o,a){var s=[];s.push(e+"="+encodeURIComponent(t)),b.isNumber(r)&&s.push("expires="+new Date(r).toGMTString()),b.isString(n)&&s.push("path="+n),b.isString(o)&&s.push("domain="+o),!0===a&&s.push("secure"),document.cookie=s.join("; ")},read:function(e){var t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove:function(e){this.write(e,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}},D=function(e,t){return e&&!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t)?function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}(e,t):t},U=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"],B=b.isStandardBrowserEnv()?function(){var e,t=/(msie|trident)/i.test(navigator.userAgent),r=document.createElement("a");function n(e){var n=e;return t&&(r.setAttribute("href",n),n=r.href),r.setAttribute("href",n),{href:r.href,protocol:r.protocol?r.protocol.replace(/:$/,""):"",host:r.host,search:r.search?r.search.replace(/^\?/,""):"",hash:r.hash?r.hash.replace(/^#/,""):"",hostname:r.hostname,port:r.port,pathname:"/"===r.pathname.charAt(0)?r.pathname:"/"+r.pathname}}return e=n(window.location.href),function(t){var r=b.isString(t)?n(t):t;return r.protocol===e.protocol&&r.host===e.host}}():function(){return!0};function k(e){L.call(this,null==e?"canceled":e,L.ERR_CANCELED),this.name="CanceledError"}b.inherits(k,L,{__CANCEL__:!0});var I=k,F=function(e){return new Promise((function(t,r){var n,o=e.data,a=e.headers,s=e.responseType;function i(){e.cancelToken&&e.cancelToken.unsubscribe(n),e.signal&&e.signal.removeEventListener("abort",n)}b.isFormData(o)&&b.isStandardBrowserEnv()&&delete a["Content-Type"];var u=new XMLHttpRequest;if(e.auth){var c=e.auth.username||"",f=e.auth.password?unescape(encodeURIComponent(e.auth.password)):"";a.Authorization="Basic "+btoa(c+":"+f)}var d=D(e.baseURL,e.url);function l(){if(u){var n,o,a,c,f,d="getAllResponseHeaders"in u?(n=u.getAllResponseHeaders(),f={},n?(b.forEach(n.split("\n"),(function(e){if(c=e.indexOf(":"),o=b.trim(e.substr(0,c)).toLowerCase(),a=b.trim(e.substr(c+1)),o){if(f[o]&&U.indexOf(o)>=0)return;f[o]="set-cookie"===o?(f[o]?f[o]:[]).concat([a]):f[o]?f[o]+", "+a:a}})),f):f):null;!function(e,t,r){var n=r.config.validateStatus;r.status&&n&&!n(r.status)?t(new L("Request failed with status code "+r.status,[L.ERR_BAD_REQUEST,L.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r)):e(r)}((function(e){t(e),i()}),(function(e){r(e),i()}),{data:s&&"text"!==s&&"json"!==s?u.response:u.responseText,status:u.status,statusText:u.statusText,headers:d,config:e,request:u}),u=null}}if(u.open(e.method.toUpperCase(),C(d,e.params,e.paramsSerializer),!0),u.timeout=e.timeout,"onloadend"in u?u.onloadend=l:u.onreadystatechange=function(){u&&4===u.readyState&&(0!==u.status||u.responseURL&&0===u.responseURL.indexOf("file:"))&&setTimeout(l)},u.onabort=function(){u&&(r(new L("Request aborted",L.ECONNABORTED,e,u)),u=null)},u.onerror=function(){r(new L("Network Error",L.ERR_NETWORK,e,u,u)),u=null},u.ontimeout=function(){var t=e.timeout?"timeout of "+e.timeout+"ms exceeded":"timeout exceeded",n=e.transitional||_;e.timeoutErrorMessage&&(t=e.timeoutErrorMessage),r(new L(t,n.clarifyTimeoutError?L.ETIMEDOUT:L.ECONNABORTED,e,u)),u=null},b.isStandardBrowserEnv()){var h=(e.withCredentials||B(d))&&e.xsrfCookieName?P.read(e.xsrfCookieName):void 0;h&&(a[e.xsrfHeaderName]=h)}"setRequestHeader"in u&&b.forEach(a,(function(e,t){void 0===o&&"content-type"===t.toLowerCase()?delete a[t]:u.setRequestHeader(t,e)})),b.isUndefined(e.withCredentials)||(u.withCredentials=!!e.withCredentials),s&&"json"!==s&&(u.responseType=e.responseType),"function"==typeof e.onDownloadProgress&&u.addEventListener("progress",e.onDownloadProgress),"function"==typeof e.onUploadProgress&&u.upload&&u.upload.addEventListener("progress",e.onUploadProgress),(e.cancelToken||e.signal)&&(n=function(e){u&&(r(!e||e&&e.type?new I:e),u.abort(),u=null)},e.cancelToken&&e.cancelToken.subscribe(n),e.signal&&(e.signal.aborted?n():e.signal.addEventListener("abort",n))),o||(o=null);var p,m=(p=/^([-+\w]{1,25})(:?\/\/|:)/.exec(d))&&p[1]||"";m&&-1===["http","https","file"].indexOf(m)?r(new L("Unsupported protocol "+m+":",L.ERR_BAD_REQUEST,e)):u.send(o)}))},q={"Content-Type":"application/x-www-form-urlencoded"};function M(e,t){!b.isUndefined(e)&&b.isUndefined(e["Content-Type"])&&(e["Content-Type"]=t)}var J,H={transitional:_,adapter:(("undefined"!=typeof XMLHttpRequest||"undefined"!=typeof process&&"[object process]"===Object.prototype.toString.call(process))&&(J=F),J),transformRequest:[function(e,t){if(A(t,"Accept"),A(t,"Content-Type"),b.isFormData(e)||b.isArrayBuffer(e)||b.isBuffer(e)||b.isStream(e)||b.isFile(e)||b.isBlob(e))return e;if(b.isArrayBufferView(e))return e.buffer;if(b.isURLSearchParams(e))return M(t,"application/x-www-form-urlencoded;charset=utf-8"),e.toString();var r,n=b.isObject(e),o=t&&t["Content-Type"];if((r=b.isFileList(e))||n&&"multipart/form-data"===o){var a=this.env&&this.env.FormData;return x(r?{"files[]":e}:e,a&&new a)}return n||"application/json"===o?(M(t,"application/json"),function(e,t,r){if(b.isString(e))try{return(t||JSON.parse)(e),b.trim(e)}catch(e){if("SyntaxError"!==e.name)throw e}return(r||JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){var t=this.transitional||H.transitional,r=t&&t.silentJSONParsing,n=t&&t.forcedJSONParsing,o=!r&&"json"===this.responseType;if(o||n&&b.isString(e)&&e.length)try{return JSON.parse(e)}catch(e){if(o){if("SyntaxError"===e.name)throw L.from(e,L.ERR_BAD_RESPONSE,this,null,this.response);throw e}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:null},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*"}}};b.forEach(["delete","get","head"],(function(e){H.headers[e]={}})),b.forEach(["post","put","patch"],(function(e){H.headers[e]=b.merge(q)}));var W=H,z=function(e,t,r){var n=this||W;return b.forEach(r,(function(r){e=r.call(n,e,t)})),e},$=function(e){return!(!e||!e.__CANCEL__)};function V(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new I}var X=function(e){return V(e),e.headers=e.headers||{},e.data=z.call(e,e.data,e.headers,e.transformRequest),e.headers=b.merge(e.headers.common||{},e.headers[e.method]||{},e.headers),b.forEach(["delete","get","head","post","put","patch","common"],(function(t){delete e.headers[t]})),(e.adapter||W.adapter)(e).then((function(t){return V(e),t.data=z.call(e,t.data,t.headers,e.transformResponse),t}),(function(t){return $(t)||(V(e),t&&t.response&&(t.response.data=z.call(e,t.response.data,t.response.headers,e.transformResponse))),Promise.reject(t)}))},K=function(e,t){t=t||{};var r={};function n(e,t){return b.isPlainObject(e)&&b.isPlainObject(t)?b.merge(e,t):b.isPlainObject(t)?b.merge({},t):b.isArray(t)?t.slice():t}function o(r){return b.isUndefined(t[r])?b.isUndefined(e[r])?void 0:n(void 0,e[r]):n(e[r],t[r])}function a(e){if(!b.isUndefined(t[e]))return n(void 0,t[e])}function s(r){return b.isUndefined(t[r])?b.isUndefined(e[r])?void 0:n(void 0,e[r]):n(void 0,t[r])}function i(r){return r in t?n(e[r],t[r]):r in e?n(void 0,e[r]):void 0}var u={url:a,method:a,data:a,baseURL:s,transformRequest:s,transformResponse:s,paramsSerializer:s,timeout:s,timeoutMessage:s,withCredentials:s,adapter:s,responseType:s,xsrfCookieName:s,xsrfHeaderName:s,onUploadProgress:s,onDownloadProgress:s,decompress:s,maxContentLength:s,maxBodyLength:s,beforeRedirect:s,transport:s,httpAgent:s,httpsAgent:s,cancelToken:s,socketPath:s,responseEncoding:s,validateStatus:i};return b.forEach(Object.keys(e).concat(Object.keys(t)),(function(e){var t=u[e]||o,n=t(e);b.isUndefined(n)&&t!==i||(r[e]=n)})),r},Q="0.27.2",G=Q,Y={};["object","boolean","number","function","string","symbol"].forEach((function(e,t){Y[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}}));var Z={};Y.transitional=function(e,t,r){function n(e,t){return"[Axios v"+G+"] Transitional option '"+e+"'"+t+(r?". "+r:"")}return function(r,o,a){if(!1===e)throw new L(n(o," has been removed"+(t?" in "+t:"")),L.ERR_DEPRECATED);return t&&!Z[o]&&(Z[o]=!0,console.warn(n(o," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(r,o,a)}};var ee={assertOptions:function(e,t,r){if("object"!=typeof e)throw new L("options must be an object",L.ERR_BAD_OPTION_VALUE);for(var n=Object.keys(e),o=n.length;o-- >0;){var a=n[o],s=t[a];if(s){var i=e[a],u=void 0===i||s(i,a,e);if(!0!==u)throw new L("option "+a+" must be "+u,L.ERR_BAD_OPTION_VALUE)}else if(!0!==r)throw new L("Unknown option "+a,L.ERR_BAD_OPTION)}},validators:Y},te=ee.validators;function re(e){this.defaults=e,this.interceptors={request:new S,response:new S}}re.prototype.request=function(e,t){"string"==typeof e?(t=t||{}).url=e:t=e||{},(t=K(this.defaults,t)).method?t.method=t.method.toLowerCase():this.defaults.method?t.method=this.defaults.method.toLowerCase():t.method="get";var r=t.transitional;void 0!==r&&ee.assertOptions(r,{silentJSONParsing:te.transitional(te.boolean),forcedJSONParsing:te.transitional(te.boolean),clarifyTimeoutError:te.transitional(te.boolean)},!1);var n=[],o=!0;this.interceptors.request.forEach((function(e){"function"==typeof e.runWhen&&!1===e.runWhen(t)||(o=o&&e.synchronous,n.unshift(e.fulfilled,e.rejected))}));var a,s=[];if(this.interceptors.response.forEach((function(e){s.push(e.fulfilled,e.rejected)})),!o){var i=[X,void 0];for(Array.prototype.unshift.apply(i,n),i=i.concat(s),a=Promise.resolve(t);i.length;)a=a.then(i.shift(),i.shift());return a}for(var u=t;n.length;){var c=n.shift(),f=n.shift();try{u=c(u)}catch(e){f(e);break}}try{a=X(u)}catch(e){return Promise.reject(e)}for(;s.length;)a=a.then(s.shift(),s.shift());return a},re.prototype.getUri=function(e){e=K(this.defaults,e);var t=D(e.baseURL,e.url);return C(t,e.params,e.paramsSerializer)},b.forEach(["delete","get","head","options"],(function(e){re.prototype[e]=function(t,r){return this.request(K(r||{},{method:e,url:t,data:(r||{}).data}))}})),b.forEach(["post","put","patch"],(function(e){function t(t){return function(r,n,o){return this.request(K(o||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:r,data:n}))}}re.prototype[e]=t(),re.prototype[e+"Form"]=t(!0)}));var ne=re;function oe(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");var t;this.promise=new Promise((function(e){t=e}));var r=this;this.promise.then((function(e){if(r._listeners){var t,n=r._listeners.length;for(t=0;t<n;t++)r._listeners[t](e);r._listeners=null}})),this.promise.then=function(e){var t,n=new Promise((function(e){r.subscribe(e),t=e})).then(e);return n.cancel=function(){r.unsubscribe(t)},n},e((function(e){r.reason||(r.reason=new I(e),t(r.reason))}))}oe.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},oe.prototype.subscribe=function(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]},oe.prototype.unsubscribe=function(e){if(this._listeners){var t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}},oe.source=function(){var e;return{token:new oe((function(t){e=t})),cancel:e}};var ae=oe;var se=function e(t){var r=new ne(t),o=n(ne.prototype.request,r);return b.extend(o,ne.prototype,r),b.extend(o,r),o.create=function(r){return e(K(t,r))},o}(W);se.Axios=ne,se.CanceledError=I,se.CancelToken=ae,se.isCancel=$,se.VERSION=Q,se.toFormData=x,se.AxiosError=L,se.Cancel=se.CanceledError,se.all=function(e){return Promise.all(e)},se.spread=function(e){return function(t){return e.apply(null,t)}},se.isAxiosError=function(e){return b.isObject(e)&&!0===e.isAxiosError};var ie=se,ue=se;let ce;function fe(){ce=e.service({lock:!0,text:"Loading",background:"rgba(0, 0, 0, 0.7)"})}function de(){ce.close()}ie.default=ue;const le=ie.create({baseURL:"https://esu.top/",timeout:6e4,withCredentials:!0,headers:{"Content-Type":"application/json","X-Requested-With":"XMLHttpRequest"}});function he(e){return le({url:"/?s=/RestaurantCashier/getCashierOrder",method:"post",data:e})}function pe(e){return le({url:"/?s=/RestaurantCashier/addToCashier",method:"post",data:e})}function me(e){return le({url:"/?s=/RestaurantCashier/cashierChangeNum",method:"post",data:e})}function ge(e){return le({url:"/?s=/RestaurantCashier/cashierChangePrice",method:"post",data:e})}function ve(e){return le({url:"/?s=/RestaurantCashier/cancelHangup",method:"post",data:e})}function ye(e){return le({url:"/?s=/RestaurantCashier/delCashierOrder",method:"post",data:e})}function Re(e){return le({url:"/?s=/RestaurantCashier/cashierChangeRemark",method:"post",data:e})}function Ee(e){return le({url:"/?s=/RestaurantCashier/searchMember",method:"post",data:e})}function be(e){return le({url:"/?s=/RestaurantCashier/getWaitPayOrder",method:"post",data:e})}function Oe(e){return le({url:"/?s=/RestaurantCashier/memberCouponList",method:"post",data:e})}function Ce(e){return le({url:"/?s=/RestaurantCashier/print",method:"post",data:e})}function we(e){return le({url:"/?s=/RestaurantCashier/getCashierInfo",method:"post",data:e})}function Se(e){return le({url:"/?s=/RestaurantCashier/registerMember",method:"post",data:e})}function Ae(e){return le({url:"/?s=/RestaurantCashier/storeRecharge",method:"post",data:e})}function je(e){return le({url:"/?s=/RestaurantCashier/getStored",method:"post",data:e})}function Te(e){return le({url:"/?s=/RestaurantCashier/getStoredDetail&id="+e,method:"get"})}function Ne(e){return le({url:"/?s=/RestaurantCashierLogin/jiaoban",method:"post",data:e})}function Le(){return le({url:"/?s=/RestaurantCashierLogin/tablelist",method:"post"})}function _e(e){return le({url:"/?s=/RestaurantCashierLogin/clean",method:"post",data:{tableId:e}})}function xe(e){return le({url:"/?s=/RestaurantCashierLogin/cleanOver",method:"post",data:{tableId:e}})}function Pe(e){return le({url:"/?s=/RestaurantCashierLogin/change",method:"post",data:e})}function De(e){return le({url:"/?s=/RestaurantCashierLogin/createOrder",method:"post",data:e})}function Ue(e){return le({url:"/?s=/RestaurantCashierLogin/editOrder",method:"post",data:e})}function Be(e,t){return le({url:`/?s=/RestaurantCashierLogin/buyview&tableId=${e}&frompage=admin&prodata=${t}`,method:"get"})}function ke(e){return le({url:"/?s=/RestaurantCashierLogin/payconfirm",method:"post",data:e})}function Ie(e){return le({url:"/?s=/RestaurantCashierLogin/detail&id="+e,method:"get"})}function Fe(e,t){return le({url:`/?s=/RestaurantCashierLogin/chooselist&tableId=${e}&bid=${t}`,method:"get"})}function qe(e){return le({url:"/?s=/RestaurantCashierLogin/addcart",method:"post",data:e})}function Me(e){return le({url:"/?s=/RestaurantCashierLogin/cartdelete",method:"post",data:e})}function Je(e){return le({url:`/?s=/RestaurantCashierLogin/cartclear&tableId=${e}`,method:"get"})}function He(e){return le({url:"/?s=/RestaurantCashier/orderlist",method:"post",data:e})}le.interceptors.request.use((function(e){return e}),(function(e){return Promise.reject(e)})),le.interceptors.response.use((function(e){if("-1"==e.data.status){var r=sessionStorage.getItem("loginState");return r&&""!=r?void 0:(sessionStorage.setItem("loginState","1"),t.error(e.data.msg),void setTimeout((()=>{sessionStorage.setItem("loginState","")}),1e3))}return e.data}),(function(e){return e.response.status&&(de(),t.error(e.response.data.message)),Promise.reject(e)}));export{ye as A,he as B,ve as C,qe as D,Me as E,He as F,Re as G,Le as a,pe as b,de as c,Fe as d,ke as e,Je as f,we as g,je as h,Ue as i,Ne as j,De as k,Be as l,Oe as m,xe as n,fe as o,Ce as p,_e as q,Se as r,ge as s,Ie as t,be as u,Te as v,me as w,Ae as x,Ee as y,Pe as z};
